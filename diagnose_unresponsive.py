#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断系统未响应问题
"""

import time
import psutil
import os
import threading
from pathlib import Path

def check_system_resources():
    """检查系统资源"""
    print("💻 系统资源检查")
    print("="*50)
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"🔥 CPU使用率: {cpu_percent:.1f}%")
    
    # 内存使用
    memory = psutil.virtual_memory()
    print(f"🧠 内存使用: {memory.percent:.1f}% ({memory.used//1024//1024}MB / {memory.total//1024//1024}MB)")
    
    # 磁盘使用
    disk = psutil.disk_usage('.')
    print(f"💾 磁盘使用: {disk.percent:.1f}% ({disk.used//1024//1024//1024}GB / {disk.total//1024//1024//1024}GB)")
    
    # 检查是否有资源瓶颈
    if cpu_percent > 90:
        print("⚠️ CPU使用率过高，可能导致响应缓慢")
    if memory.percent > 90:
        print("⚠️ 内存使用率过高，可能导致系统卡顿")
    if disk.percent > 95:
        print("⚠️ 磁盘空间不足，可能影响系统性能")

def check_file_locks():
    """检查文件锁定情况"""
    print(f"\n📁 文件锁定检查")
    print("="*50)
    
    # 检查关键文件
    key_files = [
        "zodiac_cache.pkl",
        "strategy_config.yaml",
        "historical_data.csv"
    ]
    
    for filename in key_files:
        if os.path.exists(filename):
            try:
                # 尝试打开文件
                with open(filename, 'rb') as f:
                    f.read(1)
                print(f"✅ {filename}: 可访问")
            except Exception as e:
                print(f"❌ {filename}: 被锁定或损坏 - {e}")
        else:
            print(f"⚠️ {filename}: 文件不存在")

def test_core_components():
    """测试核心组件"""
    print(f"\n🧪 核心组件测试")
    print("="*50)
    
    # 测试预测引擎
    print("🎯 测试预测引擎...")
    try:
        from prediction_engine import PredictionEngine
        
        start_time = time.time()
        engine = PredictionEngine()
        init_time = time.time() - start_time
        
        if init_time > 10:
            print(f"⚠️ 预测引擎初始化缓慢: {init_time:.2f} 秒")
        else:
            print(f"✅ 预测引擎初始化正常: {init_time:.2f} 秒")
            
    except Exception as e:
        print(f"❌ 预测引擎初始化失败: {e}")
    
    # 测试策略解析器
    print("📋 测试策略解析器...")
    try:
        from dsl_strategy_parser import DSLStrategyParser
        
        start_time = time.time()
        parser = DSLStrategyParser()
        summary = parser.get_strategy_summary()
        parse_time = time.time() - start_time
        
        print(f"✅ 策略解析器正常: {parse_time:.3f} 秒, {summary['total_strategies']} 个策略")
        
    except Exception as e:
        print(f"❌ 策略解析器失败: {e}")
    
    # 测试生肖引擎
    print("🐲 测试生肖引擎...")
    try:
        from optimized_zodiac_engine import OptimizedZodiacEngine
        
        start_time = time.time()
        zodiac_engine = OptimizedZodiacEngine()
        zodiac_time = time.time() - start_time
        
        if zodiac_time > 15:
            print(f"⚠️ 生肖引擎初始化缓慢: {zodiac_time:.2f} 秒")
        else:
            print(f"✅ 生肖引擎正常: {zodiac_time:.2f} 秒, {len(zodiac_engine.all_groups):,} 个小组")
            
    except Exception as e:
        print(f"❌ 生肖引擎失败: {e}")

def test_gui_components():
    """测试GUI组件"""
    print(f"\n🖥️ GUI组件测试")
    print("="*50)
    
    try:
        import tkinter as tk
        
        # 测试基础Tkinter
        print("🔧 测试基础Tkinter...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试GUI创建
        print("🎨 测试GUI创建...")
        start_time = time.time()
        
        from gui_main import LotteryPredictionGUI
        gui = LotteryPredictionGUI(root)
        
        gui_time = time.time() - start_time
        
        if gui_time > 20:
            print(f"⚠️ GUI创建缓慢: {gui_time:.2f} 秒")
        else:
            print(f"✅ GUI创建正常: {gui_time:.2f} 秒")
        
        # 检查关键组件
        components = [
            'prediction_engine',
            'dsl_parser',
            'zodiac_analyzer',
            'reback_engine'
        ]
        
        for component in components:
            if hasattr(gui, component):
                print(f"✅ {component}: 存在")
            else:
                print(f"❌ {component}: 缺失")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_performance():
    """性能测试"""
    print(f"\n⚡ 性能测试")
    print("="*50)
    
    # 测试简单预测
    print("🎯 测试简单预测...")
    try:
        from prediction_engine import PredictionEngine
        
        engine = PredictionEngine()
        
        start_time = time.time()
        result = engine.run_prediction("2025199")
        predict_time = time.time() - start_time
        
        if predict_time > 30:
            print(f"⚠️ 预测速度缓慢: {predict_time:.2f} 秒")
        else:
            print(f"✅ 预测速度正常: {predict_time:.2f} 秒")
            
        if result:
            print(f"   推荐号码: {len(result.final_numbers)} 个")
            print(f"   置信度: {result.confidence_score:.2%}")
        
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")

def monitor_with_timeout(func, timeout=30):
    """带超时的监控执行"""
    result = {'completed': False, 'error': None}
    
    def target():
        try:
            func()
            result['completed'] = True
        except Exception as e:
            result['error'] = str(e)
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout)
    
    if thread.is_alive():
        print(f"⚠️ 操作超时 ({timeout}秒)，可能存在死循环或阻塞")
        return False
    elif result['error']:
        print(f"❌ 操作失败: {result['error']}")
        return False
    elif result['completed']:
        print(f"✅ 操作完成")
        return True
    else:
        print(f"⚠️ 操作状态未知")
        return False

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案建议")
    print("="*50)
    
    solutions = """
🔧 常见解决方案:

1. 🔄 重启应用:
   - 完全关闭程序
   - 等待几秒钟
   - 重新启动

2. 💾 清理缓存:
   - 删除 zodiac_cache.pkl 文件
   - 让系统重新生成缓存

3. 📊 检查数据文件:
   - 确保 historical_data.csv 完整
   - 检查文件是否被其他程序占用

4. 🧠 释放内存:
   - 关闭其他占用内存的程序
   - 重启计算机释放内存

5. ⚡ 优化性能:
   - 降低Z-Score阈值
   - 减少分析的小组数量
   - 使用更快的存储设备

6. 🔧 重新安装:
   - 备份配置文件
   - 重新下载程序文件
   - 恢复配置

⚠️ 紧急处理:
如果程序完全无响应，请：
1. 使用任务管理器强制结束进程
2. 删除缓存文件重新启动
3. 检查系统资源是否充足
"""
    
    print(solutions)

def main():
    """主诊断流程"""
    print("🔍 系统未响应问题诊断")
    print("="*60)
    
    # 系统资源检查
    check_system_resources()
    
    # 文件锁定检查
    check_file_locks()
    
    # 核心组件测试
    print(f"\n⏱️ 核心组件测试 (30秒超时)...")
    monitor_with_timeout(test_core_components, 30)
    
    # GUI组件测试
    print(f"\n⏱️ GUI组件测试 (30秒超时)...")
    monitor_with_timeout(test_gui_components, 30)
    
    # 性能测试
    print(f"\n⏱️ 性能测试 (30秒超时)...")
    monitor_with_timeout(test_performance, 30)
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n🎯 诊断完成!")

if __name__ == "__main__":
    main()
