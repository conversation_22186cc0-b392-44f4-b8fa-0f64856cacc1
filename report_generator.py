#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
生成预测报告、回测报告和分析报告
"""

import json
import csv
from typing import Dict, List, Any, Optional
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import pandas as pd
from dataclasses import asdict
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def generate_prediction_report(self, prediction_result: Any, 
                                 include_charts: bool = True) -> str:
        """生成预测报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(self.output_dir, f"prediction_report_{timestamp}.html")
        
        # 生成HTML报告
        html_content = self._create_prediction_html(prediction_result, include_charts)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"预测报告已生成: {report_file}")
        return report_file
    
    def _create_prediction_html(self, result: Any, include_charts: bool = True) -> str:
        """创建预测HTML报告"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六合彩预测报告 - {result.target_period}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .title {{ color: #007bff; font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
        .subtitle {{ color: #666; font-size: 16px; }}
        .section {{ margin: 30px 0; }}
        .section-title {{ color: #333; font-size: 20px; font-weight: bold; border-left: 4px solid #007bff; padding-left: 15px; margin-bottom: 15px; }}
        .numbers-box {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; }}
        .numbers {{ font-size: 24px; font-weight: bold; letter-spacing: 3px; }}
        .confidence {{ font-size: 18px; margin-top: 10px; }}
        .info-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
        .info-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; }}
        .info-label {{ font-weight: bold; color: #333; }}
        .info-value {{ color: #666; margin-top: 5px; }}
        .strategy-list {{ background: #f8f9fa; padding: 15px; border-radius: 8px; }}
        .strategy-item {{ margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border-left: 3px solid #ffc107; }}
        .model-list {{ background: #e3f2fd; padding: 15px; border-radius: 8px; }}
        .model-item {{ margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border-left: 3px solid #2196f3; }}
        .footer {{ text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">六合彩智能预测报告</div>
            <div class="subtitle">基于多维策略融合与机器学习算法</div>
        </div>
        
        <div class="section">
            <div class="section-title">🎯 预测结果</div>
            <div class="numbers-box">
                <div class="numbers">{' '.join(f'{num:02d}' for num in result.final_numbers)}</div>
                <div class="confidence">置信度: {result.confidence_score:.2%}</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">📊 预测信息</div>
            <div class="info-grid">
                <div class="info-card">
                    <div class="info-label">目标期号</div>
                    <div class="info-value">{result.target_period}</div>
                </div>
                <div class="info-card">
                    <div class="info-label">预测时间</div>
                    <div class="info-value">{result.prediction_date}</div>
                </div>
                <div class="info-card">
                    <div class="info-label">融合方法</div>
                    <div class="info-value">{result.fusion_method}</div>
                </div>
                <div class="info-card">
                    <div class="info-label">执行时间</div>
                    <div class="info-value">{result.execution_time:.2f} 秒</div>
                </div>
                <div class="info-card">
                    <div class="info-label">使用策略数</div>
                    <div class="info-value">{result.total_strategies_used} 个</div>
                </div>
                <div class="info-card">
                    <div class="info-label">使用模型数</div>
                    <div class="info-value">{result.total_models_used} 个</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">🔮 传统策略分析</div>
            <div class="strategy-list">
        """
        
        # 添加策略详情
        for strategy in result.strategy_details:
            html += f"""
                <div class="strategy-item">
                    <strong>{strategy.get('strategy_name', 'Unknown')}</strong>
                    <br>推荐号码: {', '.join(f'{num:02d}' for num in strategy.get('predicted_numbers', []))}
                    <br>置信度: {strategy.get('confidence', 0):.4f} | 命中率: {strategy.get('hit_rate', 0):.2%}
                </div>
            """
        
        html += """
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">🤖 机器学习模型分析</div>
            <div class="model-list">
        """
        
        # 添加模型详情
        for model in result.model_details:
            html += f"""
                <div class="model-item">
                    <strong>{model.get('model_name', 'Unknown').replace('_', ' ').title()}</strong>
                    <br>推荐号码: {', '.join(f'{num:02d}' for num in model.get('predicted_numbers', []))}
                    <br>置信度: {model.get('confidence', 0):.4f}
                </div>
            """
        
        html += f"""
            </div>
        </div>
        
        <div class="warning">
            <strong>⚠️ 风险提示:</strong> 本预测结果仅供参考，不构成投资建议。彩票具有随机性，请理性参与，量力而行。
        </div>
        
        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>六合彩智能预测系统 v1.0 | Powered by AI & Machine Learning</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def generate_backtest_report(self, backtest_results: Dict[str, Any]) -> str:
        """生成回测报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = os.path.join(self.output_dir, f"backtest_report_{timestamp}.html")
        
        html_content = self._create_backtest_html(backtest_results)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"回测报告已生成: {report_file}")
        return report_file
    
    def _create_backtest_html(self, results: Dict[str, Any]) -> str:
        """创建回测HTML报告"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测分析报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 3px solid #dc3545; padding-bottom: 20px; margin-bottom: 30px; }}
        .title {{ color: #dc3545; font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
        .section {{ margin: 30px 0; }}
        .section-title {{ color: #333; font-size: 20px; font-weight: bold; border-left: 4px solid #dc3545; padding-left: 15px; margin-bottom: 15px; }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-top: 4px solid #dc3545; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #dc3545; }}
        .metric-label {{ color: #666; margin-top: 5px; }}
        .details-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .details-table th, .details-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        .details-table th {{ background-color: #f8f9fa; font-weight: bold; }}
        .hit {{ background-color: #d4edda; color: #155724; }}
        .miss {{ background-color: #f8d7da; color: #721c24; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">策略回测分析报告</div>
        </div>
        
        <div class="section">
            <div class="section-title">📈 核心指标</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{results.get('hit_rate', 0):.2%}</div>
                    <div class="metric-label">命中率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('hit_count', 0)}</div>
                    <div class="metric-label">命中次数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('total_periods', 0)}</div>
                    <div class="metric-label">总期数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('avg_miss_interval', 0):.1f}</div>
                    <div class="metric-label">平均遗漏</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('max_miss_streak', 0)}</div>
                    <div class="metric-label">最大遗漏</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.get('sharpe_ratio', 0):.3f}</div>
                    <div class="metric-label">夏普比率</div>
                </div>
            </div>
        </div>
        """
        
        # 添加详细结果表格
        if 'details' in results and results['details']:
            html += """
        <div class="section">
            <div class="section-title">📋 详细结果 (前10期)</div>
            <table class="details-table">
                <thead>
                    <tr>
                        <th>期号</th>
                        <th>预测号码</th>
                        <th>实际号码</th>
                        <th>结果</th>
                        <th>命中排名</th>
                    </tr>
                </thead>
                <tbody>
            """
            
            for detail in results['details'][:10]:
                result_class = 'hit' if detail.get('is_hit') else 'miss'
                result_text = '命中' if detail.get('is_hit') else '未中'
                hit_rank = detail.get('hit_rank', '-')
                
                predicted_str = ', '.join(f"{num:02d}" for num in detail.get('predicted', []))
                
                html += f"""
                    <tr>
                        <td>{detail.get('period', '')}</td>
                        <td>{predicted_str}</td>
                        <td>{detail.get('actual', '')}</td>
                        <td class="{result_class}">{result_text}</td>
                        <td>{hit_rank}</td>
                    </tr>
                """
            
            html += """
                </tbody>
            </table>
        </div>
            """
        
        html += f"""
        <div class="footer" style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def generate_charts(self, data: Dict[str, Any], chart_type: str = 'prediction') -> str:
        """生成图表"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if chart_type == 'prediction':
            return self._create_prediction_charts(data, timestamp)
        elif chart_type == 'backtest':
            return self._create_backtest_charts(data, timestamp)
        else:
            raise ValueError(f"不支持的图表类型: {chart_type}")
    
    def _create_prediction_charts(self, data: Dict[str, Any], timestamp: str) -> str:
        """创建预测相关图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('预测分析图表', fontsize=16, fontweight='bold')
        
        # 图1: 号码分布
        if 'final_numbers' in data:
            numbers = data['final_numbers']
            ax1.bar(range(len(numbers)), numbers, color='skyblue', alpha=0.7)
            ax1.set_title('推荐号码分布')
            ax1.set_xlabel('排序')
            ax1.set_ylabel('号码')
            ax1.grid(True, alpha=0.3)
        
        # 图2: 策略贡献度
        if 'strategy_details' in data:
            strategies = data['strategy_details']
            if strategies:
                names = [s.get('strategy_name', 'Unknown')[:10] for s in strategies]
                confidences = [s.get('confidence', 0) for s in strategies]
                ax2.pie(confidences, labels=names, autopct='%1.1f%%', startangle=90)
                ax2.set_title('策略贡献度')
        
        # 图3: 置信度分布
        if 'model_details' in data:
            models = data['model_details']
            if models:
                model_names = [m.get('model_name', 'Unknown') for m in models]
                model_confidences = [m.get('confidence', 0) for m in models]
                ax3.barh(model_names, model_confidences, color='lightgreen', alpha=0.7)
                ax3.set_title('模型置信度')
                ax3.set_xlabel('置信度')
        
        # 图4: 号码热力图
        if 'final_numbers' in data:
            numbers = data['final_numbers']
            # 创建7x7的网格来显示1-49的号码
            heatmap_data = np.zeros((7, 7))
            for i, num in enumerate(numbers):
                if num <= 49:
                    row, col = divmod(num - 1, 7)
                    heatmap_data[row, col] = i + 1  # 排名
            
            sns.heatmap(heatmap_data, annot=True, fmt='.0f', cmap='YlOrRd', 
                       ax=ax4, cbar_kws={'label': '推荐排名'})
            ax4.set_title('号码热力图 (1-49)')
        
        plt.tight_layout()
        
        chart_file = os.path.join(self.output_dir, f"prediction_charts_{timestamp}.png")
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"预测图表已生成: {chart_file}")
        return chart_file
    
    def _create_backtest_charts(self, data: Dict[str, Any], timestamp: str) -> str:
        """创建回测相关图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('回测分析图表', fontsize=16, fontweight='bold')
        
        # 图1: 命中率趋势
        if 'details' in data:
            details = data['details']
            periods = [d.get('period', '') for d in details]
            hits = [1 if d.get('is_hit') else 0 for d in details]
            
            # 计算累积命中率
            cumulative_hits = np.cumsum(hits)
            cumulative_rate = cumulative_hits / np.arange(1, len(hits) + 1)
            
            ax1.plot(range(len(cumulative_rate)), cumulative_rate, 'b-', linewidth=2)
            ax1.set_title('累积命中率趋势')
            ax1.set_xlabel('期数')
            ax1.set_ylabel('命中率')
            ax1.grid(True, alpha=0.3)
        
        # 图2: 命中分布
        hit_count = data.get('hit_count', 0)
        miss_count = data.get('total_periods', 0) - hit_count
        ax2.pie([hit_count, miss_count], labels=['命中', '未中'], 
               autopct='%1.1f%%', colors=['lightgreen', 'lightcoral'])
        ax2.set_title('命中分布')
        
        # 图3: 遗漏分布
        if 'details' in data:
            miss_counts = []
            current_miss = 0
            for detail in data['details']:
                if detail.get('is_hit'):
                    if current_miss > 0:
                        miss_counts.append(current_miss)
                    current_miss = 0
                else:
                    current_miss += 1
            
            if miss_counts:
                ax3.hist(miss_counts, bins=10, alpha=0.7, color='orange')
                ax3.set_title('遗漏期数分布')
                ax3.set_xlabel('遗漏期数')
                ax3.set_ylabel('频次')
        
        # 图4: 关键指标雷达图
        metrics = ['命中率', '平均遗漏', '最大遗漏', '夏普比率']
        values = [
            data.get('hit_rate', 0) * 100,
            100 - min(data.get('avg_miss_interval', 0) * 10, 100),  # 转换为正向指标
            100 - min(data.get('max_miss_streak', 0) * 5, 100),    # 转换为正向指标
            max(0, min(data.get('sharpe_ratio', 0) * 50 + 50, 100))  # 标准化
        ]
        
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False)
        values += values[:1]  # 闭合图形
        angles = np.concatenate((angles, [angles[0]]))
        
        ax4 = plt.subplot(224, projection='polar')
        ax4.plot(angles, values, 'o-', linewidth=2, color='blue')
        ax4.fill(angles, values, alpha=0.25, color='blue')
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(metrics)
        ax4.set_ylim(0, 100)
        ax4.set_title('综合指标雷达图')
        
        plt.tight_layout()
        
        chart_file = os.path.join(self.output_dir, f"backtest_charts_{timestamp}.png")
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"回测图表已生成: {chart_file}")
        return chart_file
    
    def export_excel_report(self, data: Dict[str, Any], report_type: str = 'prediction') -> str:
        """导出Excel报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = os.path.join(self.output_dir, f"{report_type}_report_{timestamp}.xlsx")
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            if report_type == 'prediction':
                # 预测结果表
                pred_df = pd.DataFrame({
                    '期号': [data.get('target_period', '')],
                    '预测号码': [', '.join(f'{num:02d}' for num in data.get('final_numbers', []))],
                    '置信度': [data.get('confidence_score', 0)],
                    '预测时间': [data.get('prediction_date', '')],
                    '执行时间(秒)': [data.get('execution_time', 0)]
                })
                pred_df.to_excel(writer, sheet_name='预测结果', index=False)
                
                # 策略详情表
                if data.get('strategy_details'):
                    strategy_df = pd.DataFrame(data['strategy_details'])
                    strategy_df.to_excel(writer, sheet_name='策略详情', index=False)
                
                # 模型详情表
                if data.get('model_details'):
                    model_df = pd.DataFrame(data['model_details'])
                    model_df.to_excel(writer, sheet_name='模型详情', index=False)
            
            elif report_type == 'backtest':
                # 回测摘要
                summary_df = pd.DataFrame({
                    '指标': ['命中率', '命中次数', '总期数', '平均遗漏', '最大遗漏', '夏普比率'],
                    '数值': [
                        f"{data.get('hit_rate', 0):.2%}",
                        data.get('hit_count', 0),
                        data.get('total_periods', 0),
                        f"{data.get('avg_miss_interval', 0):.2f}",
                        data.get('max_miss_streak', 0),
                        f"{data.get('sharpe_ratio', 0):.4f}"
                    ]
                })
                summary_df.to_excel(writer, sheet_name='回测摘要', index=False)
                
                # 详细结果
                if data.get('details'):
                    details_df = pd.DataFrame(data['details'])
                    details_df.to_excel(writer, sheet_name='详细结果', index=False)
        
        print(f"Excel报告已生成: {excel_file}")
        return excel_file

if __name__ == "__main__":
    # 测试报告生成器
    generator = ReportGenerator()
    
    # 模拟预测结果数据
    from dataclasses import dataclass
    
    @dataclass
    class MockPredictionResult:
        target_period: str = "2025199"
        prediction_date: str = "2025-07-18 10:30:00"
        final_numbers: List[int] = None
        confidence_score: float = 0.75
        strategy_details: List[Dict] = None
        model_details: List[Dict] = None
        fusion_method: str = "weighted_union"
        total_strategies_used: int = 2
        total_models_used: int = 3
        execution_time: float = 2.5
        metadata: Dict = None
        
        def __post_init__(self):
            if self.final_numbers is None:
                self.final_numbers = [3, 11, 14, 18, 25, 30, 31, 35, 38, 41, 43, 47]
            if self.strategy_details is None:
                self.strategy_details = [
                    {'strategy_name': '生肖极限策略', 'predicted_numbers': [3, 11, 25, 35], 'confidence': 0.8, 'hit_rate': 0.35},
                    {'strategy_name': '五行热门策略', 'predicted_numbers': [14, 18, 30, 41], 'confidence': 0.7, 'hit_rate': 0.42}
                ]
            if self.model_details is None:
                self.model_details = [
                    {'model_name': 'random_forest', 'predicted_numbers': [31, 38, 43, 47], 'confidence': 0.65},
                    {'model_name': 'gradient_boosting', 'predicted_numbers': [25, 30, 35, 41], 'confidence': 0.72}
                ]
            if self.metadata is None:
                self.metadata = {}
    
    # 生成测试报告
    mock_result = MockPredictionResult()
    report_file = generator.generate_prediction_report(mock_result)
    print(f"测试报告已生成: {report_file}")
