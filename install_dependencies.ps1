# 六合彩预测系统依赖安装脚本
Write-Host "正在安装六合彩预测系统依赖包..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# 检查Python环境
Write-Host "`n检查Python环境..." -ForegroundColor Yellow

$pythonCmd = $null
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        $pythonCmd = "python"
        Write-Host "找到Python: $pythonVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "python命令不可用，尝试py命令..." -ForegroundColor Yellow
}

if (-not $pythonCmd) {
    try {
        $pythonVersion = py --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            $pythonCmd = "py"
            Write-Host "找到Python: $pythonVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "py命令也不可用" -ForegroundColor Red
    }
}

if (-not $pythonCmd) {
    Write-Host "错误: 未找到Python环境" -ForegroundColor Red
    Write-Host "请确保Python已正确安装并添加到PATH环境变量中" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "`n使用Python命令: $pythonCmd" -ForegroundColor Cyan

# 升级pip
Write-Host "`n升级pip..." -ForegroundColor Yellow
& $pythonCmd -m pip install --upgrade pip

# 安装核心依赖
Write-Host "`n安装核心依赖包..." -ForegroundColor Yellow
$corePackages = @(
    "numpy>=1.21.0",
    "pandas>=1.3.0", 
    "scikit-learn>=1.0.0",
    "joblib>=1.0.0"
)

foreach ($package in $corePackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    & $pythonCmd -m pip install $package
}

# 安装配置和文件处理包
Write-Host "`n安装配置和文件处理包..." -ForegroundColor Yellow
$utilPackages = @(
    "PyYAML>=6.0",
    "openpyxl>=3.0.0",
    "xlsxwriter>=3.0.0",
    "python-dateutil>=2.8.0"
)

foreach ($package in $utilPackages) {
    Write-Host "安装 $package..." -ForegroundColor Cyan
    & $pythonCmd -m pip install $package
}

# 验证安装
Write-Host "`n验证安装..." -ForegroundColor Yellow
try {
    & $pythonCmd -c "import numpy, pandas, sklearn, yaml, openpyxl; print('所有依赖包安装成功!')"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ 依赖安装完成！" -ForegroundColor Green
        Write-Host "`n现在可以运行以下命令:" -ForegroundColor Cyan
        Write-Host "  $pythonCmd main.py                 # 运行基础预测" -ForegroundColor White
        Write-Host "  $pythonCmd gui_main.py             # 启动GUI界面" -ForegroundColor White  
        Write-Host "  $pythonCmd system_status_check.py  # 完整系统检查" -ForegroundColor White
        Write-Host "  $pythonCmd quick_status.py         # 快速状态检查" -ForegroundColor White
    } else {
        Write-Host "`n❌ 依赖验证失败" -ForegroundColor Red
    }
} catch {
    Write-Host "`n❌ 依赖验证失败: $_" -ForegroundColor Red
}

Write-Host "`n"
Read-Host "按Enter键退出"
