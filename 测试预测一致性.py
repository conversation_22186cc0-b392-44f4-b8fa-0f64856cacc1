#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测一致性 - 验证同一期号多次预测结果是否一致
"""

def test_prediction_consistency():
    """测试预测一致性"""
    print("🎯 测试预测一致性")
    print("=" * 60)
    
    try:
        print("1️⃣ 导入预测引擎...")
        from prediction_engine_adapter import PredictionEngineAdapter
        adapter = PredictionEngineAdapter()
        print("✅ 预测引擎导入成功")
        
        # 测试期号
        test_periods = ["2025210", "2025211", "2025212"]
        
        for period in test_periods:
            print(f"\n2️⃣ 测试期号 {period} 的一致性...")
            
            # 对同一期号进行多次预测
            predictions = []
            for i in range(5):
                print(f"   第 {i+1} 次预测...")
                result = adapter.run_prediction(period)
                predictions.append({
                    'numbers': result.final_numbers,
                    'confidence': result.confidence_score,
                    'strategies': result.total_strategies_used
                })
            
            # 检查一致性
            first_prediction = predictions[0]
            all_consistent = True
            
            for i, prediction in enumerate(predictions[1:], 2):
                if prediction['numbers'] != first_prediction['numbers']:
                    print(f"   ❌ 第 {i} 次预测号码不一致:")
                    print(f"      第1次: {first_prediction['numbers']}")
                    print(f"      第{i}次: {prediction['numbers']}")
                    all_consistent = False
                elif prediction['confidence'] != first_prediction['confidence']:
                    print(f"   ❌ 第 {i} 次预测置信度不一致:")
                    print(f"      第1次: {first_prediction['confidence']:.4f}")
                    print(f"      第{i}次: {prediction['confidence']:.4f}")
                    all_consistent = False
            
            if all_consistent:
                print(f"   ✅ 期号 {period} 预测结果完全一致")
                print(f"      推荐号码: {first_prediction['numbers']}")
                print(f"      置信度: {first_prediction['confidence']:.2%}")
                print(f"      策略数: {first_prediction['strategies']}")
            else:
                print(f"   ❌ 期号 {period} 预测结果不一致")
                return False
        
        print("\n3️⃣ 测试不同期号的差异性...")
        
        # 验证不同期号产生不同结果
        period_results = {}
        for period in test_periods:
            result = adapter.run_prediction(period)
            period_results[period] = result.final_numbers
        
        # 检查不同期号是否产生不同结果
        periods = list(period_results.keys())
        different_results = True
        
        for i in range(len(periods)):
            for j in range(i+1, len(periods)):
                period1, period2 = periods[i], periods[j]
                if period_results[period1] == period_results[period2]:
                    print(f"   ⚠️ 期号 {period1} 和 {period2} 产生了相同结果")
                    different_results = False
        
        if different_results:
            print("   ✅ 不同期号产生不同预测结果")
            for period, numbers in period_results.items():
                print(f"      {period}: {numbers}")
        else:
            print("   ⚠️ 部分不同期号产生了相同结果（可能正常）")
        
        print("\n🎉 预测一致性测试完成！")
        return True
        
    except Exception as e:
        print("❌ 测试过程中出现异常:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        
        import traceback
        print("\n详细错误:")
        traceback.print_exc()
        
        return False

def test_seed_mechanism():
    """测试种子机制"""
    print("\n🔧 测试种子机制")
    print("-" * 40)
    
    try:
        print("1️⃣ 测试期号种子生成...")
        
        import hashlib
        
        test_periods = ["2025210", "2025211", "2025210"]  # 注意第3个重复
        seeds = []
        
        for period in test_periods:
            seed = int(hashlib.md5(period.encode()).hexdigest()[:8], 16)
            seeds.append(seed)
            print(f"   期号 {period} -> 种子: {seed}")
        
        # 检查相同期号产生相同种子
        if seeds[0] == seeds[2]:
            print("   ✅ 相同期号产生相同种子")
        else:
            print("   ❌ 相同期号产生不同种子")
            return False
        
        # 检查不同期号产生不同种子
        if seeds[0] != seeds[1]:
            print("   ✅ 不同期号产生不同种子")
        else:
            print("   ❌ 不同期号产生相同种子")
            return False
        
        print("✅ 种子机制测试通过")
        return True
        
    except Exception as e:
        print("❌ 种子机制测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动预测一致性测试...")
    
    # 运行测试
    consistency_result = test_prediction_consistency()
    seed_result = test_seed_mechanism()
    
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    
    print("预测一致性测试:", "✅ 通过" if consistency_result else "❌ 失败")
    print("种子机制测试:", "✅ 通过" if seed_result else "❌ 失败")
    
    overall_success = consistency_result and seed_result
    
    print("\n🎯 总体结果:", "✅ 完全成功" if overall_success else "❌ 存在问题")
    
    if overall_success:
        print("\n🎉 恭喜！预测一致性问题已解决")
        print("✅ 同一期号多次预测结果完全一致")
        print("✅ 不同期号产生不同预测结果")
        print("✅ 基于期号的确定性种子机制正常工作")
        
        print("\n💡 现在的预测特点:")
        print("   - 同一期号：每次预测结果相同")
        print("   - 不同期号：产生不同的预测结果")
        print("   - 确定性：基于期号的数学种子")
        print("   - 多样性：不同期号有不同的分析参数")
        
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("请检查上述错误信息")
    
    return overall_success

if __name__ == "__main__":
    main()
