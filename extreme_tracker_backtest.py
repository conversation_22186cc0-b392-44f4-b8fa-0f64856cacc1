#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极限追踪和自动回测系统
与三层筛选系统对接，提供极限记录和回测分析
"""

import json
import sqlite3
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from collections import defaultdict
import numpy as np
import pandas as pd

@dataclass
class ExtremeRecord:
    """极限记录"""
    combination_id: str
    combination_type: str  # 'zodiac', 'color', 'wuxing', 'number_group'
    combination_value: str
    current_miss: int
    max_miss: int
    avg_miss: float
    last_hit_period: str
    extreme_ratio: float  # 当前遗漏/最大遗漏
    warning_level: str    # 'normal', 'warning', 'critical', 'extreme'

@dataclass
class BacktestResult:
    """回测结果"""
    strategy_name: str
    test_periods: int
    hit_count: int
    hit_rate: float
    avg_miss_interval: float
    max_miss_streak: int
    min_miss_streak: int
    roi: float
    sharpe_ratio: float
    stability_score: float
    period_details: List[Dict]

class ExtremeStatTracker:
    """极限统计追踪器"""
    
    def __init__(self, db_file: str = "extreme_stats.db"):
        self.db_file = db_file
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建极限记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extreme_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                combination_id TEXT UNIQUE,
                combination_type TEXT,
                combination_value TEXT,
                current_miss INTEGER DEFAULT 0,
                max_miss INTEGER DEFAULT 0,
                total_hits INTEGER DEFAULT 0,
                total_periods INTEGER DEFAULT 0,
                last_hit_period TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建历史命中记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hit_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period TEXT,
                winning_number INTEGER,
                combination_id TEXT,
                combination_type TEXT,
                hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建回测记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backtest_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT,
                test_date TEXT,
                test_periods INTEGER,
                hit_rate REAL,
                avg_miss_interval REAL,
                max_miss_streak INTEGER,
                roi REAL,
                sharpe_ratio REAL,
                stability_score REAL,
                details TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ 极限追踪数据库初始化完成")
    
    def update_period_result(self, period: str, winning_number: int):
        """更新期号结果"""
        print(f"📊 更新期号 {period} 结果: {winning_number}")
        
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 获取所有组合记录
        cursor.execute("SELECT * FROM extreme_records")
        records = cursor.fetchall()
        
        for record in records:
            combination_id = record[1]
            combination_type = record[2]
            combination_value = record[3]
            current_miss = record[4]
            
            # 检查是否命中
            is_hit = self._check_combination_hit(winning_number, combination_type, combination_value)
            
            if is_hit:
                # 命中：重置遗漏，更新统计
                cursor.execute('''
                    UPDATE extreme_records 
                    SET current_miss = 0, 
                        total_hits = total_hits + 1,
                        total_periods = total_periods + 1,
                        last_hit_period = ?,
                        updated_time = CURRENT_TIMESTAMP
                    WHERE combination_id = ?
                ''', (period, combination_id))
                
                # 记录命中历史
                cursor.execute('''
                    INSERT INTO hit_history (period, winning_number, combination_id, combination_type)
                    VALUES (?, ?, ?, ?)
                ''', (period, winning_number, combination_id, combination_type))
                
                print(f"   ✅ 组合 {combination_id} 命中")
            else:
                # 未命中：增加遗漏，更新最大遗漏
                new_miss = current_miss + 1
                cursor.execute('''
                    UPDATE extreme_records 
                    SET current_miss = ?,
                        max_miss = MAX(max_miss, ?),
                        total_periods = total_periods + 1,
                        updated_time = CURRENT_TIMESTAMP
                    WHERE combination_id = ?
                ''', (new_miss, new_miss, combination_id))
        
        conn.commit()
        conn.close()
    
    def _check_combination_hit(self, winning_number: int, combination_type: str, combination_value: str) -> bool:
        """检查组合是否命中"""
        if combination_type == 'zodiac':
            # 检查生肖命中
            zodiac_map = {i: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'][(i-1) % 12] for i in range(1, 50)}
            return zodiac_map.get(winning_number) == combination_value
        
        elif combination_type == 'color':
            # 检查波色命中（简化版）
            color_map = {i: ['红', '蓝', '绿'][i % 3] for i in range(1, 50)}
            return color_map.get(winning_number) == combination_value
        
        elif combination_type == 'wuxing':
            # 检查五行命中
            wuxing_map = {i: ['金', '木', '水', '火', '土'][(i-1) % 5] for i in range(1, 50)}
            return wuxing_map.get(winning_number) == combination_value
        
        elif combination_type == 'number_group':
            # 检查号码组命中
            try:
                group_numbers = json.loads(combination_value)
                return winning_number in group_numbers
            except:
                return False
        
        return False
    
    def get_extreme_status(self, combination_id: str) -> Dict:
        """获取组合的极限状态"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM extreme_records WHERE combination_id = ?", (combination_id,))
        record = cursor.fetchone()
        
        if not record:
            conn.close()
            return {}
        
        current_miss = record[4]
        max_miss = record[5]
        total_hits = record[6]
        total_periods = record[7]
        
        # 计算统计指标
        avg_miss = total_periods / max(total_hits, 1) if total_hits > 0 else total_periods
        extreme_ratio = current_miss / max(max_miss, 1) if max_miss > 0 else 0
        
        # 确定警告级别
        if extreme_ratio >= 0.9:
            warning_level = 'extreme'
        elif extreme_ratio >= 0.7:
            warning_level = 'critical'
        elif extreme_ratio >= 0.5:
            warning_level = 'warning'
        else:
            warning_level = 'normal'
        
        conn.close()
        
        return {
            'combination_id': combination_id,
            'current_miss': current_miss,
            'max_miss': max_miss,
            'avg_miss': avg_miss,
            'extreme_ratio': extreme_ratio,
            'warning_level': warning_level,
            'total_hits': total_hits,
            'total_periods': total_periods
        }
    
    def get_all_extreme_combinations(self, warning_level: str = None) -> List[Dict]:
        """获取所有极限组合"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM extreme_records ORDER BY current_miss DESC")
        records = cursor.fetchall()
        
        extreme_combinations = []
        for record in records:
            combination_id = record[1]
            status = self.get_extreme_status(combination_id)
            
            if warning_level is None or status.get('warning_level') == warning_level:
                extreme_combinations.append(status)
        
        conn.close()
        return extreme_combinations
    
    def register_combination(self, combination_id: str, combination_type: str, combination_value: str):
        """注册新的组合追踪"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR IGNORE INTO extreme_records 
            (combination_id, combination_type, combination_value)
            VALUES (?, ?, ?)
        ''', (combination_id, combination_type, combination_value))
        
        conn.commit()
        conn.close()

class BacktestEngine:
    """自动回测引擎"""
    
    def __init__(self, extreme_tracker: ExtremeStatTracker):
        self.extreme_tracker = extreme_tracker
    
    def run_backtest(self, strategy_engine, history_data: List[Dict], test_periods: int = 100) -> BacktestResult:
        """运行回测"""
        print(f"🔄 开始回测 - 测试期数: {test_periods}")
        
        if len(history_data) < test_periods:
            test_periods = len(history_data)
            print(f"⚠️ 历史数据不足，调整测试期数为: {test_periods}")
        
        # 选择测试数据
        test_data = history_data[-test_periods:]
        
        hit_count = 0
        miss_streaks = []
        current_miss_streak = 0
        period_details = []
        
        for i, period_data in enumerate(test_data):
            period = period_data.get('period', f"TEST_{i}")
            winning_number = period_data.get('winning_number', 0)
            
            try:
                # 使用策略预测
                prediction_result = strategy_engine.predict(period)
                predicted_numbers = prediction_result.numbers
                
                # 检查是否命中
                is_hit = winning_number in predicted_numbers
                
                if is_hit:
                    hit_count += 1
                    if current_miss_streak > 0:
                        miss_streaks.append(current_miss_streak)
                        current_miss_streak = 0
                else:
                    current_miss_streak += 1
                
                # 记录详情
                period_details.append({
                    'period': period,
                    'predicted': predicted_numbers,
                    'actual': winning_number,
                    'is_hit': is_hit,
                    'miss_streak': current_miss_streak,
                    'confidence': prediction_result.confidence
                })
                
                if (i + 1) % 20 == 0:
                    print(f"   进度: {i + 1}/{test_periods} ({(i + 1)/test_periods*100:.1f}%)")
            
            except Exception as e:
                print(f"   ⚠️ 期号 {period} 预测失败: {e}")
                continue
        
        # 添加最后的遗漏序列
        if current_miss_streak > 0:
            miss_streaks.append(current_miss_streak)
        
        # 计算统计指标
        hit_rate = hit_count / test_periods if test_periods > 0 else 0
        avg_miss_interval = np.mean(miss_streaks) if miss_streaks else 0
        max_miss_streak = max(miss_streaks) if miss_streaks else 0
        min_miss_streak = min(miss_streaks) if miss_streaks else 0
        
        # 计算ROI（简化版）
        roi = (hit_count * 40 - test_periods * 2) / (test_periods * 2) if test_periods > 0 else 0
        
        # 计算夏普比率（简化版）
        returns = [40 if detail['is_hit'] else -2 for detail in period_details]
        sharpe_ratio = np.mean(returns) / np.std(returns) if len(returns) > 1 and np.std(returns) > 0 else 0
        
        # 计算稳定性评分
        stability_score = self._calculate_stability_score(period_details)
        
        result = BacktestResult(
            strategy_name=getattr(strategy_engine, 'config', {}).get('strategy_name', 'Unknown'),
            test_periods=test_periods,
            hit_count=hit_count,
            hit_rate=hit_rate,
            avg_miss_interval=avg_miss_interval,
            max_miss_streak=max_miss_streak,
            min_miss_streak=min_miss_streak,
            roi=roi,
            sharpe_ratio=sharpe_ratio,
            stability_score=stability_score,
            period_details=period_details
        )
        
        # 保存回测记录
        self._save_backtest_result(result)
        
        print(f"✅ 回测完成!")
        print(f"   命中率: {hit_rate:.2%}")
        print(f"   平均遗漏: {avg_miss_interval:.1f}")
        print(f"   最大连续遗漏: {max_miss_streak}")
        print(f"   ROI: {roi:.2%}")
        print(f"   夏普比率: {sharpe_ratio:.3f}")
        print(f"   稳定性评分: {stability_score:.3f}")
        
        return result
    
    def _calculate_stability_score(self, period_details: List[Dict]) -> float:
        """计算稳定性评分"""
        if not period_details:
            return 0.0
        
        # 基于命中分布的稳定性
        hit_intervals = []
        last_hit_index = -1
        
        for i, detail in enumerate(period_details):
            if detail['is_hit']:
                if last_hit_index >= 0:
                    hit_intervals.append(i - last_hit_index)
                last_hit_index = i
        
        if not hit_intervals:
            return 0.0
        
        # 稳定性 = 1 - (标准差 / 平均值)
        mean_interval = np.mean(hit_intervals)
        std_interval = np.std(hit_intervals)
        
        if mean_interval > 0:
            stability = 1 - (std_interval / mean_interval)
            return max(0, min(1, stability))
        
        return 0.0
    
    def _save_backtest_result(self, result: BacktestResult):
        """保存回测结果"""
        conn = sqlite3.connect(self.extreme_tracker.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO backtest_records 
            (strategy_name, test_date, test_periods, hit_rate, avg_miss_interval, 
             max_miss_streak, roi, sharpe_ratio, stability_score, details)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            result.strategy_name,
            datetime.now().strftime('%Y-%m-%d'),
            result.test_periods,
            result.hit_rate,
            result.avg_miss_interval,
            result.max_miss_streak,
            result.roi,
            result.sharpe_ratio,
            result.stability_score,
            json.dumps(result.period_details[:10])  # 只保存前10期详情
        ))
        
        conn.commit()
        conn.close()
    
    def generate_report(self, result: BacktestResult) -> str:
        """生成回测报告"""
        report = f"""
# 三层筛选策略回测报告

## 基本信息
- 策略名称: {result.strategy_name}
- 测试期数: {result.test_periods}
- 测试日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 核心指标
- 命中率: {result.hit_rate:.2%}
- 命中次数: {result.hit_count}
- 平均遗漏间隔: {result.avg_miss_interval:.1f} 期
- 最大连续遗漏: {result.max_miss_streak} 期
- 最小连续遗漏: {result.min_miss_streak} 期

## 收益分析
- ROI (投资回报率): {result.roi:.2%}
- 夏普比率: {result.sharpe_ratio:.3f}
- 稳定性评分: {result.stability_score:.3f}

## 详细结果 (前10期)
"""
        
        for i, detail in enumerate(result.period_details[:10]):
            status = "✅ 命中" if detail['is_hit'] else "❌ 未中"
            predicted_str = ", ".join(f"{n:02d}" for n in detail['predicted'][:6])
            
            report += f"""
期号 {detail['period']}: 预测 [{predicted_str}...] 开奖 {detail['actual']:02d} {status}
"""
        
        report += f"""
## 评估总结
- 策略表现: {'优秀' if result.hit_rate > 0.3 else '良好' if result.hit_rate > 0.2 else '一般'}
- 风险控制: {'良好' if result.max_miss_streak < 20 else '需要改进'}
- 稳定性: {'稳定' if result.stability_score > 0.6 else '波动较大'}

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report

def test_extreme_tracker_backtest():
    """测试极限追踪和回测系统"""
    print("🧪 测试极限追踪和回测系统")
    print("=" * 60)
    
    try:
        # 初始化系统
        tracker = ExtremeStatTracker("test_extreme.db")
        backtest_engine = BacktestEngine(tracker)
        
        # 注册一些测试组合
        tracker.register_combination("zodiac_dragon", "zodiac", "龙")
        tracker.register_combination("color_red", "color", "红")
        tracker.register_combination("wuxing_gold", "wuxing", "金")
        
        print("✅ 系统初始化完成")
        
        # 模拟一些历史数据
        test_history = []
        for i in range(50):
            test_history.append({
                'period': f"2025{100+i:03d}",
                'winning_number': (i * 7 + 13) % 49 + 1  # 模拟开奖号码
            })
        
        # 更新一些期号结果
        for i in range(10):
            period_data = test_history[i]
            tracker.update_period_result(period_data['period'], period_data['winning_number'])
        
        print("✅ 历史数据更新完成")
        
        # 获取极限状态
        extreme_status = tracker.get_extreme_status("zodiac_dragon")
        print(f"✅ 龙生肖极限状态: {extreme_status}")
        
        # 获取所有极限组合
        all_extremes = tracker.get_all_extreme_combinations()
        print(f"✅ 发现 {len(all_extremes)} 个极限组合")
        
        print("\n🎉 极限追踪和回测系统测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_extreme_tracker_backtest()
