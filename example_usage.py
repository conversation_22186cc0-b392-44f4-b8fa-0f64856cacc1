from tail_group_filter import TailGroupFilter
import sqlite3
import json
import os
from datetime import datetime

def create_test_data():
    """Create test database and config"""
    # Create test config
    config = {
        "groups": [
            {
                "label": "Group 123",
                "tails": [1, 2, 3],
                "type": "basic"
            },
            {
                "label": "Group 456",
                "tails": [4, 5, 6],
                "type": "basic"
            },
            {
                "label": "Group 789",
                "tails": [7, 8, 9],
                "type": "basic"
            }
        ]
    }

    config_file = "test_config.json"
    with open(config_file, "w", encoding='utf-8') as f:
        json.dump(config, f, indent=2)

    # Create test database
    db_file = "test_lottery.db"
    with sqlite3.connect(db_file) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_results (
                draw_date TEXT,
                numbers TEXT,
                special_number INTEGER
            )
        ''')

        # Insert test data
        test_data = [
            ('2025-07-01', '1,11,21,31,41,2', 12),
            ('2025-07-02', '3,13,23,33,43,4', 14),
            ('2025-07-03', '5,15,25,35,45,6', 16),
            ('2025-07-04', '7,17,27,37,47,8', 18)
        ]
        cursor.executemany(
            'INSERT INTO lottery_results VALUES (?, ?, ?)',
            test_data
        )
        conn.commit()

    return config_file, db_file

def main():
    """Example usage of TailGroupFilter"""
    # Create test data
    config_file, db_file = create_test_data()

    try:
        # Initialize filter
        with TailGroupFilter(db_path=db_file, config_file=config_file) as filter:
            print("\n=== Basic Operations ===")

            # 1. Get numbers by tail
            tails = {1, 2}
            numbers = filter.get_numbers_by_tail(tails)
            print(f"\nNumbers with tails {tails}:")
            print(numbers)

            # 2. Filter candidates
            candidates = list(range(1, 20))
            filtered = filter.filter_candidates(candidates)
            print("\nFiltered candidates:")
            for label, nums in filtered.items():
                print(f"{label}: {nums}")

            # 3. Analyze history
            analysis = filter.analyze_history(days=30)
            print("\nHistory analysis:")
            for label, stats in analysis.items():
                print(f"\n{label}:")
                print(f"  Hit rate: {stats['hit_rate']:.2%}")
                print(f"  Stability: {stats['stability_score']:.2f}")

            # 4. Get hot groups
            hot_groups = filter.get_hot_groups()
            print("\nHot groups:")
            for group in hot_groups:
                print(f"{group['label']}: {group['hit_rate']:.2%}")

            # 5. Test data import/export
            print("\n=== Data Operations ===")

            # Create test CSV
            csv_file = "test_import.csv"
            with open(csv_file, "w", encoding='utf-8') as f:
                f.write("draw_date,numbers,special_number\n")
                f.write("2025-07-05,9;19;29;39;49;10,20\n")

            try:
                # Import CSV
                imported = filter.import_from_csv(csv_file)
                print(f"\nImported {imported} records from CSV")

                # Export analysis
                export_file = "analysis_export.json"
                analysis = filter.analyze_history(days=30)
                filter.export_analysis(analysis, export_file)
                print(f"\nExported analysis to {export_file}")

                # Test backup/restore
                backup_path = filter.create_backup()
                print(f"\nCreated backup at: {backup_path}")

                filter.restore_from_backup(backup_path)
                print("\nRestored from backup successfully")

            finally:
                # Cleanup temporary files
                for f in [csv_file, export_file, backup_path]:
                    if os.path.exists(f):
                        os.unlink(f)

    finally:
        # Cleanup test files
        os.unlink(config_file)
        os.unlink(db_file)

if __name__ == "__main__":
    main()
