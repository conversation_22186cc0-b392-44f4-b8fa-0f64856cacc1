<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoGen FastAPI Sample: Agent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: #f0f0f0;
        }

        #chat-container {
            width: 90%;
            max-width: 600px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        #messages {
            height: 600px;
            overflow-y: auto;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        .message {
            margin: 10px 0;
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message.error {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .message.system {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        #input-container input:disabled,
        #input-container button:disabled {
            background-color: #e0e0e0;
            cursor: not-allowed;
        }

        #input-container {
            display: flex;
        }

        #input-container input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        #input-container button {
            padding: 10px 20px;
            border: none;
            background-color: #007bff;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div id="chat-container">
        <div id="messages"></div>
        <div id="input-container">
            <input type="text" id="message-input" placeholder="Type a message...">
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        document.getElementById('message-input').addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });

        async function sendMessage() {
            const input = document.getElementById('message-input');
            const button = document.querySelector('#input-container button');
            const message = input.value;
            if (!message) return;

            // Display user message
            displayMessage(message, 'user');

            // Clear input and disable controls
            input.value = '';
            input.disabled = true;
            button.disabled = true;

            try {
                const response = await fetch('http://localhost:8001/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content: message, source: 'user' })
                });

                const data = await response.json();
                if (!response.ok) {
                    // Handle error response
                    if (data.detail && data.detail.type === 'error') {
                        displayMessage(data.detail.content, 'error');
                    } else {
                        displayMessage('Error: ' + (data.detail || 'Unknown error'), 'error');
                    }
                } else {
                    displayMessage(data.content, 'assistant');
                }
            } catch (error) {
                console.error('Error:', error);
                displayMessage('Error: Could not reach the server.', 'error');
            } finally {
                // Re-enable controls
                input.disabled = false;
                button.disabled = false;
                input.focus();
            }
        }

        function displayMessage(content, source) {
            const messagesContainer = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${source}`;

            const labelElement = document.createElement('span');
            labelElement.className = 'label';
            labelElement.textContent = source;

            const contentElement = document.createElement('div');
            contentElement.className = 'content';
            contentElement.textContent = content;

            messageElement.appendChild(labelElement);
            messageElement.appendChild(contentElement);
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        async function loadHistory() {
            try {
                const response = await fetch('http://localhost:8001/history');
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const history = await response.json();
                history.forEach(message => {
                    displayMessage(message.content, message.source);
                });
            } catch (error) {
                console.error('Error loading history:', error);
            }
        }

        // Load chat history when the page loads
        window.onload = loadHistory;
    </script>
</body>

</html>
