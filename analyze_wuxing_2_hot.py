#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析wuxing_2_hot模组的位置和工作原理
"""

from combo_generator import ComboGenerator
from dsl_strategy_parser import DSLStrategyParser
from data_attributes import HistoryDataManager
from dynamic_mapping_system import DynamicMappingSystem
import yaml

def analyze_wuxing_2_hot_location():
    """分析wuxing_2_hot模组的位置"""
    print("🔍 wuxing_2_hot模组位置分析")
    print("="*60)
    
    print("📍 1. 策略配置位置:")
    print("   文件: strategy_config.yaml")
    print("   策略ID: wuxing_2_hot")
    print("   策略名称: 五行2组合热门策略")
    print("   状态: ✅ 启用")
    print("   权重: 0.6")
    print()
    
    print("📍 2. 组合生成位置:")
    print("   文件: combo_generator.py")
    print("   方法: generate_wuxing_2()")
    print("   功能: 生成所有五行2元素组合")
    print()
    
    print("📍 3. 分析逻辑位置:")
    print("   文件: extreme_stat_tracker.py")
    print("   功能: 追踪五行组合的命中和遗漏统计")
    print()
    
    print("📍 4. 预测应用位置:")
    print("   文件: prediction_engine.py")
    print("   方法: _run_traditional_strategies()")
    print("   功能: 执行五行2组合热门分析")

def analyze_wuxing_combinations():
    """分析五行组合生成"""
    print(f"\n🔥 五行2组合生成分析")
    print("="*60)
    
    generator = ComboGenerator()
    wuxing_combos = generator.generate_wuxing_2()
    
    print(f"📊 五行元素: {generator.wuxing}")
    print(f"📊 组合总数: {len(wuxing_combos)} 个")
    print()
    
    print(f"📋 所有五行2组合:")
    for i, combo in enumerate(wuxing_combos, 1):
        key = combo['key']
        members = combo['members']
        print(f"   {i:2d}. {key} ({', '.join(members)})")
    
    return wuxing_combos

def analyze_strategy_config():
    """分析策略配置"""
    print(f"\n⚙️ wuxing_2_hot策略配置分析")
    print("="*60)
    
    parser = DSLStrategyParser()
    strategy_summary = parser.get_strategy_summary()
    
    # 查找wuxing_2_hot策略
    wuxing_strategy = None
    for strategy in strategy_summary['strategies']:
        if strategy['id'] == 'wuxing_2_hot':
            wuxing_strategy = strategy
            break
    
    if wuxing_strategy:
        print(f"✅ 找到wuxing_2_hot策略配置:")
        print(f"   策略ID: {wuxing_strategy['id']}")
        print(f"   策略名称: {wuxing_strategy['name']}")
        print(f"   策略类型: {wuxing_strategy['type']}")
        print(f"   分析模式: {wuxing_strategy.get('mode', '热门分析')}")
        print(f"   启用状态: {'✅ 启用' if wuxing_strategy['active'] else '❌ 禁用'}")
        print(f"   策略权重: {wuxing_strategy['weight']}")

        print(f"\n📋 过滤条件:")
        filters = wuxing_strategy.get('filters', {})
        if filters:
            for key, value in filters.items():
                print(f"   {key}: {value}")
        else:
            print("   min_hit_rate: 0.25 (最小命中率)")
            print("   max_current_miss: 8 (最大当前遗漏)")

        print(f"\n📊 评分参数:")
        scoring = wuxing_strategy.get('scoring', {})
        if scoring:
            for key, value in scoring.items():
                print(f"   {key}: {value}")
        else:
            print("   hit_weight: 0.7 (命中权重)")
            print("   miss_weight: 0.2 (遗漏权重)")
    else:
        print("❌ 未找到wuxing_2_hot策略配置")

def analyze_working_principle():
    """分析工作原理"""
    print(f"\n🧠 wuxing_2_hot工作原理分析")
    print("="*60)
    
    print("🔄 1. 数据流程:")
    print("   ├── 历史开奖数据 (CSV/数据库)")
    print("   ├── 动态映射系统 (号码→五行)")
    print("   ├── 五行组合生成器 (10个组合)")
    print("   ├── 极限统计追踪器 (命中/遗漏分析)")
    print("   └── 策略评分系统 (热门度计算)")
    print()
    
    print("📊 2. 五行映射原理:")
    print("   每个号码根据年份和春节时间动态映射到五行")
    print("   五行: 金、木、水、火、土")
    print("   映射会根据农历年份变化而调整")
    print()
    
    print("🔥 3. 热门分析原理:")
    print("   ├── 命中率分析: 统计每个五行组合的历史命中频率")
    print("   ├── 遗漏分析: 计算当前遗漏期数和历史最大遗漏")
    print("   ├── 热门度评分: 基于最近30期的表现")
    print("   └── 过滤条件: 最小命中率25%，最大当前遗漏8期")
    print()
    
    print("⚡ 4. 评分算法:")
    print("   总分 = 命中权重(0.7) × 命中率 + 遗漏权重(0.2) × 遗漏因子")
    print("   热门组合: 最近表现好且当前遗漏不太长的组合")
    print("   冷门组合: 长期未命中但可能即将回补的组合")

def demonstrate_analysis_process():
    """演示分析过程"""
    print(f"\n🎯 wuxing_2_hot分析过程演示")
    print("="*60)
    
    # 1. 生成五行组合
    print("📊 步骤1: 生成五行组合")
    generator = ComboGenerator()
    wuxing_combos = generator.generate_wuxing_2()
    print(f"   生成 {len(wuxing_combos)} 个五行2组合")
    
    # 2. 加载历史数据
    print(f"\n📚 步骤2: 加载历史数据")
    try:
        data_manager = HistoryDataManager()
        historical_data = data_manager.load_data()
        print(f"   加载 {len(historical_data)} 条历史记录")
        
        # 显示最近几期的五行数据
        print(f"   最近5期五行数据:")
        for i, record in enumerate(historical_data[-5:], 1):
            period = record.get('period_number', 'Unknown')
            five_element = record.get('five_element', 'Unknown')
            special_code = record.get('special_code', 'Unknown')
            print(f"     {i}. {period}期: 特码{special_code} → {five_element}")
    
    except Exception as e:
        print(f"   ❌ 数据加载失败: {e}")
    
    # 3. 分析五行组合表现
    print(f"\n🔍 步骤3: 分析五行组合表现")
    print("   对每个五行组合进行:")
    print("   ├── 历史命中次数统计")
    print("   ├── 当前遗漏期数计算")
    print("   ├── 最近30期表现分析")
    print("   └── 热门度评分计算")
    
    # 4. 应用过滤条件
    print(f"\n🔧 步骤4: 应用过滤条件")
    print("   过滤条件:")
    print("   ├── min_hit_rate: 0.25 (最小命中率25%)")
    print("   ├── max_current_miss: 8 (最大当前遗漏8期)")
    print("   └── recent_periods: 30 (分析最近30期)")
    
    # 5. 生成预测结果
    print(f"\n🎯 步骤5: 生成预测结果")
    print("   输出:")
    print("   ├── 热门五行组合排序")
    print("   ├── 对应的号码推荐")
    print("   ├── 置信度评分")
    print("   └── 策略权重应用")

def show_example_analysis():
    """显示分析示例"""
    print(f"\n📋 五行组合分析示例")
    print("="*60)
    
    # 模拟分析结果
    example_analysis = [
        {"combo": "金+木", "hit_rate": 0.32, "current_miss": 2, "score": 0.89, "status": "🔥 热门"},
        {"combo": "水+火", "hit_rate": 0.28, "current_miss": 5, "score": 0.76, "status": "⚡ 温热"},
        {"combo": "土+金", "hit_rate": 0.25, "current_miss": 8, "score": 0.65, "status": "💧 一般"},
        {"combo": "木+水", "hit_rate": 0.22, "current_miss": 12, "score": 0.45, "status": "❄️ 冷门"},
        {"combo": "火+土", "hit_rate": 0.18, "current_miss": 15, "score": 0.32, "status": "🚫 过滤"}
    ]
    
    print("📊 五行组合热门度排行:")
    print("排名  组合    命中率  当前遗漏  评分   状态")
    print("-" * 50)
    
    for i, analysis in enumerate(example_analysis, 1):
        combo = analysis['combo']
        hit_rate = analysis['hit_rate']
        current_miss = analysis['current_miss']
        score = analysis['score']
        status = analysis['status']
        
        print(f"{i:2d}.  {combo:6s}  {hit_rate:.2f}    {current_miss:2d}期     {score:.2f}  {status}")
    
    print(f"\n💡 分析说明:")
    print("- 🔥 热门: 命中率高且当前遗漏短，优先推荐")
    print("- ⚡ 温热: 表现良好，可以考虑")
    print("- 💧 一般: 达到最低标准，备选")
    print("- ❄️ 冷门: 低于过滤条件，但可能回补")
    print("- 🚫 过滤: 不符合策略要求，排除")

def main():
    """主分析流程"""
    print("🔍 wuxing_2_hot模组完整分析")
    print("="*70)
    
    # 分析模组位置
    analyze_wuxing_2_hot_location()
    
    # 分析五行组合
    analyze_wuxing_combinations()
    
    # 分析策略配置
    analyze_strategy_config()
    
    # 分析工作原理
    analyze_working_principle()
    
    # 演示分析过程
    demonstrate_analysis_process()
    
    # 显示分析示例
    show_example_analysis()
    
    print(f"\n🎯 总结:")
    print("wuxing_2_hot是一个基于五行理论的热门组合分析策略，")
    print("通过统计分析找出当前表现最好的五行组合，")
    print("为六合彩预测提供基于传统文化和统计学的双重支撑。")

if __name__ == "__main__":
    main()
