#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析虎兔羊狗小组的历史遗漏情况
为生肖能量分析界面提供数据
"""

from optimized_zodiac_engine import OptimizedZodiacEngine
from prediction_engine import PredictionEngine
import numpy as np

def analyze_tiger_rabbit_sheep_dog():
    """分析虎兔羊狗小组的历史遗漏"""
    print("🔍 分析虎兔羊狗小组的历史遗漏情况")
    print("="*70)
    
    # 初始化引擎
    print("📊 初始化高级生肖引擎...")
    engine = OptimizedZodiacEngine()
    
    # 查找虎兔羊狗小组
    target_members = ["虎", "兔", "羊", "狗"]
    target_group = None
    
    print(f"🔍 查找包含 {target_members} 的小组...")
    
    for group_id, group in engine.all_groups.items():
        if set(group.members) == set(target_members):
            target_group = group
            print(f"✅ 找到目标小组: {group_id}")
            break
    
    if not target_group:
        print("❌ 未找到完全匹配的小组")
        return None
    
    # 分析历史遗漏数据
    print(f"\n📊 虎兔羊狗小组历史分析:")
    print(f"   小组ID: {target_group.group_id}")
    print(f"   成员: {target_group.members}")
    print(f"   当前遗漏: {target_group.current_miss} 期")
    print(f"   历史最大遗漏: {target_group.max_miss} 期")
    print(f"   总命中次数: {target_group.hit_count}")
    print(f"   历史遗漏记录数: {len(target_group.miss_history)}")
    
    if target_group.miss_history:
        miss_array = np.array(target_group.miss_history)
        print(f"   平均遗漏: {np.mean(miss_array):.2f} 期")
        print(f"   遗漏标准差: {np.std(miss_array):.2f} 期")
        print(f"   最小遗漏: {np.min(miss_array)} 期")
        print(f"   最大遗漏: {np.max(miss_array)} 期")
        
        # Z-Score计算
        if target_group.miss_std_dev > 0:
            z_score = (target_group.current_miss - target_group.miss_mean) / target_group.miss_std_dev
            print(f"   当前Z-Score: {z_score:.3f}")
        
        # 遗漏分布统计
        print(f"\n📈 遗漏分布统计:")
        miss_ranges = {
            "0-2期": len([m for m in miss_array if 0 <= m <= 2]),
            "3-5期": len([m for m in miss_array if 3 <= m <= 5]),
            "6-10期": len([m for m in miss_array if 6 <= m <= 10]),
            "11-15期": len([m for m in miss_array if 11 <= m <= 15]),
            "16-20期": len([m for m in miss_array if 16 <= m <= 20]),
            "20期以上": len([m for m in miss_array if m > 20])
        }
        
        for range_name, count in miss_ranges.items():
            percentage = count / len(miss_array) * 100
            print(f"   {range_name}: {count} 次 ({percentage:.1f}%)")
        
        # 显示最近10次遗漏
        print(f"\n📋 最近10次遗漏记录:")
        recent_misses = target_group.miss_history[-10:] if len(target_group.miss_history) >= 10 else target_group.miss_history
        for i, miss in enumerate(recent_misses, 1):
            print(f"   {i:2d}. {miss} 期")
    
    # 组内能量分析
    print(f"\n⚡ 组内能量分析:")
    for member in target_group.members:
        miss_count = target_group.internal_misses[member]
        if miss_count >= 20:
            level = "🔥 极高压力"
        elif miss_count >= 15:
            level = "⚡ 高压力"
        elif miss_count >= 10:
            level = "💧 中等压力"
        elif miss_count >= 5:
            level = "❄️ 低压力"
        else:
            level = "🌟 极低压力"
        
        print(f"   {member}: {miss_count} 期 - {level}")
    
    # 验证当前遗漏是否接近历史极值
    print(f"\n🎯 当前状态评估:")
    if target_group.current_miss >= target_group.max_miss * 0.8:
        urgency = "🔥 极高"
    elif target_group.current_miss >= target_group.max_miss * 0.6:
        urgency = "⚡ 高"
    elif target_group.current_miss >= target_group.max_miss * 0.4:
        urgency = "💧 中等"
    else:
        urgency = "❄️ 低"
    
    print(f"   紧迫程度: {urgency}")
    print(f"   距离历史最大遗漏: {target_group.max_miss - target_group.current_miss} 期")
    
    if target_group.current_miss >= target_group.max_miss * 0.8:
        print(f"   ⚠️ 警告: 当前遗漏已接近历史极值!")
    
    return {
        'group_id': target_group.group_id,
        'members': target_group.members,
        'current_miss': target_group.current_miss,
        'max_miss': target_group.max_miss,
        'hit_count': target_group.hit_count,
        'miss_history': target_group.miss_history,
        'internal_misses': dict(target_group.internal_misses),
        'z_score': getattr(target_group, 'z_score', 0),
        'miss_mean': getattr(target_group, 'miss_mean', 0),
        'miss_std_dev': getattr(target_group, 'miss_std_dev', 0)
    }

def analyze_all_zodiac_groups():
    """分析所有生肖小组的历史最大遗漏"""
    print(f"\n🌟 分析所有生肖小组的历史最大遗漏")
    print("="*70)
    
    engine = OptimizedZodiacEngine()
    
    # 统计所有小组的最大遗漏
    max_miss_stats = {}
    
    for group_id, group in engine.all_groups.items():
        if group.max_miss > 0:
            members_key = ",".join(sorted(group.members))
            if members_key not in max_miss_stats:
                max_miss_stats[members_key] = {
                    'group_id': group_id,
                    'members': group.members,
                    'max_miss': group.max_miss,
                    'current_miss': group.current_miss,
                    'hit_count': group.hit_count
                }
            else:
                # 如果有重复，保留最大遗漏更大的
                if group.max_miss > max_miss_stats[members_key]['max_miss']:
                    max_miss_stats[members_key] = {
                        'group_id': group_id,
                        'members': group.members,
                        'max_miss': group.max_miss,
                        'current_miss': group.current_miss,
                        'hit_count': group.hit_count
                    }
    
    # 按最大遗漏排序
    sorted_groups = sorted(max_miss_stats.values(), key=lambda x: x['max_miss'], reverse=True)
    
    print(f"📊 历史最大遗漏排行榜 (前20名):")
    for i, group_data in enumerate(sorted_groups[:20], 1):
        members_str = ", ".join(group_data['members'])
        print(f"   {i:2d}. {members_str}: {group_data['max_miss']} 期 (当前: {group_data['current_miss']} 期)")
    
    # 查找虎兔羊狗的排名
    target_members = ["虎", "兔", "羊", "狗"]
    target_key = ",".join(sorted(target_members))
    
    if target_key in max_miss_stats:
        target_data = max_miss_stats[target_key]
        target_rank = next((i for i, group in enumerate(sorted_groups, 1) 
                           if set(group['members']) == set(target_members)), None)
        
        print(f"\n🎯 虎兔羊狗小组排名:")
        print(f"   历史最大遗漏: {target_data['max_miss']} 期")
        print(f"   当前遗漏: {target_data['current_miss']} 期")
        print(f"   排名: 第 {target_rank} 名 / {len(sorted_groups)} 个小组")
        
        # 计算百分位
        percentile = (len(sorted_groups) - target_rank + 1) / len(sorted_groups) * 100
        print(f"   百分位: {percentile:.1f}% (超过 {percentile:.1f}% 的小组)")
    
    return sorted_groups

def generate_energy_analysis_data():
    """生成生肖能量分析界面所需的数据"""
    print(f"\n🎨 生成生肖能量分析界面数据")
    print("="*70)
    
    # 分析虎兔羊狗小组
    tiger_rabbit_sheep_dog_data = analyze_tiger_rabbit_sheep_dog()
    
    if not tiger_rabbit_sheep_dog_data:
        return None
    
    # 生成界面显示数据
    energy_data = {
        'target_group': {
            'name': '虎兔羊狗',
            'current_miss': tiger_rabbit_sheep_dog_data['current_miss'],
            'max_miss_history': tiger_rabbit_sheep_dog_data['max_miss'],
            'hit_count': tiger_rabbit_sheep_dog_data['hit_count'],
            'z_score': tiger_rabbit_sheep_dog_data['z_score'],
            'urgency_level': '高' if tiger_rabbit_sheep_dog_data['current_miss'] >= 8 else '中等'
        },
        'individual_energy': []
    }
    
    # 个体生肖能量
    for member in tiger_rabbit_sheep_dog_data['members']:
        miss_count = tiger_rabbit_sheep_dog_data['internal_misses'][member]
        
        if miss_count >= 20:
            pressure_level = "极高"
            pressure_icon = "🔥"
        elif miss_count >= 15:
            pressure_level = "高"
            pressure_icon = "⚡"
        elif miss_count >= 10:
            pressure_level = "中等"
            pressure_icon = "💧"
        elif miss_count >= 5:
            pressure_level = "低"
            pressure_icon = "❄️"
        else:
            pressure_level = "极低"
            pressure_icon = "🌟"
        
        energy_data['individual_energy'].append({
            'zodiac': member,
            'miss_count': miss_count,
            'pressure_level': pressure_level,
            'pressure_icon': pressure_icon
        })
    
    print(f"✅ 生成的能量分析数据:")
    print(f"   目标小组: {energy_data['target_group']['name']}")
    print(f"   当前遗漏: {energy_data['target_group']['current_miss']} 期")
    print(f"   历史最大遗漏: {energy_data['target_group']['max_miss_history']} 期")
    print(f"   个体能量数据: {len(energy_data['individual_energy'])} 个生肖")
    
    return energy_data

if __name__ == "__main__":
    # 运行分析
    tiger_data = analyze_tiger_rabbit_sheep_dog()
    
    # 分析所有小组排名
    all_groups_ranking = analyze_all_zodiac_groups()
    
    # 生成界面数据
    energy_ui_data = generate_energy_analysis_data()
    
    print(f"\n🎯 分析完成!")
    print("数据已准备好用于生肖能量分析界面显示")
