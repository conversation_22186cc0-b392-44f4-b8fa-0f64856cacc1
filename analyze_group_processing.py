#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析103,950个小组的历史数据处理机制
详细展示命中/遗漏/最大遗漏的计算过程
"""

import numpy as np
from typing import Dict, List
from advanced_zodiac_engine import AdvancedZodiacEngine
from prediction_engine import PredictionEngine
from datetime import datetime

def analyze_group_processing():
    """分析小组处理机制"""
    print("🔍 分析103,950个小组的历史数据处理机制")
    print("="*70)
    
    # 初始化引擎
    print("📊 初始化高级生肖引擎...")
    engine = AdvancedZodiacEngine()
    
    print(f"✅ 生成了 {len(engine.all_groups):,} 个小组")
    
    # 加载历史数据
    print("\n📈 加载历史数据...")
    pred_engine = PredictionEngine()
    history_data = pred_engine.load_historical_data()
    print(f"✅ 加载了 {len(history_data)} 期历史数据")
    
    # 选择几个示例小组进行详细分析
    sample_groups = list(engine.all_groups.items())[:5]
    
    print(f"\n🔬 详细分析前5个小组的处理过程:")
    print("-"*70)
    
    for group_id, group in sample_groups:
        print(f"\n📋 小组 {group_id}: {group.members}")
        print(f"   初始状态: 当前遗漏=0, 历史遗漏=[], 最大遗漏=0")
    
    # 模拟历史数据处理过程
    print(f"\n🔄 开始处理历史数据 (前20期示例):")
    print("-"*70)
    
    for i, period_data in enumerate(history_data[:20]):
        special_code = period_data.get('special_code')
        zodiac = period_data.get('zodiac', '')
        period = period_data.get('period', '')
        
        if not special_code or not zodiac:
            continue
        
        print(f"\n期号 {period}: 开奖 {special_code:02d} (生肖: {zodiac})")
        
        # 更新示例小组
        for group_id, group in sample_groups:
            old_miss = group.current_miss
            
            if zodiac in group.members:
                # 命中
                if group.current_miss > 0:
                    group.miss_history.append(group.current_miss)
                    group.max_miss = max(group.max_miss, group.current_miss)
                
                group.current_miss = 0
                group.internal_misses[zodiac] = 0
                
                print(f"   ✅ {group_id} 命中! 遗漏 {old_miss}→0, 记录遗漏: {group.miss_history[-1] if group.miss_history else 0}")
            else:
                # 未命中
                group.current_miss += 1
                print(f"   ❌ {group_id} 未中, 遗漏 {old_miss}→{group.current_miss}")
            
            # 更新组内其他生肖遗漏
            for member in group.members:
                if member != zodiac:
                    group.internal_misses[member] += 1
    
    # 显示处理结果统计
    print(f"\n📊 处理完成后的统计结果:")
    print("="*70)
    
    for group_id, group in sample_groups:
        hit_count = len(group.miss_history)
        total_periods = 20
        hit_rate = hit_count / total_periods if total_periods > 0 else 0
        
        print(f"\n📋 小组 {group_id}: {group.members}")
        print(f"   命中次数: {hit_count}")
        print(f"   命中率: {hit_rate:.2%}")
        print(f"   当前遗漏: {group.current_miss} 期")
        print(f"   最大遗漏: {group.max_miss} 期")
        print(f"   历史遗漏: {group.miss_history}")
        
        if group.miss_history:
            avg_miss = np.mean(group.miss_history)
            std_miss = np.std(group.miss_history)
            print(f"   平均遗漏: {avg_miss:.2f} 期")
            print(f"   遗漏标准差: {std_miss:.2f} 期")
            
            # 计算Z-Score
            if std_miss > 0:
                z_score = (group.current_miss - avg_miss) / std_miss
                print(f"   当前Z-Score: {z_score:.3f}")
        
        # 显示组内能量
        print(f"   组内能量: {dict(group.internal_misses)}")

def analyze_full_processing():
    """分析完整的103,950个小组处理"""
    print(f"\n🌐 完整系统分析 (103,950个小组)")
    print("="*70)
    
    try:
        # 初始化完整系统
        engine = AdvancedZodiacEngine()
        pred_engine = PredictionEngine()
        
        print("📊 开始完整历史数据校准...")
        start_time = datetime.now()
        
        # 加载并处理所有历史数据
        history_data = pred_engine.load_historical_data()
        engine.calibrate_with_history(history_data)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"✅ 处理完成! 用时: {processing_time:.2f} 秒")
        
        # 统计分析
        total_groups = len(engine.all_groups)
        groups_with_hits = 0
        total_hits = 0
        total_max_miss = 0
        z_scores = []
        
        for group in engine.all_groups.values():
            if group.miss_history:
                groups_with_hits += 1
                total_hits += len(group.miss_history)
                total_max_miss += group.max_miss
                
                # 计算Z-Score
                if group.miss_std_dev > 0:
                    z_score = (group.current_miss - group.miss_mean) / group.miss_std_dev
                    z_scores.append(z_score)
        
        print(f"\n📈 全系统统计:")
        print(f"   总小组数: {total_groups:,}")
        print(f"   有命中记录的小组: {groups_with_hits:,}")
        print(f"   总命中次数: {total_hits:,}")
        print(f"   平均每组命中: {total_hits/groups_with_hits:.2f} 次" if groups_with_hits > 0 else "   平均每组命中: 0 次")
        print(f"   平均最大遗漏: {total_max_miss/groups_with_hits:.2f} 期" if groups_with_hits > 0 else "   平均最大遗漏: 0 期")
        
        if z_scores:
            print(f"   Z-Score统计:")
            print(f"     平均值: {np.mean(z_scores):.3f}")
            print(f"     标准差: {np.std(z_scores):.3f}")
            print(f"     最大值: {np.max(z_scores):.3f}")
            print(f"     最小值: {np.min(z_scores):.3f}")
            
            # 异常小组统计
            high_z_groups = len([z for z in z_scores if z >= 2.0])
            extreme_z_groups = len([z for z in z_scores if z >= 3.0])
            
            print(f"   异常小组 (Z≥2.0): {high_z_groups} 个 ({high_z_groups/len(z_scores)*100:.1f}%)")
            print(f"   极端异常 (Z≥3.0): {extreme_z_groups} 个 ({extreme_z_groups/len(z_scores)*100:.1f}%)")
        
        # 性能分析
        periods_processed = len(history_data)
        operations_per_second = (total_groups * periods_processed) / processing_time
        
        print(f"\n⚡ 性能分析:")
        print(f"   处理期数: {periods_processed}")
        print(f"   总操作数: {total_groups * periods_processed:,}")
        print(f"   处理速度: {operations_per_second:,.0f} 操作/秒")
        print(f"   内存使用: 约 {total_groups * 500 / 1024 / 1024:.1f} MB")
        
    except Exception as e:
        print(f"❌ 完整分析失败: {e}")
        import traceback
        traceback.print_exc()

def explain_processing_logic():
    """解释处理逻辑"""
    print(f"\n📚 103,950个小组处理逻辑详解:")
    print("="*70)
    
    explanation = """
🔍 数据结构:
每个小组包含:
├── group_id: 小组唯一标识 (如 "P0001-A")
├── members: 4个生肖成员 (如 ["鼠", "牛", "虎", "兔"])
├── current_miss: 当前连续遗漏期数
├── max_miss: 历史最大遗漏期数
├── miss_history: 历史遗漏期数列表 [3, 7, 2, 5, ...]
├── internal_misses: 组内每个生肖的遗漏 {"鼠": 5, "牛": 3, ...}
├── miss_mean: 历史遗漏均值
├── miss_std_dev: 历史遗漏标准差
└── z_score: 当前Z-Score值

🔄 处理流程:
对于每期开奖:
1. 获取开奖号码和对应生肖
2. 遍历所有103,950个小组:
   ├── 如果生肖在小组中 (命中):
   │   ├── 将current_miss添加到miss_history
   │   ├── 更新max_miss = max(max_miss, current_miss)
   │   ├── 重置current_miss = 0
   │   └── 重置命中生肖的internal_misses = 0
   └── 如果生肖不在小组中 (未命中):
       └── current_miss += 1
3. 更新所有生肖的internal_misses += 1 (除命中生肖)

📊 统计计算:
处理完所有历史数据后:
├── miss_mean = mean(miss_history)
├── miss_std_dev = std(miss_history)
└── z_score = (current_miss - miss_mean) / miss_std_dev

🎯 预测逻辑:
筛选候选小组:
├── Z-Score >= 阈值 (默认2.0)
├── 按Z-Score降序排列
└── 分析组内能量分布
"""
    
    print(explanation)

def demonstrate_example():
    """演示具体例子"""
    print(f"\n💡 具体例子演示:")
    print("="*70)
    
    example = """
假设小组 P0001-A: ["鼠", "牛", "虎", "兔"]

历史处理过程:
期号    开奖生肖    小组状态                    操作
2025190    龙      未命中 (current_miss=1)    遗漏+1
2025189    蛇      未命中 (current_miss=2)    遗漏+1  
2025188    鼠      命中!                      记录遗漏2→miss_history, 清零
2025187    马      未命中 (current_miss=1)    遗漏+1
2025186    羊      未命中 (current_miss=2)    遗漏+1
2025185    牛      命中!                      记录遗漏2→miss_history, 清零
...

处理完成后:
├── miss_history: [2, 2, 5, 3, 7, 1, 4, ...]  # 历史遗漏记录
├── miss_mean: 3.2                            # 平均遗漏
├── miss_std_dev: 1.8                         # 标准差
├── current_miss: 5                           # 当前遗漏
├── max_miss: 7                               # 最大遗漏
└── z_score: (5-3.2)/1.8 = 1.0               # Z-Score

如果 current_miss = 8:
z_score = (8-3.2)/1.8 = 2.67 > 2.0 → 成为候选小组!

组内能量分析:
├── 鼠: 5期未出 (中等压力)
├── 牛: 12期未出 (高压力) ← 重点关注
├── 虎: 3期未出 (低压力)
└── 兔: 8期未出 (中等压力)
"""
    
    print(example)

if __name__ == "__main__":
    # 运行详细分析
    analyze_group_processing()
    
    # 解释处理逻辑
    explain_processing_logic()
    
    # 演示具体例子
    demonstrate_example()
    
    # 运行完整分析
    analyze_full_processing()
    
    print(f"\n🎯 分析完成!")
    print("="*70)
