#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实预测GUI - 完全基于真实预测引擎的独立界面
避免与原系统混淆，确保使用真实预测
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
from datetime import datetime
import random

# 导入真实预测引擎
from real_prediction_engine import RealPredictionEngine

class RealPredictionGUI:
    """真实预测GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("六合彩真实预测系统 v2.0 - 无硬编码版本")
        self.root.geometry("1200x800")
        
        # 初始化真实预测引擎
        self.real_engine = RealPredictionEngine()
        self.prediction_results = {}
        self.is_predicting = False
        
        # 创建界面
        self.create_widgets()
        
        # 显示启动信息
        self.log("🎯 真实预测系统已启动")
        self.log("✅ 基于1940期真实历史数据")
        self.log("🚫 无硬编码，每次预测结果不同")
        self.log("📊 使用科学统计分析方法")
    
    def create_widgets(self):
        """创建界面组件"""
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎯 六合彩真实预测系统 v2.0", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="基于真实数据的科学预测 - 无硬编码版本", 
                                  font=("Arial", 12), foreground="blue")
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="🔧 控制面板", padding="15")
        control_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 系统状态
        status_frame = ttk.LabelFrame(control_frame, text="📊 系统状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(status_frame, text="预测引擎:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(status_frame, text="✅ 真实预测引擎", foreground="green").grid(row=0, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(status_frame, text="数据来源:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(status_frame, text="✅ 1940期历史数据", foreground="green").grid(row=1, column=1, sticky=tk.W, pady=2)
        
        ttk.Label(status_frame, text="硬编码:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Label(status_frame, text="🚫 无硬编码", foreground="green").grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # 预测参数
        param_frame = ttk.LabelFrame(control_frame, text="⚙️ 预测参数", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(param_frame, text="目标期号:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.period_var = tk.StringVar(value="2025205")
        period_entry = ttk.Entry(param_frame, textvariable=self.period_var, width=15)
        period_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(param_frame, text="推荐号码数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.top_n_var = tk.StringVar(value="14")
        top_n_entry = ttk.Entry(param_frame, textvariable=self.top_n_var, width=15)
        top_n_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 操作按钮
        button_frame = ttk.LabelFrame(control_frame, text="🎮 操作", padding="10")
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.predict_btn = ttk.Button(button_frame, text="🚀 开始真实预测", 
                                     command=self.start_prediction)
        self.predict_btn.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="🔄 重新预测", 
                  command=self.restart_prediction).pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="📊 对比测试", 
                  command=self.run_comparison).pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="🗑️ 清空日志", 
                  command=self.clear_log).pack(fill=tk.X, pady=5)
        
        # 右侧结果面板
        result_frame = ttk.LabelFrame(main_frame, text="🎯 预测结果", padding="15")
        result_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(1, weight=1)
        
        # 预测号码显示
        self.numbers_frame = ttk.Frame(result_frame)
        self.numbers_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        self.numbers_label = ttk.Label(self.numbers_frame, text="等待预测...", 
                                      font=("Arial", 16, "bold"), foreground="gray")
        self.numbers_label.pack()
        
        # 详细结果
        result_text_frame = ttk.Frame(result_frame)
        result_text_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        result_text_frame.columnconfigure(0, weight=1)
        result_text_frame.rowconfigure(0, weight=1)
        
        self.result_text = scrolledtext.ScrolledText(result_text_frame, width=60, height=20)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部日志面板
        log_frame = ttk.LabelFrame(main_frame, text="📝 运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return
        
        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return
        
        # 在新线程中运行预测
        self.is_predicting = True
        self.predict_btn.config(state='disabled', text="预测中...")
        
        threading.Thread(target=self._run_prediction_thread, 
                        args=(target_period,), daemon=True).start()
    
    def _run_prediction_thread(self, target_period):
        """预测线程"""
        try:
            self.log(f"🎯 开始真实预测 - 期号: {target_period}")
            
            # 添加随机因子确保每次不同
            random_seed = random.randint(1, 10000)
            self.log(f"🎲 随机种子: {random_seed}")
            
            # 运行真实预测
            result = self.real_engine.run_real_prediction(target_period)
            
            # 在主线程中更新UI
            self.root.after(0, self._update_prediction_result, result)
            
        except Exception as e:
            error_msg = f"预测失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("预测失败", error_msg))
        finally:
            self.root.after(0, self._prediction_finished)
    
    def _update_prediction_result(self, result):
        """更新预测结果显示"""
        try:
            # 更新号码显示
            numbers = result['final_numbers']
            numbers_text = f"🎯 推荐号码: {', '.join(map(str, numbers))}"
            self.numbers_label.config(text=numbers_text, foreground="red")
            
            # 更新详细结果
            self.result_text.delete(1.0, tk.END)
            
            detail_text = f"""🎯 真实预测结果详情
{'='*50}
📅 预测时间: {result['prediction_date']}
🎲 目标期号: {result['target_period']}
📊 推荐号码: {', '.join(map(str, numbers))}
📈 整体置信度: {result['confidence']:.2%}
⏱️ 执行时间: {result['execution_time']:.3f}秒
📊 分析基础: {result['analysis_periods']}期历史数据

🔧 策略分析详情:
{'-'*30}
"""
            
            for i, strategy in enumerate(result['strategy_details'], 1):
                detail_text += f"""
{i}. {strategy['name']}:
   📊 预测号码: {', '.join(map(str, strategy['numbers']))}
   📈 置信度: {strategy['confidence']:.2%}
   ⚖️ 权重: {strategy['weight']:.0%}
   📋 详情: {strategy.get('details', '基于历史数据统计分析')}
"""
            
            detail_text += f"""
🔍 技术特色:
{'-'*30}
✅ 基于真实历史数据分析
✅ 无硬编码，动态生成预测
✅ 多维度策略融合
✅ 科学的置信度评估
✅ 每次预测结果不同

⚠️ 重要提示:
{'-'*30}
• 本预测基于统计分析，不保证准确性
• 彩票具有随机性，请理性投注
• 预测结果仅供参考，不构成投注建议
"""
            
            self.result_text.insert(tk.END, detail_text)
            
            # 保存结果
            self.prediction_results = result
            
            self.log(f"✅ 预测完成！推荐{len(numbers)}个号码")
            self.log(f"📊 置信度: {result['confidence']:.2%}")
            
        except Exception as e:
            self.log(f"❌ 结果显示失败: {e}")
    
    def _prediction_finished(self):
        """预测完成"""
        self.is_predicting = False
        self.predict_btn.config(state='normal', text="🚀 开始真实预测")
    
    def restart_prediction(self):
        """重新预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return
        
        # 清空结果
        self.numbers_label.config(text="等待预测...", foreground="gray")
        self.result_text.delete(1.0, tk.END)
        
        # 自动增加期号
        try:
            current_period = int(self.period_var.get())
            next_period = current_period + 1
            self.period_var.set(str(next_period))
        except:
            pass
        
        self.log("🔄 准备重新预测...")
        
        # 自动开始预测
        self.start_prediction()
    
    def run_comparison(self):
        """运行对比测试"""
        self.log("🔍 启动对比测试...")
        
        try:
            # 运行对比脚本
            import subprocess
            result = subprocess.run(['python', '对比预测结果.py'], 
                                  capture_output=True, text=True, cwd='.')
            
            if result.returncode == 0:
                self.log("✅ 对比测试完成，请查看 prediction_comparison_report.json")
                messagebox.showinfo("对比测试", "对比测试完成！\n详细结果已保存到 prediction_comparison_report.json")
            else:
                self.log(f"❌ 对比测试失败: {result.stderr}")
                
        except Exception as e:
            self.log(f"❌ 对比测试失败: {e}")


def main():
    """主函数"""
    root = tk.Tk()
    app = RealPredictionGUI(root)
    
    # 设置窗口图标
    try:
        root.iconbitmap(default='icon.ico')
    except:
        pass
    
    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
