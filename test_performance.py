import unittest
import tempfile
import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from tail_group_filter import TailGroupFilter, PerformanceMonitor

class TestPerformance(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Create test data"""
        cls.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        cls.temp_config = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
        cls.temp_db.close()
        cls.temp_config.close()

        # Create config
        test_config = {
            "tail_group_settings": {
                "min_group_size": 3,
                "max_group_size": 6,
                "min_tail_value": 0,
                "max_tail_value": 9,
                "hot_group_threshold": 5,
                "analysis_window": 30
            },
            "database_settings": {
                "backup_interval": 24,
                "max_history_records": 1000
            },
            "filter_settings": {
                "enable_performance_logging": True,
                "cache_results": True,
                "parallel_processing": True
            },
            "groups": [
                {
                    "label": "Test Group 1",
                    "tails": [1, 2, 3],
                    "description": "Test group 1"
                },
                {
                    "label": "Test Group 2",
                    "tails": [4, 5, 6],
                    "description": "Test group 2"
                }
            ]
        }
        with open(cls.temp_config.name, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=4)

        # Initialize filter with performance monitoring
        cls.filter = TailGroupFilter(
            db_path=cls.temp_db.name,
            config_file=cls.temp_config.name,
            enable_monitoring=True,
            cache_enabled=True
        )

        # Verify performance monitoring is enabled
        assert cls.filter.performance_monitor is not None
        assert cls.filter.cache_enabled

    @classmethod
    def tearDownClass(cls):
        if cls.filter.conn:
            cls.filter.conn.close()
        try:
            os.unlink(cls.temp_db.name)
            os.unlink(cls.temp_config.name)
        except (OSError, IOError):
            pass  # Ignore deletion errors

    def test_performance_monitoring(self):
        """Test performance monitoring features"""
        # 1. Test cache functionality
        for _ in range(3):
            result = self.filter.analyze_history(days=30)
            self.assertIsNotNone(result)

        # Check cache hits
        stats = self.filter.performance_monitor.get_stats()
        self.assertGreater(stats["cache_stats"]["hits"], 0)

        # 2. Test query timing
        self.filter.cache.clear()  # Clear cache to force new query
        _ = self.filter.analyze_history(days=30)

        stats = self.filter.performance_monitor.get_stats()
        self.assertGreater(len(stats["query_times"]), 0)
        self.assertGreater(stats["avg_query_time"], 0)

        # 3. Test operation timing
        self.assertIn("analyze_history", stats["operation_stats"])
        self.assertGreater(stats["operation_stats"]["analyze_history"]["total_calls"], 0)

    def test_config_reload_with_performance(self):
        """Test config reload with performance monitoring"""
        # Modify config
        new_config = {
            "filter_settings": {
                "enable_performance_logging": True,
                "cache_results": False,
                "profiling_level": "basic"
            },
            "groups": [
                {
                    "label": "New Group",
                    "tails": [7, 8, 9],
                    "type": "basic"
                }
            ]
        }
        with open(self.temp_config.name, 'w') as f:
            json.dump(new_config, f)

        # Reload and verify
        self.filter.reload_config()

        # Verify config changes
        self.assertEqual(len(self.filter.config["groups"]), 1)
        self.assertEqual(self.filter.config["groups"][0]["label"], "New Group")

        # Verify performance monitoring still works
        self.assertIsNotNone(self.filter.performance_monitor)
        self.assertFalse(self.filter.cache_enabled)

    def test_import_export_with_performance(self):
        """Test data import/export with performance monitoring"""
        # Create test CSV
        csv_file = tempfile.NamedTemporaryFile(suffix='.csv', delete=False)
        with open(csv_file.name, 'w') as f:
            f.write("draw_date,numbers,special_number\n")
            f.write("2025-07-01,1;11;21;31;41;2,12\n")
            f.write("2025-07-02,3;13;23;33;43;4,14\n")
            f.write("2025-07-03,5;15;25;35;45;6,16\n")

        try:
            # 1. Test import with performance monitoring
            start_time = datetime.now()
            imported = self.filter.import_from_csv(csv_file.name)
            self.assertEqual(imported, 3)

            # Check import performance
            stats = self.filter.performance_monitor.get_stats()
            self.assertIn('import_csv', stats['operation_stats'])
            self.assertGreater(stats['operation_stats']['import_csv']['avg_time'], 0)

            # 2. Test export with performance monitoring
            result = self.filter.analyze_history(days=30)
            export_file = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
            self.filter.export_analysis(result, export_file.name)

            # Verify export and performance data
            with open(export_file.name) as f:
                exported = json.load(f)
                self.assertIn('analysis_date', exported)
                self.assertIn('groups', exported)
                self.assertIn('performance', exported)

                # Verify performance data in export
                perf_data = exported['performance']
                self.assertIn('query_times', perf_data)
                self.assertIn('operation_stats', perf_data)
                self.assertIn('cache_stats', perf_data)

        finally:
            # Cleanup
            if os.path.exists(csv_file.name):
                os.unlink(csv_file.name)
            if os.path.exists(export_file.name):
                os.unlink(export_file.name)

    def test_backup_restore_with_performance(self):
        """Test backup and restore with performance monitoring"""
        # 1. Create backup with performance data
        backup_path = self.filter.create_backup()
        self.assertTrue(os.path.exists(backup_path))

        # Check if performance stats were saved
        timestamp = backup_path.split('_')[-1].split('.')[0]
        stats_file = os.path.join(
            os.path.dirname(backup_path),
            f"performance_stats_{timestamp}.json"
        )
        self.assertTrue(os.path.exists(stats_file))

        try:
            # 2. Verify performance stats file
            with open(stats_file) as f:
                perf_stats = json.load(f)
                self.assertIn('query_times', perf_stats)
                self.assertIn('operation_stats', perf_stats)
                self.assertIn('cache_stats', perf_stats)

            # 3. Modify original database
            with open(self.temp_db.name, 'w') as f:
                f.write('corrupted')

            # 4. Restore with performance monitoring
            self.filter.restore_from_backup(backup_path)

            # 5. Verify restore and performance
            self.assertTrue(os.path.getsize(self.temp_db.name) > 0)

            # 6. Check if operations were logged
            stats = self.filter.performance_monitor.get_stats()
            self.assertIn('restore_from_backup', stats['operation_stats'])

        finally:
            # Cleanup
            for file in [backup_path, stats_file]:
                if os.path.exists(file):
                    os.unlink(file)

if __name__ == '__main__':
    unittest.main()
