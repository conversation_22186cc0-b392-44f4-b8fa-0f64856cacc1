#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比预测结果 - 直观展示原系统vs真实预测引擎的差异
"""

import json
from datetime import datetime
from prediction_engine import PredictionEngine
from real_prediction_engine import RealPredictionEngine

def compare_prediction_systems():
    """对比两个预测系统"""
    print("🔍 预测系统对比测试")
    print("=" * 60)
    
    target_period = "2025205"  # 测试期号
    
    # 1. 测试原系统（硬编码）
    print("\n1️⃣ 测试原系统（硬编码）...")
    print("-" * 30)
    
    try:
        original_engine = PredictionEngine()
        original_result = original_engine.run_prediction(target_period)
        
        print(f"✅ 原系统预测完成")
        print(f"📊 推荐号码: {original_result.final_numbers}")
        print(f"📈 置信度: {original_result.confidence_score:.2%}")
        print(f"🎯 使用策略: {original_result.total_strategies_used}个")
        print(f"🤖 使用模型: {original_result.total_models_used}个")
        print(f"⏱️ 执行时间: {original_result.execution_time:.3f}秒")
        
        # 显示策略详情
        print(f"\n策略详情:")
        for i, strategy in enumerate(original_result.strategy_details, 1):
            strategy_name = getattr(strategy, 'strategy_name', 'Unknown')
            predicted_numbers = getattr(strategy, 'predicted_numbers', [])
            confidence = getattr(strategy, 'confidence', 0)
            print(f"  {i}. {strategy_name}")
            print(f"     号码: {predicted_numbers}")
            print(f"     置信度: {confidence:.3f}")
        
    except Exception as e:
        print(f"❌ 原系统测试失败: {e}")
        original_result = None
    
    # 2. 测试真实预测引擎
    print(f"\n2️⃣ 测试真实预测引擎...")
    print("-" * 30)
    
    try:
        real_engine = RealPredictionEngine()
        real_result = real_engine.run_real_prediction(target_period)
        
        print(f"✅ 真实引擎预测完成")
        print(f"📊 推荐号码: {real_result['final_numbers']}")
        print(f"📈 置信度: {real_result['confidence']:.2%}")
        print(f"🎯 使用策略: {real_result['strategies_used']}个")
        print(f"🤖 使用模型: {real_result['models_used']}个")
        print(f"⏱️ 执行时间: {real_result['execution_time']:.3f}秒")
        print(f"📅 分析基础: {real_result['analysis_periods']}期历史数据")
        
        # 显示策略详情
        print(f"\n策略详情:")
        for i, strategy in enumerate(real_result['strategy_details'], 1):
            print(f"  {i}. {strategy['name']}")
            print(f"     号码: {strategy['numbers']}")
            print(f"     置信度: {strategy['confidence']:.3f}")
            print(f"     权重: {strategy['weight']:.2f}")
        
    except Exception as e:
        print(f"❌ 真实引擎测试失败: {e}")
        real_result = None
    
    # 3. 对比分析
    print(f"\n3️⃣ 对比分析")
    print("=" * 30)
    
    if original_result and real_result:
        # 提取号码进行对比
        original_numbers = set(original_result.final_numbers)
        real_numbers = set(real_result['final_numbers'])
        
        # 计算重叠
        overlap = original_numbers & real_numbers
        overlap_rate = len(overlap) / len(original_numbers) * 100
        
        print(f"📊 号码对比:")
        print(f"   原系统: {sorted(original_numbers)}")
        print(f"   真实引擎: {sorted(real_numbers)}")
        print(f"   重叠号码: {sorted(overlap)} ({len(overlap)}个)")
        print(f"   重叠率: {overlap_rate:.1f}%")
        
        print(f"\n📈 性能对比:")
        print(f"   置信度: 原系统 {original_result.confidence_score:.2%} vs 真实引擎 {real_result['confidence']:.2%}")
        print(f"   策略数: 原系统 {original_result.total_strategies_used} vs 真实引擎 {real_result['strategies_used']}")
        print(f"   执行时间: 原系统 {original_result.execution_time:.3f}s vs 真实引擎 {real_result['execution_time']:.3f}s")
        
        # 判断是否为硬编码
        print(f"\n🔍 硬编码检测:")
        if overlap_rate > 80:
            print(f"   ⚠️ 高重叠率({overlap_rate:.1f}%)，可能存在硬编码")
        else:
            print(f"   ✅ 低重叠率({overlap_rate:.1f}%)，预测结果差异明显")
    
    # 4. 多次运行测试（检测是否重复）
    print(f"\n4️⃣ 重复性测试")
    print("-" * 30)
    
    print("测试真实引擎是否每次产生不同结果...")
    real_results = []
    
    for i in range(3):
        try:
            result = real_engine.run_real_prediction(f"202520{6+i}")
            real_results.append(set(result['final_numbers']))
            print(f"  第{i+1}次: {sorted(result['final_numbers'])}")
        except:
            print(f"  第{i+1}次: 测试失败")
    
    # 检查结果是否相同
    if len(real_results) >= 2:
        all_same = all(r == real_results[0] for r in real_results[1:])
        if all_same:
            print(f"   ❌ 所有结果相同，可能存在硬编码")
        else:
            print(f"   ✅ 结果不同，确认为真实动态预测")
    
    # 5. 生成对比报告
    comparison_report = {
        'test_time': datetime.now().isoformat(),
        'target_period': target_period,
        'original_system': {
            'numbers': list(original_numbers) if original_result else [],
            'confidence': original_result.confidence_score if original_result else 0,
            'strategies': original_result.total_strategies_used if original_result else 0,
            'models': original_result.total_models_used if original_result else 0
        },
        'real_system': {
            'numbers': real_result['final_numbers'] if real_result else [],
            'confidence': real_result['confidence'] if real_result else 0,
            'strategies': real_result['strategies_used'] if real_result else 0,
            'models': real_result['models_used'] if real_result else 0
        },
        'comparison': {
            'overlap_numbers': list(overlap) if original_result and real_result else [],
            'overlap_rate': overlap_rate if original_result and real_result else 0,
            'is_different': overlap_rate < 80 if original_result and real_result else False
        }
    }
    
    # 保存报告
    with open('prediction_comparison_report.json', 'w', encoding='utf-8') as f:
        json.dump(comparison_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 对比报告已保存: prediction_comparison_report.json")
    
    # 6. 结论
    print(f"\n🎯 测试结论")
    print("=" * 30)
    
    if original_result and real_result:
        if overlap_rate > 80:
            print(f"❌ 原系统疑似使用硬编码（重叠率{overlap_rate:.1f}%）")
            print(f"✅ 真实引擎提供动态预测")
            print(f"📋 建议: 使用真实预测引擎替代原系统")
        else:
            print(f"✅ 两系统预测结果差异明显")
            print(f"📊 重叠率: {overlap_rate:.1f}%")
            print(f"🔍 需要进一步验证预测质量")
    else:
        print(f"⚠️ 测试不完整，请检查系统配置")
    
    return comparison_report

def main():
    """主函数"""
    print("🔧 启动预测系统对比测试...")
    
    try:
        report = compare_prediction_systems()
        
        print(f"\n🎉 对比测试完成!")
        print(f"📄 详细报告: prediction_comparison_report.json")
        
        # 简要总结
        if report['comparison']['is_different']:
            print(f"✅ 确认: 真实预测引擎与原系统结果不同")
            print(f"🎯 重叠率: {report['comparison']['overlap_rate']:.1f}%")
        else:
            print(f"⚠️ 警告: 预测结果重叠率过高")
            print(f"🔍 建议检查硬编码问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
