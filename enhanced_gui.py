import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from three_layer_filter_engine import ThreeLayerFilterEngine
from prediction_engine_adapter import PredictionEngineAdapter
import json
from typing import List, Dict
import threading
from datetime import datetime

class FilterPanel(ttk.LabelFrame):
    """三层筛选面板"""

    def __init__(self, parent, filter_engine: ThreeLayerFilterEngine):
        super().__init__(parent, text="三层筛选系统")
        self.filter_engine = filter_engine
        self.setup_ui()

    def setup_ui(self):
        # 创建筛选按钮
        self.filter_btn = ttk.Button(
            self,
            text="应用三层筛选",
            command=self.apply_filters
        )
        self.filter_btn.pack(pady=5)

        # 创建结果显示区
        self.result_text = tk.Text(self, height=10, width=50)
        self.result_text.pack(pady=5, padx=5, fill=tk.BOTH, expand=True)

        # 创建统计信息显示区
        self.stats_label = ttk.Label(self, text="筛选统计:")
        self.stats_label.pack(pady=5)

    def apply_filters(self):
        """应用三层筛选"""
        try:
            # 获取当前候选号码
            candidates = self.get_current_candidates()
            if not candidates:
                messagebox.showwarning("警告", "没有可用的候选号码")
                return

            # 应用筛选
            results = self.filter_engine.apply_all_filters(candidates)

            # 获取统计信息
            stats = self.filter_engine.get_filter_statistics(candidates)

            # 显示结果
            self.show_results(results, stats)

        except Exception as e:
            messagebox.showerror("错误", f"筛选过程出错: {str(e)}")

    def get_current_candidates(self) -> List[int]:
        """获取当前候选号码"""
        # TODO: 从主界面获取当前候选号码
        return list(range(1, 49))

    def show_results(self, results: Dict[str, List[int]], stats: Dict[str, int]):
        """显示筛选结果和统计信息"""
        self.result_text.delete(1.0, tk.END)

        # 显示统计信息
        self.result_text.insert(tk.END, "=== 筛选统计 ===\n")
        self.result_text.insert(tk.END, f"初始数量: {stats['initial_count']}\n")
        self.result_text.insert(tk.END, f"大筛后: {stats['after_large_filter']}\n")
        self.result_text.insert(tk.END, f"中筛后: {stats['after_medium_filter']}\n")
        self.result_text.insert(tk.END, f"小筛组数: {stats['tail_group_count']}\n")
        self.result_text.insert(tk.END, "\n=== 筛选结果 ===\n")

        # 显示每个尾数组合的结果
        for label, numbers in results.items():
            self.result_text.insert(tk.END, f"{label}: {numbers}\n")

class EnhancedGUI(tk.Tk):
    """增强版GUI主界面"""

    def __init__(self):
        super().__init__()

        self.title("六合彩智能预测系统 V3.0")
        self.geometry("1200x800")

        # 初始化组件
        self.setup_components()
        self.setup_layout()

    def setup_components(self):
        """初始化各个组件"""
        # 创建三层筛选引擎
        self.filter_engine = ThreeLayerFilterEngine()

        # 创建预测引擎适配器
        self.prediction_adapter = PredictionEngineAdapter()

        # 创建筛选面板
        self.filter_panel = FilterPanel(self, self.filter_engine)

        # 创建主预测按钮
        self.predict_btn = ttk.Button(
            self,
            text="开始预测",
            command=self.start_prediction
        )

        # 创建结果显示区
        self.result_display = scrolledtext.ScrolledText(
            self,
            height=15,
            width=60
        )

    def setup_layout(self):
        """设置界面布局"""
        # 设置网格布局
        self.grid_columnconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=1)

        # 放置预测按钮
        self.predict_btn.grid(
            row=0, column=0,
            pady=10, padx=10,
            sticky="ew"
        )

        # 放置筛选面板
        self.filter_panel.grid(
            row=1, column=0,
            columnspan=2,
            pady=10, padx=10,
            sticky="nsew"
        )

        # 放置结果显示区
        self.result_display.grid(
            row=2, column=0,
            columnspan=2,
            pady=10, padx=10,
            sticky="nsew"
        )

    def start_prediction(self):
        """开始预测流程"""
        try:
            # 启动预测线程
            thread = threading.Thread(
                target=self._prediction_thread,
                daemon=True
            )
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"预测失败: {str(e)}")

    def _prediction_thread(self):
        """预测线程"""
        try:
            # 获取当前期号
            current_period = self._get_current_period()

            # 调用预测引擎
            results = self.prediction_adapter.predict(current_period)

            # 应用三层筛选
            filtered_results = self.filter_engine.apply_all_filters(results.get('numbers', []))

            # 更新界面显示
            self.after(0, self._update_display, filtered_results)

        except Exception as e:
            error_msg = str(e)
            self.after(0, lambda msg=error_msg: messagebox.showerror("错误", msg))

    def _get_current_period(self) -> str:
        """获取当前期号"""
        # TODO: 实现期号获取逻辑
        return "2025210"

    def _update_display(self, results: Dict[str, List[int]]):
        """更新结果显示"""
        self.result_display.delete(1.0, tk.END)

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.result_display.insert(tk.END, f"预测时间: {timestamp}\n\n")

        for label, numbers in results.items():
            self.result_display.insert(tk.END, f"{label}:\n")
            self.result_display.insert(tk.END, f"{numbers}\n\n")

def main():
    app = EnhancedGUI()
    app.mainloop()

if __name__ == "__main__":
    main()
