#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维生肖组合配置
包含所有54类生肖组合的定义
"""

# 多维生肖组合定义
ZODIAC_GROUPS = {
    # 基础属性类
    "日肖": {"兔", "龙", "蛇", "马", "羊", "猴"},
    "夜肖": {"鼠", "牛", "虎", "鸡", "狗", "猪"},
    "左肖": {"鼠", "牛", "龙", "蛇", "猴", "鸡"},
    "右肖": {"虎", "兔", "马", "羊", "狗", "猪"},
    "阴肖": {"鼠", "龙", "蛇", "马", "狗", "猪"},
    "阳肖": {"牛", "虎", "兔", "羊", "猴", "鸡"},
    
    # 结构类
    "独肖": {"鼠", "牛", "虎", "兔", "马", "羊"},
    "合肖": {"龙", "蛇", "猴", "鸡", "狗", "猪"},
    "家肖": {"牛", "马", "羊", "狗", "鸡", "猪"},
    "野肖": {"鼠", "虎", "兔", "龙", "蛇", "猴"},
    "天肖": {"牛", "猴", "兔", "猪", "马", "龙"},
    "地肖": {"蛇", "虎", "羊", "鸡", "狗", "鼠"},
    
    # 性别类
    "男肖": {"鼠", "牛", "虎", "龙", "马", "猴", "狗"},
    "女肖": {"兔", "蛇", "羊", "鸡", "猪"},
    
    # 吉凶类
    "吉肖": {"兔", "龙", "蛇", "马", "羊", "鸡"},
    "凶肖": {"鼠", "牛", "虎", "猴", "狗", "猪"},
    
    # 位置类
    "前肖": {"鼠", "牛", "虎", "兔", "龙", "蛇"},
    "后肖": {"马", "羊", "猴", "鸡", "狗", "猪"},
    
    # 笔画类
    "单笔": {"鼠", "龙", "马", "蛇", "鸡", "猪"},
    "双笔": {"虎", "猴", "狗", "兔", "羊", "牛"},
    
    # 性格类
    "胆大": {"牛", "虎", "马", "猴", "狗", "猪"},
    "胆小": {"鼠", "兔", "龙", "蛇", "羊", "鸡"},
    
    # 颜色类
    "红肖": {"马", "兔", "鼠", "鸡"},
    "蓝肖": {"蛇", "虎", "猪", "猴"},
    "绿肖": {"羊", "龙", "牛", "狗"},
    
    # 琴棋书画
    "琴": {"兔", "蛇", "鸡"},
    "棋": {"鼠", "牛", "狗"},
    "书": {"虎", "龙", "马"},
    "画": {"羊", "猴", "猪"},
    
    # 四季类
    "春": {"虎", "兔", "龙"},
    "夏": {"蛇", "马", "羊"},
    "秋": {"猴", "狗", "鸡"},
    "冬": {"鼠", "牛", "猪"},
    
    # 花卉类
    "梅": {"龙", "牛", "狗"},
    "兰": {"兔", "羊", "蛇"},
    "菊": {"鼠", "马", "猪"},
    "竹": {"虎", "鸡", "猴"},
    
    # 军衔类
    "元帅": {"鼠", "虎", "狗"},
    "大将": {"牛", "蛇", "猴"},
    "先锋": {"马", "羊", "鸡"},
    "小兵": {"兔", "龙", "猪"},
    
    # 特殊类
    "五福肖": {"鼠", "虎", "兔", "蛇", "猴"},
    "白边": {"鼠", "牛", "虎", "鸡", "狗", "猪"},
    "黑中": {"兔", "龙", "蛇", "马", "羊", "猴"},
}

# 六合三合组合（特殊处理）
SPECIAL_COMBINATIONS = {
    # 六合组合
    "六合-鼠牛": {"鼠", "牛"},
    "六合-龙鸡": {"龙", "鸡"},
    "六合-虎猪": {"虎", "猪"},
    "六合-蛇猴": {"蛇", "猴"},
    "六合-兔狗": {"兔", "狗"},
    "六合-马羊": {"马", "羊"},
    
    # 三合组合
    "三合-鼠龙猴": {"鼠", "龙", "猴"},
    "三合-牛蛇鸡": {"牛", "蛇", "鸡"},
    "三合-虎马狗": {"虎", "马", "狗"},
    "三合-兔羊猪": {"兔", "羊", "猪"},
}

# 合并所有组合
ALL_ZODIAC_GROUPS = {**ZODIAC_GROUPS, **SPECIAL_COMBINATIONS}

# 组合分类（用于分析报告）
GROUP_CATEGORIES = {
    "基础属性": ["日肖", "夜肖", "左肖", "右肖", "阴肖", "阳肖"],
    "结构特征": ["独肖", "合肖", "家肖", "野肖", "天肖", "地肖"],
    "性别吉凶": ["男肖", "女肖", "吉肖", "凶肖"],
    "位置笔画": ["前肖", "后肖", "单笔", "双笔"],
    "性格颜色": ["胆大", "胆小", "红肖", "蓝肖", "绿肖"],
    "文化元素": ["琴", "棋", "书", "画", "春", "夏", "秋", "冬", "梅", "兰", "菊", "竹"],
    "军衔等级": ["元帅", "大将", "先锋", "小兵"],
    "特殊组合": ["五福肖", "白边", "黑中"],
    "六合三合": [key for key in SPECIAL_COMBINATIONS.keys()]
}

def get_zodiac_groups():
    """获取所有生肖组合"""
    return ALL_ZODIAC_GROUPS

def get_group_categories():
    """获取组合分类"""
    return GROUP_CATEGORIES

def get_groups_for_zodiac(zodiac):
    """获取指定生肖所属的所有组合"""
    groups = []
    for group_name, group_zodiacs in ALL_ZODIAC_GROUPS.items():
        if zodiac in group_zodiacs:
            groups.append(group_name)
    return groups

def validate_zodiac_groups():
    """验证生肖组合配置"""
    all_zodiacs = {"鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"}
    
    print("=== 生肖组合验证 ===")
    print(f"总组合数: {len(ALL_ZODIAC_GROUPS)}")
    
    for group_name, group_zodiacs in ALL_ZODIAC_GROUPS.items():
        # 检查是否包含无效生肖
        invalid_zodiacs = group_zodiacs - all_zodiacs
        if invalid_zodiacs:
            print(f"❌ {group_name}: 包含无效生肖 {invalid_zodiacs}")
        else:
            print(f"✅ {group_name}: {len(group_zodiacs)}个生肖 - {sorted(group_zodiacs)}")
    
    # 统计每个生肖所属组合数
    print("\n=== 生肖分布统计 ===")
    for zodiac in sorted(all_zodiacs):
        groups = get_groups_for_zodiac(zodiac)
        print(f"{zodiac}: 属于{len(groups)}个组合")

if __name__ == "__main__":
    validate_zodiac_groups()
