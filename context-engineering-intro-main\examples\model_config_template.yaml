# Use Open AI with key
provider: autogen_ext.models.openai.OpenAIChatCompletionClient
config:
  model: gpt-4o
  api_key: REPLACE_WITH_YOUR_API_KEY
# Use Azure Open AI with key
# provider: autogen_ext.models.openai.AzureOpenAIChatCompletionClient
# config:
#   model: gpt-4o
#   azure_endpoint: https://{your-custom-endpoint}.openai.azure.com/
#   azure_deployment: {your-azure-deployment}
#   api_version: {your-api-version}
#   api_key: REPLACE_WITH_YOUR_API_KEY
# Use Azure OpenAI with AD token provider.
# provider: autogen_ext.models.openai.AzureOpenAIChatCompletionClient
# config:
#   model: gpt-4o
#   azure_endpoint: https://{your-custom-endpoint}.openai.azure.com/
#   azure_deployment: {your-azure-deployment}
#   api_version: {your-api-version}
#   azure_ad_token_provider:
#     provider: autogen_ext.auth.azure.AzureTokenProvider
#     config:
#       provider_kind: DefaultAzureCredential
#       scopes:
#         - https://cognitiveservices.azure.com/.default
