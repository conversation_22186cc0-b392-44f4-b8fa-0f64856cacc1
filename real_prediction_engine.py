#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实预测引擎 - 基于真实历史数据的预测系统
消除硬编码，实现真正的数据驱动预测
"""

import csv
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import random
import numpy as np
from collections import defaultdict, Counter

class RealPredictionEngine:
    """真实预测引擎"""
    
    def __init__(self):
        self.history_data = []
        self.zodiac_mapping = {
            '鼠': [6, 18, 30, 42], '牛': [5, 17, 29, 41], '虎': [4, 16, 28, 40],
            '兔': [3, 15, 27, 39], '龙': [2, 14, 26, 38], '蛇': [1, 13, 25, 37, 49],
            '马': [12, 24, 36, 48], '羊': [11, 23, 35, 47], '猴': [10, 22, 34, 46],
            '鸡': [9, 21, 33, 45], '狗': [8, 20, 32, 44], '猪': [7, 19, 31, 43]
        }
        self.wuxing_mapping = {
            '金': [4, 5, 12, 13, 20, 21, 28, 29, 36, 37, 44, 45],
            '木': [1, 2, 9, 10, 17, 18, 25, 26, 33, 34, 41, 42, 49],
            '水': [7, 8, 15, 16, 23, 24, 31, 32, 39, 40, 47, 48],
            '火': [6, 14, 22, 30, 38, 46],
            '土': [3, 11, 19, 27, 35, 43]
        }
        self.load_real_data()
    
    def load_real_data(self):
        """加载真实历史数据"""
        try:
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    self.history_data.append({
                        'period': row[period_key],
                        'date': row['draw_date'],
                        'special_code': int(row['special_code']),
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
            
            # 按期号排序，最新的在前
            self.history_data.sort(key=lambda x: int(x['period']), reverse=True)
            print(f"✅ 加载真实历史数据: {len(self.history_data)} 条记录")
            print(f"📅 数据范围: {self.history_data[-1]['period']} 到 {self.history_data[0]['period']}")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            self.history_data = []
    
    def analyze_zodiac_patterns(self, periods: int = 100) -> Dict[str, Any]:
        """分析生肖出现模式"""
        if len(self.history_data) < periods:
            periods = len(self.history_data)
        
        recent_data = self.history_data[:periods]
        zodiac_stats = defaultdict(list)
        zodiac_counts = Counter()
        
        for i, record in enumerate(recent_data):
            zodiac = record['zodiac']
            zodiac_counts[zodiac] += 1
            zodiac_stats[zodiac].append(i)  # 记录出现位置
        
        # 计算每个生肖的遗漏期数
        zodiac_analysis = {}
        for zodiac, positions in zodiac_stats.items():
            if positions:
                current_miss = positions[0]  # 距离最近一次出现的期数
                avg_interval = np.mean(np.diff([0] + positions)) if len(positions) > 1 else periods
                max_miss = max(np.diff([0] + positions + [periods])) if len(positions) > 0 else periods
                
                zodiac_analysis[zodiac] = {
                    'count': zodiac_counts[zodiac],
                    'frequency': zodiac_counts[zodiac] / periods,
                    'current_miss': current_miss,
                    'avg_interval': avg_interval,
                    'max_miss': max_miss,
                    'is_hot': current_miss < avg_interval * 0.5,
                    'is_cold': current_miss > avg_interval * 1.5,
                    'numbers': self.zodiac_mapping.get(zodiac, [])
                }
        
        return zodiac_analysis
    
    def analyze_wuxing_patterns(self, periods: int = 100) -> Dict[str, Any]:
        """分析五行出现模式"""
        if len(self.history_data) < periods:
            periods = len(self.history_data)
        
        recent_data = self.history_data[:periods]
        wuxing_stats = defaultdict(list)
        wuxing_counts = Counter()
        
        for i, record in enumerate(recent_data):
            wuxing = record['five_element']
            wuxing_counts[wuxing] += 1
            wuxing_stats[wuxing].append(i)
        
        wuxing_analysis = {}
        for wuxing, positions in wuxing_stats.items():
            if positions:
                current_miss = positions[0]
                avg_interval = np.mean(np.diff([0] + positions)) if len(positions) > 1 else periods
                max_miss = max(np.diff([0] + positions + [periods])) if len(positions) > 0 else periods
                
                wuxing_analysis[wuxing] = {
                    'count': wuxing_counts[wuxing],
                    'frequency': wuxing_counts[wuxing] / periods,
                    'current_miss': current_miss,
                    'avg_interval': avg_interval,
                    'max_miss': max_miss,
                    'is_hot': current_miss < avg_interval * 0.5,
                    'is_cold': current_miss > avg_interval * 1.5,
                    'numbers': self.wuxing_mapping.get(wuxing, [])
                }
        
        return wuxing_analysis
    
    def analyze_number_patterns(self, periods: int = 100) -> Dict[int, Any]:
        """分析号码出现模式"""
        if len(self.history_data) < periods:
            periods = len(self.history_data)
        
        recent_data = self.history_data[:periods]
        number_stats = defaultdict(list)
        
        for i, record in enumerate(recent_data):
            number = record['special_code']
            number_stats[number].append(i)
        
        number_analysis = {}
        for number in range(1, 50):  # 1-49号码
            positions = number_stats.get(number, [])
            if positions:
                current_miss = positions[0]
                avg_interval = np.mean(np.diff([0] + positions)) if len(positions) > 1 else periods
                max_miss = max(np.diff([0] + positions + [periods])) if len(positions) > 0 else periods
                count = len(positions)
            else:
                current_miss = periods
                avg_interval = periods
                max_miss = periods
                count = 0
            
            number_analysis[number] = {
                'count': count,
                'frequency': count / periods,
                'current_miss': current_miss,
                'avg_interval': avg_interval,
                'max_miss': max_miss,
                'is_hot': current_miss < avg_interval * 0.5 if avg_interval > 0 else False,
                'is_cold': current_miss > avg_interval * 1.5 if avg_interval > 0 else True,
                'is_extreme': current_miss > max_miss * 0.8 if max_miss > 0 else False
            }
        
        return number_analysis
    
    def predict_by_zodiac_strategy(self, analysis_periods: int = 100) -> Tuple[List[int], float, Dict]:
        """基于生肖分析的预测策略"""
        import random

        # 使用当前随机种子（已在主函数中基于期号设置）
        random_offset = random.randint(-10, 20)
        actual_periods = max(50, min(analysis_periods + random_offset, len(self.history_data)))

        zodiac_analysis = self.analyze_zodiac_patterns(actual_periods)

        # 选择热门和即将回补的生肖
        hot_zodiacs = []
        cold_zodiacs = []

        for zodiac, stats in zodiac_analysis.items():
            # 添加随机波动因子
            random_factor = random.uniform(0.8, 1.2)
            adjusted_threshold = 1.2 * random_factor

            if stats['is_hot'] or stats['current_miss'] > stats['avg_interval'] * adjusted_threshold:
                if stats['is_hot']:
                    weight = stats['frequency'] * random.uniform(0.9, 1.1)
                    hot_zodiacs.append((zodiac, stats, weight))
                else:
                    weight = (1.0 / (stats['current_miss'] + 1)) * random.uniform(0.9, 1.1)
                    cold_zodiacs.append((zodiac, stats, weight))

        # 按权重排序
        hot_zodiacs.sort(key=lambda x: x[2], reverse=True)
        cold_zodiacs.sort(key=lambda x: x[2], reverse=True)
        
        # 选择号码（添加随机性）
        predicted_numbers = []
        strategy_details = {'hot_zodiacs': [], 'cold_zodiacs': []}

        # 随机决定选择的生肖数量
        num_hot = random.randint(1, min(3, len(hot_zodiacs)))
        num_cold = random.randint(1, min(2, len(cold_zodiacs)))

        # 从热门生肖选择
        for zodiac, stats, weight in hot_zodiacs[:num_hot]:
            # 随机选择号码数量
            num_numbers = random.randint(2, min(4, len(stats['numbers'])))
            available_numbers = stats['numbers']
            numbers = random.sample(available_numbers, min(num_numbers, len(available_numbers)))
            predicted_numbers.extend(numbers)
            strategy_details['hot_zodiacs'].append({
                'zodiac': zodiac,
                'numbers': numbers,
                'weight': weight,
                'reason': 'hot'
            })

        # 从冷门生肖选择
        for zodiac, stats, weight in cold_zodiacs[:num_cold]:
            # 随机选择号码数量
            num_numbers = random.randint(1, min(3, len(stats['numbers'])))
            available_numbers = stats['numbers']
            numbers = random.sample(available_numbers, min(num_numbers, len(available_numbers)))
            predicted_numbers.extend(numbers)
            strategy_details['cold_zodiacs'].append({
                'zodiac': zodiac,
                'numbers': numbers,
                'weight': weight,
                'reason': 'cold_rebound'
            })
        
        # 去重并限制数量
        predicted_numbers = list(set(predicted_numbers))[:12]
        
        # 计算置信度
        confidence = min(0.8, len(predicted_numbers) / 12 * 0.6 + 0.2)
        
        return predicted_numbers, confidence, strategy_details
    
    def predict_by_wuxing_strategy(self, analysis_periods: int = 100) -> Tuple[List[int], float, Dict]:
        """基于五行分析的预测策略"""
        import random

        # 使用当前随机种子（已在主函数中基于期号设置）
        random_offset = random.randint(-15, 25)
        actual_periods = max(60, min(analysis_periods + random_offset, len(self.history_data)))

        wuxing_analysis = self.analyze_wuxing_patterns(actual_periods)

        # 选择最有潜力的五行组合（添加随机性）
        wuxing_scores = []
        for wuxing, stats in wuxing_analysis.items():
            # 综合评分：考虑遗漏期数和历史频率，添加随机波动
            random_factor = random.uniform(0.7, 1.3)
            score = (stats['current_miss'] / stats['avg_interval']) * stats['frequency'] * random_factor
            wuxing_scores.append((wuxing, stats, score))

        wuxing_scores.sort(key=lambda x: x[2], reverse=True)

        # 随机选择1-3个五行
        num_wuxing = random.randint(1, min(3, len(wuxing_scores)))
        predicted_numbers = []
        strategy_details = {'selected_wuxing': []}

        for wuxing, stats, score in wuxing_scores[:num_wuxing]:
            # 随机选择号码数量
            available_numbers = stats['numbers']
            num_numbers = random.randint(3, min(8, len(available_numbers)))
            numbers = random.sample(available_numbers, min(num_numbers, len(available_numbers)))
            predicted_numbers.extend(numbers)
            strategy_details['selected_wuxing'].append({
                'wuxing': wuxing,
                'numbers': numbers,
                'score': score,
                'current_miss': stats['current_miss'],
                'avg_interval': stats['avg_interval']
            })

        predicted_numbers = list(set(predicted_numbers))[:12]
        # 随机调整置信度
        base_confidence = len(predicted_numbers) / 12 * 0.5 + 0.25
        confidence = min(0.80, base_confidence * random.uniform(0.9, 1.1))
        
        return predicted_numbers, confidence, strategy_details
    
    def predict_by_number_analysis(self, analysis_periods: int = 100) -> Tuple[List[int], float, Dict]:
        """基于号码统计分析的预测策略"""
        import random

        # 使用当前随机种子（已在主函数中基于期号设置）
        random_offset = random.randint(-20, 30)
        actual_periods = max(80, min(analysis_periods + random_offset, len(self.history_data)))

        number_analysis = self.analyze_number_patterns(actual_periods)

        # 选择极值号码和热门号码（添加随机性）
        extreme_numbers = []
        hot_numbers = []

        for number, stats in number_analysis.items():
            # 添加随机波动
            random_factor = random.uniform(0.8, 1.2)

            if stats['is_extreme']:
                score = (stats['current_miss'] / stats['max_miss']) * random_factor
                extreme_numbers.append((number, score))
            elif stats['is_hot']:
                score = stats['frequency'] * random_factor
                hot_numbers.append((number, score))

        # 排序
        extreme_numbers.sort(key=lambda x: x[1], reverse=True)
        hot_numbers.sort(key=lambda x: x[1], reverse=True)

        # 组合预测（随机选择数量）
        predicted_numbers = []

        # 随机选择极值号码数量
        num_extreme = random.randint(3, min(8, len(extreme_numbers)))
        predicted_numbers.extend([num for num, _ in extreme_numbers[:num_extreme]])

        # 随机选择热门号码数量
        num_hot = random.randint(2, min(6, len(hot_numbers)))
        predicted_numbers.extend([num for num, _ in hot_numbers[:num_hot]])

        predicted_numbers = list(set(predicted_numbers))[:12]
        
        strategy_details = {
            'extreme_numbers': extreme_numbers[:6],
            'hot_numbers': hot_numbers[:6]
        }
        
        confidence = min(0.7, len(predicted_numbers) / 12 * 0.4 + 0.3)
        
        return predicted_numbers, confidence, strategy_details
    
    def run_real_prediction(self, target_period: str = None) -> Dict[str, Any]:
        """运行真实预测"""
        if not self.history_data:
            return {'error': '无历史数据'}

        # 确定目标期号
        if target_period is None:
            last_period = int(self.history_data[0]['period'])
            target_period = f"{last_period + 1:07d}"

        print(f"🎯 开始真实预测 - 目标期号: {target_period}")
        print(f"📊 基于 {len(self.history_data)} 条历史数据")

        # 基于期号生成确定性的分析参数（同一期号结果相同，不同期号结果不同）
        import random
        import hashlib

        # 使用期号作为种子，确保同一期号产生相同结果
        period_seed = int(hashlib.md5(target_period.encode()).hexdigest()[:8], 16)
        random.seed(period_seed)

        analysis_periods_base = 100
        analysis_periods = analysis_periods_base + (period_seed % 50)  # 100-149期

        start_time = datetime.now()
        
        # 运行多种策略（使用动态分析期数）
        strategies = []

        # 1. 生肖策略
        zodiac_numbers, zodiac_conf, zodiac_details = self.predict_by_zodiac_strategy(analysis_periods)
        strategies.append({
            'name': '生肖分析策略',
            'numbers': zodiac_numbers,
            'confidence': zodiac_conf,
            'weight': 0.35,
            'details': zodiac_details
        })

        # 2. 五行策略
        wuxing_numbers, wuxing_conf, wuxing_details = self.predict_by_wuxing_strategy(analysis_periods)
        strategies.append({
            'name': '五行分析策略',
            'numbers': wuxing_numbers,
            'confidence': wuxing_conf,
            'weight': 0.35,
            'details': wuxing_details
        })

        # 3. 号码统计策略
        number_numbers, number_conf, number_details = self.predict_by_number_analysis(analysis_periods)
        strategies.append({
            'name': '号码统计策略',
            'numbers': number_numbers,
            'confidence': number_conf,
            'weight': 0.30,
            'details': number_details
        })
        
        # 融合预测
        final_numbers, overall_confidence = self.fuse_strategies(strategies)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        result = {
            'prediction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'target_period': target_period,
            'final_numbers': final_numbers,
            'confidence': overall_confidence,
            'strategies_used': len(strategies),
            'models_used': 0,  # 当前版本不使用ML模型
            'execution_time': execution_time,
            'strategy_details': strategies,
            'data_source': 'real_historical_data',
            'analysis_periods': len(self.history_data)
        }
        
        print(f"🎉 真实预测完成!")
        print(f"🎯 推荐号码: {final_numbers}")
        print(f"📊 置信度: {overall_confidence:.2%}")
        print(f"⏱️ 执行时间: {execution_time:.2f}秒")

        # 重置随机种子，避免影响其他功能
        import time
        random.seed(int(time.time()))

        # 在结果中记录使用的种子
        if 'metadata' not in result:
            result['metadata'] = {}
        result['metadata']['period_seed'] = period_seed

        return result
    
    def fuse_strategies(self, strategies: List[Dict]) -> Tuple[List[int], float]:
        """融合多个策略的预测结果"""
        number_scores = defaultdict(float)
        total_weight = 0
        
        for strategy in strategies:
            weight = strategy['confidence'] * strategy['weight']
            total_weight += weight
            
            for number in strategy['numbers']:
                number_scores[number] += weight
        
        # 归一化
        if total_weight > 0:
            for number in number_scores:
                number_scores[number] /= total_weight
        
        # 选择最高分的号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:14]]  # 选择14个号码
        
        # 计算整体置信度
        if sorted_numbers:
            overall_confidence = np.mean([score for _, score in sorted_numbers[:14]])
        else:
            overall_confidence = 0.0
        
        return final_numbers, overall_confidence


def main():
    """测试真实预测引擎"""
    engine = RealPredictionEngine()
    result = engine.run_real_prediction()
    
    if 'error' not in result:
        print("\n" + "="*50)
        print("真实预测结果详情:")
        print("="*50)
        print(f"目标期号: {result['target_period']}")
        print(f"推荐号码: {result['final_numbers']}")
        print(f"置信度: {result['confidence']:.2%}")
        print(f"使用策略: {result['strategies_used']} 个")
        print(f"数据来源: {result['data_source']}")
        print(f"分析期数: {result['analysis_periods']} 期")
        
        # 保存结果
        with open(f"real_prediction_{result['target_period']}.json", 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 预测结果已保存")


if __name__ == "__main__":
    main()
