@echo off
echo 启动六合彩预测系统GUI界面...
echo ================================

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 尝试使用py命令...
    py --version
    if %errorlevel% neq 0 (
        echo 错误: 未找到Python环境
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo.
echo 尝试启动简化GUI界面...
%PYTHON_CMD% simple_gui.py

if %errorlevel% neq 0 (
    echo.
    echo GUI启动失败，尝试启动Web界面...
    %PYTHON_CMD% web_gui.py
)

if %errorlevel% neq 0 (
    echo.
    echo 所有GUI启动失败，打开演示页面...
    start prediction_demo.html
)

pause
