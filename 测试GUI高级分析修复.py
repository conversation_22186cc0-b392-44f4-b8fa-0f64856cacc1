#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI高级分析修复 - 验证GUI中的高级分析功能是否完全修复
"""

def test_gui_advanced_analysis():
    """测试GUI高级分析功能"""
    print("🎯 测试GUI高级分析功能修复")
    print("=" * 60)
    
    try:
        # 模拟GUI的完整调用流程
        print("1️⃣ 模拟GUI初始化...")
        from prediction_engine_adapter import PredictionEngineAdapter
        
        # 创建适配器（模拟GUI中的self.prediction_engine）
        prediction_engine = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        print("\n2️⃣ 模拟GUI调用高级分析...")
        # 模拟GUI中的调用：self.prediction_engine.get_advanced_zodiac_analysis()
        analysis_result = prediction_engine.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 高级分析失败:")
            print("   错误信息:", analysis_result['error'])
            return False
        
        print("✅ 高级分析调用成功")
        
        print("\n3️⃣ 模拟GUI数据处理...")
        
        # 模拟GUI中的数据处理
        candidates = analysis_result['candidates']
        system_status = analysis_result['system_status']
        top_recommendations = analysis_result['top_recommendations']
        energy_analysis = analysis_result['energy_analysis']
        
        print("   候选组合数量:", len(candidates))
        print("   系统状态:", system_status.get('status', 'unknown'))
        print("   推荐组合数量:", len(top_recommendations))
        
        print("\n4️⃣ 模拟GUI显示更新...")
        
        # 模拟update_candidates_display中的格式化
        for i, candidate in enumerate(candidates[:3]):
            try:
                members_str = ", ".join(candidate['members'])
                z_score_str = "{:.2f}".format(candidate['z_score'])
                strength_str = "{:.1%}".format(candidate['recommendation_strength'])
                
                print("   候选组合", i+1, ":")
                print("     成员:", members_str)
                print("     Z-Score:", z_score_str)
                print("     推荐强度:", strength_str)
                
            except Exception as e:
                print("   ❌ 候选组合", i+1, "格式化失败:", str(e))
                return False
        
        print("\n5️⃣ 模拟GUI状态更新...")
        
        # 模拟update_advanced_status中的格式化
        try:
            total_groups = system_status.get('total_groups', 0)
            total_groups_str = "{:,}".format(total_groups)
            
            active_candidates = system_status.get('active_candidates', 0)
            
            avg_z_score = system_status.get('avg_z_score', 0)
            avg_z_score_str = "{:.3f}".format(avg_z_score)
            
            max_z_score = system_status.get('max_z_score', 0)
            max_z_score_str = "{:.3f}".format(max_z_score)
            
            print("   总组数:", total_groups_str)
            print("   活跃候选:", str(active_candidates))
            print("   平均Z-Score:", avg_z_score_str)
            print("   最大Z-Score:", max_z_score_str)
            
        except Exception as e:
            print("   ❌ 状态更新格式化失败:", str(e))
            return False
        
        print("\n6️⃣ 测试报告生成...")
        
        # 测试报告生成功能
        try:
            report = prediction_engine.generate_advanced_report()
            if "报告生成失败" in report:
                print("   ❌ 报告生成失败")
                return False
            else:
                print("   ✅ 报告生成成功，长度:", len(report), "字符")
        except Exception as e:
            print("   ❌ 报告生成异常:", str(e))
            return False
        
        print("\n🎉 所有GUI高级分析功能测试通过！")
        return True
        
    except Exception as e:
        print("❌ 测试过程中出现异常:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        
        import traceback
        print("\n详细错误:")
        traceback.print_exc()
        
        return False

def test_multiple_calls():
    """测试多次调用的稳定性"""
    print("\n🔄 测试多次调用稳定性")
    print("-" * 40)
    
    try:
        from prediction_engine_adapter import PredictionEngineAdapter
        adapter = PredictionEngineAdapter()
        
        success_count = 0
        total_calls = 5
        
        for i in range(total_calls):
            try:
                result = adapter.get_advanced_zodiac_analysis()
                if 'error' not in result:
                    success_count += 1
                    print("   ✅ 第", i+1, "次调用成功")
                else:
                    print("   ❌ 第", i+1, "次调用失败:", result['error'])
            except Exception as e:
                print("   ❌ 第", i+1, "次调用异常:", str(e))
        
        success_rate = success_count / total_calls
        print("\n   成功率:", "{:.1%}".format(success_rate))
        
        return success_rate >= 0.8  # 80%以上成功率认为通过
        
    except Exception as e:
        print("   ❌ 稳定性测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动GUI高级分析修复验证...")
    
    # 运行测试
    gui_test_result = test_gui_advanced_analysis()
    stability_test_result = test_multiple_calls()
    
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    
    print("GUI高级分析功能:", "✅ 通过" if gui_test_result else "❌ 失败")
    print("多次调用稳定性:", "✅ 通过" if stability_test_result else "❌ 失败")
    
    overall_success = gui_test_result and stability_test_result
    
    print("\n🎯 总体结果:", "✅ 完全成功" if overall_success else "❌ 存在问题")
    
    if overall_success:
        print("\n🎉 恭喜！GUI高级分析功能已完全修复")
        print("✅ 错误 'unsupported format string passed to dict.format' 已彻底解决")
        print("✅ GUI中的所有格式化问题已修复")
        print("✅ 高级分析功能可以正常使用")
        print("✅ 多次调用稳定可靠")
        
        print("\n💡 现在可以安全使用:")
        print("   - start_lottery_gui.bat 启动GUI")
        print("   - GUI中的高级分析功能")
        print("   - 所有候选组合显示")
        print("   - 系统状态更新")
        print("   - 报告生成功能")
        
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("请检查上述错误信息并进行相应修复")
    
    return overall_success

if __name__ == "__main__":
    main()
