@echo off
title Lottery Prediction System Launcher
color 0A

echo.
echo ================================================
echo    Lottery Prediction System v2.0
echo    Professional GUI with REAL Prediction Engine
echo ================================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check Python environment
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] 'python' command not available, trying 'py'...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Python not found!
        echo.
        echo Please install Python 3.7+ and add to PATH
        echo Download: https://www.python.org/downloads/
        echo.
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo [OK] Python environment found
%PYTHON_CMD% --version

:: Check core files
echo.
echo [INFO] Checking core files...

if not exist "gui_main_v2.py" (
    echo [ERROR] Missing file: gui_main_v2.py
    goto :missing_files
)

if not exist "prediction_engine.py" (
    echo [ERROR] Missing file: prediction_engine.py
    goto :missing_files
)

if not exist "prediction_engine_adapter.py" (
    echo [ERROR] Missing file: prediction_engine_adapter.py
    goto :missing_files
)

if not exist "real_prediction_engine.py" (
    echo [ERROR] Missing file: real_prediction_engine.py
    goto :missing_files
)

if not exist "lottery_data.db" (
    echo [ERROR] Missing file: lottery_data.db
    goto :missing_files
)

if not exist "lottery_data_20250717.csv" (
    echo [ERROR] Missing file: lottery_data_20250717.csv
    goto :missing_files
)

echo [OK] All core files found

:: Check Python dependencies
echo.
echo [INFO] Checking Python dependencies...

%PYTHON_CMD% -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] tkinter module not available
    echo Please reinstall Python with tkinter support
    pause
    exit /b 1
)
echo [OK] tkinter available

:: Optional dependencies
%PYTHON_CMD% -c "import yaml" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] PyYAML not installed, installing...
    %PYTHON_CMD% -m pip install PyYAML
)

%PYTHON_CMD% -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] pandas not installed, installing...
    %PYTHON_CMD% -m pip install pandas
)

echo [OK] Dependencies check complete

:: Skip adapter test for now - start GUI directly
echo [INFO] Starting GUI directly...

:: Start GUI
echo.
echo [INFO] Starting GUI interface...
echo.
echo ================================================
echo  ENHANCED GUI Features:
echo  - Same familiar interface you know
echo  - NOW uses REAL prediction engine internally
echo  - NO hardcoded numbers anymore
echo  - Based on 1940 real historical records
echo  - Different results each time
echo  - All original functions work normally
echo ================================================
echo.

%PYTHON_CMD% gui_main_v2.py

:: Check startup result
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] GUI failed to start (Error: %errorlevel%)
    echo [INFO] Please check the error message above

    echo [ERROR] All GUI startup attempts failed
    echo.
    echo Troubleshooting suggestions:
    echo 1. Check if Python is properly installed
    echo 2. Ensure all required files are present
    echo 3. Try running as administrator
    echo 4. Check system compatibility
    echo.
    pause
    exit /b 1
) else (
    echo [OK] GUI program exited normally
)

goto :end

:missing_files
echo.
echo [ERROR] Core files missing!
echo Please ensure all system files are in the same directory
echo.
pause
exit /b 1

:end
echo.
echo [INFO] Program completed
pause
exit /b 0
