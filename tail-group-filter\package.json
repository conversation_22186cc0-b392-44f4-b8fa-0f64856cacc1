{"name": "tail-group-filter", "version": "1.0.0", "description": "A module that generates and integrates tail number combinations for a lottery prediction system.", "main": "src/index.ts", "scripts": {"test": "jest", "build": "tsc"}, "dependencies": {"typescript": "^4.0.0", "jest": "^26.0.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "eslint": "^7.0.0"}, "keywords": ["lottery", "tail numbers", "prediction", "filter"], "author": "Your Name", "license": "MIT"}