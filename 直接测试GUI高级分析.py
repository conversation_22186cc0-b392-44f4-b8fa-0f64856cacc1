#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试GUI高级分析 - 模拟GUI中的确切调用
"""

import traceback

def test_exact_gui_call():
    """测试GUI中的确切调用"""
    print("🎯 测试GUI中的确切高级分析调用")
    print("=" * 60)
    
    try:
        # 完全模拟GUI中的调用方式
        print("1️⃣ 导入并初始化适配器...")
        from prediction_engine_adapter import PredictionEngineAdapter
        
        # 模拟GUI中的self.prediction_engine
        prediction_engine = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        print("\n2️⃣ 模拟GUI中的阈值设置...")
        # 模拟GUI中第1334行的操作
        new_threshold = 2.0  # 模拟zscore_threshold_var.get()
        prediction_engine.advanced_zodiac_engine.z_score_threshold = new_threshold
        print("✅ 阈值设置成功:", new_threshold)
        
        print("\n3️⃣ 模拟GUI中的高级分析调用...")
        # 模拟GUI中第1337行的操作
        analysis_result = prediction_engine.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 高级分析失败:", analysis_result['error'])
            return False
        
        print("✅ 高级分析调用成功")
        
        print("\n4️⃣ 模拟GUI中的结果处理...")
        # 模拟GUI中update_candidates_display的操作
        candidates = analysis_result['candidates']
        system_status = analysis_result['system_status']
        
        print("   候选组合数量:", len(candidates))
        print("   系统状态:", system_status.get('status', 'unknown'))
        
        # 模拟GUI中的具体格式化操作
        print("\n5️⃣ 模拟GUI中的格式化操作...")
        
        for i, candidate in enumerate(candidates[:3]):
            try:
                # 完全按照GUI中的方式格式化
                members_str = ", ".join(candidate['members'])
                z_score_str = "{:.2f}".format(candidate['z_score'])
                strength_str = "{:.1%}".format(candidate['recommendation_strength'])
                
                # 模拟GUI中的树形控件插入
                values = (
                    candidate['rank'],
                    members_str,
                    z_score_str,
                    candidate['current_miss'],
                    candidate['urgency_level'],
                    strength_str
                )
                
                print("   候选组合", i+1, ":", values)
                
            except Exception as e:
                print("   ❌ 候选组合", i+1, "格式化失败:", str(e))
                traceback.print_exc()
                return False
        
        print("\n6️⃣ 模拟GUI中的状态更新...")
        # 模拟update_advanced_status的操作
        try:
            total_groups = system_status.get('total_groups', 0)
            total_groups_str = "{:,}".format(total_groups)
            
            active_candidates = system_status.get('active_candidates', 0)
            
            avg_z_score = system_status.get('avg_z_score', 0)
            avg_z_score_str = "{:.3f}".format(avg_z_score)
            
            max_z_score = system_status.get('max_z_score', 0)
            max_z_score_str = "{:.3f}".format(max_z_score)
            
            print("   总组数:", total_groups_str)
            print("   活跃候选:", str(active_candidates))
            print("   平均Z-Score:", avg_z_score_str)
            print("   最大Z-Score:", max_z_score_str)
            
        except Exception as e:
            print("   ❌ 状态更新失败:", str(e))
            traceback.print_exc()
            return False
        
        print("\n🎉 所有GUI操作模拟成功！")
        return True
        
    except Exception as e:
        print("❌ GUI操作模拟失败:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        print("\n详细错误堆栈:")
        traceback.print_exc()
        return False

def test_with_error_catching():
    """带错误捕获的测试"""
    print("\n🔧 带错误捕获的测试")
    print("-" * 40)
    
    try:
        from prediction_engine_adapter import PredictionEngineAdapter
        adapter = PredictionEngineAdapter()
        
        # 尝试多种可能的调用方式
        test_methods = [
            ("基本调用", lambda: adapter.get_advanced_zodiac_analysis()),
            ("设置阈值后调用", lambda: (
                setattr(adapter.advanced_zodiac_engine, 'z_score_threshold', 2.0),
                adapter.get_advanced_zodiac_analysis()
            )[1]),
            ("报告生成", lambda: adapter.generate_advanced_report()),
        ]
        
        for method_name, method_call in test_methods:
            try:
                print("   测试", method_name, "...")
                result = method_call()
                
                if isinstance(result, dict) and 'error' in result:
                    print("   ❌", method_name, "返回错误:", result['error'])
                    return False
                else:
                    print("   ✅", method_name, "成功")
                    
            except Exception as e:
                print("   ❌", method_name, "异常:", str(e))
                traceback.print_exc()
                return False
        
        print("✅ 所有方法测试通过")
        return True
        
    except Exception as e:
        print("❌ 错误捕获测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动GUI高级分析直接测试...")
    
    # 运行测试
    test1_result = test_exact_gui_call()
    test2_result = test_with_error_catching()
    
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    
    print("GUI确切调用测试:", "✅ 通过" if test1_result else "❌ 失败")
    print("错误捕获测试:", "✅ 通过" if test2_result else "❌ 失败")
    
    overall_success = test1_result and test2_result
    
    print("\n🎯 总体结果:", "✅ 完全成功" if overall_success else "❌ 存在问题")
    
    if overall_success:
        print("\n🎉 恭喜！GUI高级分析调用完全正常")
        print("✅ 所有格式化操作都成功")
        print("✅ 适配器与GUI完美兼容")
        print("✅ 错误应该已经解决")
        
        print("\n💡 如果GUI中仍然出现错误，可能的原因:")
        print("1. GUI初始化时的特定时机问题")
        print("2. 其他依赖文件中的格式化问题")
        print("3. tkinter环境相关的问题")
        
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("请检查上述错误信息")
    
    return overall_success

if __name__ == "__main__":
    main()
