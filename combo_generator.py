from itertools import combinations, product

class ComboGenerator:
    def __init__(self):
        self.shengxiao = ["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"]
        self.wuxing = ["金","木","水","火","土"]
        self.sebo = ["红波", "绿波", "蓝波"]
        self.shengxiao_tags = {
            "左肖": ["鼠","牛","龙","蛇","马","羊"],
            "右肖": ["虎","兔","猴","鸡","狗","猪"],
            "吉肖": ["马","羊","猴","鸡","狗","猪"],
            "凶肖": ["鼠","牛","虎","兔","龙","蛇"],
            "阳肖": ["虎","马","龙","猴","狗","鸡"],
            "阴肖": ["鼠","牛","兔","蛇","羊","猪"]
        }

    def generate_shengxiao_4(self):
        """Generates 4-zodiac combinations."""
        return [{"type": "生肖4组合", "key": ",".join(c), "members": list(c)} for c in combinations(self.shengxiao, 4)]

    def generate_wuxing_2(self):
        """Generates 2-element combinations."""
        return [{"type": "五行2组合", "key": "+".join(c), "members": list(c)} for c in combinations(self.wuxing, 2)]

    def generate_sebo(self):
        """Generates color wave combinations."""
        return [
            {"type": "色波组合", "key": "红绿波", "members": ["红波", "绿波"]},
            {"type": "色波组合", "key": "红蓝波", "members": ["红波", "蓝波"]},
            {"type": "色波组合", "key": "蓝绿波", "members": ["蓝波", "绿波"]}
        ]

    def generate_shengxiao_tag_combos(self):
        """Generates multi-dimensional zodiac tag combinations."""
        tag_keys = list(self.shengxiao_tags.keys())
        combos = combinations(tag_keys, 2)
        combo_list = []
        for a, b in combos:
            members = list(set(self.shengxiao_tags[a]) | set(self.shengxiao_tags[b]))
            combo_list.append({
                "type": "多维生肖组合",
                "key": f"{a}+{b}",
                "members": members
            })
        return combo_list

if __name__ == '__main__':
    import json

    generator = ComboGenerator()

    wuxing_2_combos = generator.generate_wuxing_2()
    sebo_combos = generator.generate_sebo()
    shengxiao_tag_combos = generator.generate_shengxiao_tag_combos()

    all_combos = {
        "wuxing_2": wuxing_2_combos,
        "sebo": sebo_combos,
        "shengxiao_tags": shengxiao_tag_combos
    }

    with open("combos.json", "w", encoding="utf-8") as f:
        json.dump(all_combos, f, ensure_ascii=False, indent=2)

    print("Generated combos and saved to combos.json")
    print(f"Total Wuxing 2-combos: {len(wuxing_2_combos)}")
    print(f"Total Sebo combos: {len(sebo_combos)}")
    print(f"Total Shengxiao Tag combos: {len(shengxiao_tag_combos)}")
