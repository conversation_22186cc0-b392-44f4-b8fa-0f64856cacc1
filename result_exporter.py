#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果导出器
支持预测结果和回测结果的多格式导出
"""

import json
import csv
from datetime import datetime
from typing import Dict, Any, List
from dataclasses import asdict

class ResultExporter:
    """结果导出器"""
    
    def __init__(self):
        pass
    
    def export_backtest_result(self, result: Dict[str, Any], format: str = 'txt', 
                              filename: str = None) -> str:
        """导出回测结果"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtest_result_{timestamp}"
        
        if format.lower() == 'txt':
            filepath = f"{filename}.txt"
            with open(filepath, 'w', encoding='utf-8') as f:
                report = self._generate_backtest_txt_report(result)
                f.write(report)
        
        elif format.lower() == 'json':
            filepath = f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == 'csv':
            filepath = f"{filename}.csv"
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入基本信息
                writer.writerow(['指标', '数值'])
                writer.writerow(['命中率', f"{result.get('hit_rate', 0):.2%}"])
                writer.writerow(['命中次数', result.get('hit_count', 0)])
                writer.writerow(['总期数', result.get('total_periods', 0)])
                writer.writerow(['平均遗漏', f"{result.get('avg_miss_interval', 0):.2f}"])
                writer.writerow(['最大遗漏', result.get('max_miss_streak', 0)])
                writer.writerow(['夏普比率', f"{result.get('sharpe_ratio', 0):.4f}"])
                
                # 写入详细结果
                if 'details' in result and result['details']:
                    writer.writerow([])  # 空行
                    writer.writerow(['期号', '预测号码', '实际号码', '是否命中', '命中排名'])
                    
                    for detail in result['details']:
                        predicted_str = ', '.join(f"{n:02d}" for n in detail.get('predicted', []))
                        writer.writerow([
                            detail.get('period', ''),
                            predicted_str,
                            f"{detail.get('actual', ''):02d}" if detail.get('actual') else '',
                            '是' if detail.get('is_hit', False) else '否',
                            detail.get('hit_rank', '') or ''
                        ])
        
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        print(f"回测结果已导出到: {filepath}")
        return filepath
    
    def _generate_backtest_txt_report(self, result: Dict[str, Any]) -> str:
        """生成回测结果的TXT报告"""
        report = f"""
六合彩智能预测系统 - 回测分析报告
{'='*70}

回测概况:
  分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
  回测策略: 统一预测引擎
  分析方法: 历史数据验证

核心指标:
{'='*70}
  命中率: {result.get('hit_rate', 0):.2%}
  命中次数: {result.get('hit_count', 0)} 次
  总期数: {result.get('total_periods', 0)} 期
  平均遗漏: {result.get('avg_miss_interval', 0):.2f} 期
  最大遗漏: {result.get('max_miss_streak', 0)} 期
  夏普比率: {result.get('sharpe_ratio', 0):.4f}

性能评估:
{'='*70}
"""
        
        # 性能评估
        hit_rate = result.get('hit_rate', 0)
        if hit_rate >= 0.3:
            performance = "优秀"
            comment = "命中率较高，策略表现良好"
        elif hit_rate >= 0.2:
            performance = "良好"
            comment = "命中率中等，有一定参考价值"
        elif hit_rate >= 0.1:
            performance = "一般"
            comment = "命中率偏低，需要优化策略"
        else:
            performance = "较差"
            comment = "命中率很低，建议重新调整策略"
        
        report += f"""
  整体表现: {performance}
  评估结果: {comment}
  
  风险水平: {"低" if result.get('sharpe_ratio', 0) > 0.5 else "中" if result.get('sharpe_ratio', 0) > 0 else "高"}
  稳定性: {"稳定" if result.get('max_miss_streak', 0) < 10 else "中等" if result.get('max_miss_streak', 0) < 20 else "不稳定"}
"""
        
        # 详细结果
        if 'details' in result and result['details']:
            report += f"""
详细回测结果 (最近{len(result['details'])}期):
{'='*70}
"""
            
            for i, detail in enumerate(result['details'], 1):
                period = detail.get('period', '')
                predicted = detail.get('predicted', [])
                actual = detail.get('actual', '')
                is_hit = detail.get('is_hit', False)
                hit_rank = detail.get('hit_rank', '')
                
                predicted_str = ', '.join(f"{n:02d}" for n in predicted[:8])  # 只显示前8个
                status = "✓ 命中" if is_hit else "✗ 未中"
                rank_str = f" (第{hit_rank}位)" if hit_rank else ""
                
                report += f"""
{i:2d}. 期号: {period}
    预测: [{predicted_str}...]
    实际: {actual:02d}
    结果: {status}{rank_str}
"""
        
        # 统计分析
        if 'details' in result and result['details']:
            hit_details = [d for d in result['details'] if d.get('is_hit', False)]
            if hit_details:
                hit_ranks = [d.get('hit_rank', 0) for d in hit_details if d.get('hit_rank')]
                if hit_ranks:
                    avg_rank = sum(hit_ranks) / len(hit_ranks)
                    report += f"""
命中分析:
{'='*70}
  命中期数: {len(hit_details)} 期
  平均命中排名: {avg_rank:.1f} 位
  最佳命中排名: {min(hit_ranks)} 位
  最差命中排名: {max(hit_ranks)} 位
"""
        
        report += f"""
技术说明:
{'='*70}
  回测方法: 历史数据交叉验证
  评估指标: 命中率、遗漏分析、风险评估
  数据来源: 历史开奖记录
  
  命中率计算: 命中次数 / 总期数
  夏普比率: 风险调整后的收益指标
  遗漏分析: 连续未命中的期数统计

免责声明:
{'='*70}
  本回测结果基于历史数据分析，不代表未来表现。
  彩票具有随机性，任何预测都存在不确定性。
  请理性对待预测结果，量力而行。

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本: 六合彩智能预测系统 v1.0
        """
        
        return report
    
    def export_prediction_result(self, result, format: str = 'txt', filename: str = None) -> str:
        """导出预测结果 (兼容旧接口)"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"prediction_{result.target_period}_{timestamp}"
        
        if format.lower() == 'txt':
            filepath = f"{filename}.txt"
            with open(filepath, 'w', encoding='utf-8') as f:
                report = self._generate_prediction_txt_report(result)
                f.write(report)
        
        elif format.lower() == 'json':
            filepath = f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                result_dict = asdict(result) if hasattr(result, '__dataclass_fields__') else result
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == 'csv':
            filepath = f"{filename}.csv"
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['预测期号', '预测号码', '置信度', '预测时间'])
                writer.writerow([
                    result.target_period,
                    ','.join(map(str, result.final_numbers)),
                    f"{result.confidence_score:.4f}",
                    result.prediction_date
                ])
        
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        print(f"预测结果已导出到: {filepath}")
        return filepath
    
    def _generate_prediction_txt_report(self, result) -> str:
        """生成预测结果的TXT报告"""
        report = f"""
六合彩智能预测系统 - 预测报告
{'='*60}

预测信息:
  目标期号: {result.target_period}
  预测时间: {result.prediction_date}
  执行时间: {result.execution_time:.2f} 秒
  融合方法: {result.fusion_method}

预测结果:
  推荐号码: {' '.join(f'{num:02d}' for num in result.final_numbers)}
  置信度: {result.confidence_score:.2%}
  使用策略: {result.total_strategies_used} 个
  使用模型: {result.total_models_used} 个

策略详情:
{'='*60}
"""
        
        for i, strategy in enumerate(result.strategy_details, 1):
            strategy_name = strategy.get('strategy_name', 'Unknown')
            predicted_numbers = strategy.get('predicted_numbers', [])
            confidence = strategy.get('confidence', 0)
            
            numbers_str = ' '.join(f'{num:02d}' for num in predicted_numbers)
            
            report += f"""
{i}. {strategy_name}
   推荐号码: {numbers_str}
   置信度: {confidence:.4f}
"""
        
        if result.model_details:
            report += f"""
机器学习模型详情:
{'='*60}
"""
            for i, model in enumerate(result.model_details, 1):
                model_name = model.get('model_name', 'Unknown').replace('_', ' ').title()
                predicted_numbers = model.get('predicted_numbers', [])
                confidence = model.get('confidence', 0)
                
                numbers_str = ' '.join(f'{num:02d}' for num in predicted_numbers)
                
                report += f"""
{i}. {model_name}
   推荐号码: {numbers_str}
   置信度: {confidence:.4f}
"""
        
        report += f"""
系统信息:
{'='*60}
  系统版本: 六合彩智能预测系统 v1.0
  技术栈: Python + scikit-learn + 多维策略融合
  数据来源: 历史开奖数据分析
  
风险提示:
{'='*60}
  本预测结果仅供参考，不构成投资建议。
  彩票具有随机性，请理性参与，量力而行。
  
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return report

if __name__ == "__main__":
    # 测试导出功能
    exporter = ResultExporter()
    
    # 测试回测结果导出
    test_backtest_result = {
        'hit_rate': 0.25,
        'hit_count': 25,
        'total_periods': 100,
        'avg_miss_interval': 3.2,
        'max_miss_streak': 8,
        'sharpe_ratio': 0.45,
        'details': [
            {'period': '2025190', 'predicted': [1, 13, 25, 37], 'actual': 13, 'is_hit': True, 'hit_rank': 2},
            {'period': '2025191', 'predicted': [2, 14, 26, 38], 'actual': 45, 'is_hit': False, 'hit_rank': None}
        ]
    }
    
    print("测试回测结果导出...")
    exporter.export_backtest_result(test_backtest_result, 'txt', 'test_backtest')
    print("导出完成！")
