#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证34号的生肖映射
"""

from data_attributes import DataAttributeMapper

def quick_verify():
    """快速验证"""
    print("🔍 快速验证34号生肖映射")
    print("="*40)
    
    mapper = DataAttributeMapper()
    
    # 验证34号
    attrs_34 = mapper.map_all_attributes(34)
    print(f"34号属性:")
    print(f"  生肖: {attrs_34['zodiac']}")
    print(f"  五行: {attrs_34['five_element']}")
    print(f"  波色: {attrs_34['wave_color']}")
    print(f"  大小: {attrs_34['big_small']}")
    print(f"  单双: {attrs_34['odd_even']}")
    
    # 查找猴对应的号码
    print(f"\n🐒 猴对应的号码:")
    monkey_numbers = []
    for num in range(1, 50):
        attrs = mapper.map_all_attributes(num)
        if attrs['zodiac'] == '猴':
            monkey_numbers.append(num)
    
    print(f"  猴的号码: {monkey_numbers}")
    
    # 查找牛蛇羊猴对应的所有号码
    print(f"\n📋 牛蛇羊猴对应的所有号码:")
    target_zodiacs = ['牛', '蛇', '羊', '猴']
    
    for zodiac in target_zodiacs:
        numbers = []
        for num in range(1, 50):
            attrs = mapper.map_all_attributes(num)
            if attrs['zodiac'] == zodiac:
                numbers.append(num)
        print(f"  {zodiac}: {numbers}")

if __name__ == "__main__":
    quick_verify()
