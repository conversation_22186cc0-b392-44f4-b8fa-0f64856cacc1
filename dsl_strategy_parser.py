#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DSL策略解析器
支持YAML格式的策略配置解析与执行
"""

import yaml
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class StrategyConfig:
    """策略配置数据类"""
    id: str
    name: str
    type: str
    source: str
    mode: str
    filters: Dict[str, Any]
    scoring: Dict[str, float]
    active: bool
    weight: float = 1.0

@dataclass
class FusionConfig:
    """融合配置数据类"""
    strategy_weighting: str = "manual"
    max_numbers: int = 14
    method: str = "weighted_union"
    fallback: str = "high_score_first"

class DSLStrategyParser:
    """DSL策略解析器"""
    
    def __init__(self, config_file: str = "strategy_config.yaml"):
        self.config_file = config_file
        self.strategies: List[StrategyConfig] = []
        self.fusion_config: FusionConfig = FusionConfig()
        self.load_config()
    
    def load_config(self):
        """加载策略配置文件"""
        try:
            # 在加载前清空现有策略列表，防止重复
            self.strategies = []
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 解析策略配置
            if 'strategies' in config_data:
                for strategy_data in config_data['strategies']:
                    strategy = StrategyConfig(
                        id=strategy_data.get('id', ''),
                        name=strategy_data.get('name', ''),
                        type=strategy_data.get('type', ''),
                        source=strategy_data.get('source', ''),
                        mode=strategy_data.get('mode', ''),
                        filters=strategy_data.get('filters', {}),
                        scoring=strategy_data.get('scoring', {}),
                        active=strategy_data.get('active', True),
                        weight=strategy_data.get('weight', 1.0)
                    )
                    self.strategies.append(strategy)
            
            # 解析融合配置
            if 'fusion' in config_data:
                fusion_data = config_data['fusion']
                self.fusion_config = FusionConfig(
                    strategy_weighting=fusion_data.get('strategy_weighting', 'manual'),
                    max_numbers=fusion_data.get('max_numbers', 14),
                    method=fusion_data.get('method', 'weighted_union'),
                    fallback=fusion_data.get('fallback', 'high_score_first')
                )
                
        except FileNotFoundError:
            print(f"配置文件 {self.config_file} 不存在，使用默认配置")
            self._create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        default_strategies = [
            {
                'id': 'wuxing_2_hot',
                'name': '五行2组合热门策略',
                'type': '五行',
                'source': 'wuxing_combos',
                'mode': '高频追踪',
                'filters': {
                    'min_hit_rate': 0.3,
                    'max_current_miss': 5
                },
                'scoring': {
                    'hit_weight': 0.8,
                    'omit_weight': 0.2
                },
                'active': True,
                'weight': 0.6
            }
        ]
        
        for strategy_data in default_strategies:
            strategy = StrategyConfig(**strategy_data)
            self.strategies.append(strategy)
    
    def get_active_strategies(self) -> List[StrategyConfig]:
        """获取激活的策略"""
        return [s for s in self.strategies if s.active]
    
    def filter_strategies(self, combo_data: Dict[str, Any]) -> List[StrategyConfig]:
        """根据过滤条件筛选策略"""
        filtered_strategies = []
        
        for strategy in self.get_active_strategies():
            if self._apply_filters(strategy, combo_data):
                filtered_strategies.append(strategy)
        
        return filtered_strategies
    
    def _apply_filters(self, strategy: StrategyConfig, combo_data: Dict[str, Any]) -> bool:
        """应用过滤条件"""
        filters = strategy.filters
        
        # 极限遗漏过滤
        if 'max_omit_threshold' in filters:
            threshold = filters['max_omit_threshold']
            max_miss = combo_data.get('max_miss', 0)
            current_miss = combo_data.get('current_miss', 0)
            
            if max_miss > 0 and current_miss < (max_miss * threshold):
                return False
        
        # 最小命中次数过滤
        if 'min_hit_count' in filters:
            min_hits = filters['min_hit_count']
            hit_count = combo_data.get('hit_count', 0)
            
            if hit_count < min_hits:
                return False
        
        # 最小命中率过滤
        if 'min_hit_rate' in filters:
            min_rate = filters['min_hit_rate']
            hit_rate = combo_data.get('hit_rate', 0)
            
            if hit_rate < min_rate:
                return False
        
        # 最大当前遗漏过滤
        if 'max_current_miss' in filters:
            max_miss = filters['max_current_miss']
            current_miss = combo_data.get('current_miss', 0)
            
            if current_miss > max_miss:
                return False
        
        return True
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'strategies': [],
            'fusion': {
                'strategy_weighting': self.fusion_config.strategy_weighting,
                'max_numbers': self.fusion_config.max_numbers,
                'method': self.fusion_config.method,
                'fallback': self.fusion_config.fallback
            }
        }
        
        for strategy in self.strategies:
            strategy_data = {
                'id': strategy.id,
                'name': strategy.name,
                'type': strategy.type,
                'source': strategy.source,
                'mode': strategy.mode,
                'filters': strategy.filters,
                'scoring': strategy.scoring,
                'active': strategy.active,
                'weight': strategy.weight
            }
            config_data['strategies'].append(strategy_data)
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            print(f"配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def update_strategy_weight(self, strategy_id: str, weight: float):
        """更新策略权重"""
        for strategy in self.strategies:
            if strategy.id == strategy_id:
                strategy.weight = weight
                break
    
    def toggle_strategy(self, strategy_id: str, active: bool):
        """切换策略激活状态"""
        for strategy in self.strategies:
            if strategy.id == strategy_id:
                strategy.active = active
                break
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """获取策略摘要"""
        active_count = len(self.get_active_strategies())
        total_count = len(self.strategies)
        
        return {
            'total_strategies': total_count,
            'active_strategies': active_count,
            'fusion_method': self.fusion_config.method,
            'max_output_numbers': self.fusion_config.max_numbers,
            'strategies': [
                {
                    'id': s.id,
                    'name': s.name,
                    'type': s.type,
                    'active': s.active,
                    'weight': s.weight
                } for s in self.strategies
            ]
        }

if __name__ == "__main__":
    # 测试DSL解析器
    parser = DSLStrategyParser()
    
    # 显示策略摘要
    summary = parser.get_strategy_summary()
    print("=== 策略配置摘要 ===")
    print(json.dumps(summary, ensure_ascii=False, indent=2))
    
    # 测试过滤功能
    test_combo_data = {
        'hit_count': 15,
        'current_miss': 8,
        'max_miss': 10,
        'hit_rate': 0.35
    }
    
    filtered = parser.filter_strategies(test_combo_data)
    print(f"\n=== 过滤后的策略 ===")
    for strategy in filtered:
        print(f"- {strategy.name} (权重: {strategy.weight})")
    
    # 保存配置
    parser.save_config()
