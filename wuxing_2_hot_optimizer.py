#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wuxing_2_hot优化器 - 将硬编码改为真实的五行分析算法
"""

from combo_generator import ComboGenerator
from data_attributes import HistoryDataManager
from dynamic_mapping_system import DynamicMappingSystem
from collections import defaultdict
from typing import List, Dict, Any, Tuple

class WuXing2HotOptimizer:
    """五行2组合热门策略优化器"""
    
    def __init__(self):
        self.combo_generator = ComboGenerator()
        self.data_manager = HistoryDataManager()
        self.mapping_system = DynamicMappingSystem()
        
        # 策略参数
        self.recent_periods = 50  # 分析最近50期
        self.hot_threshold = 0.3  # 热门阈值 (降低以获得更多候选)
        self.min_hit_rate = 0.15  # 最小命中率 (降低门槛)
        self.max_miss_periods = 15  # 最大遗漏期数 (增加容忍度)
        
    def analyze_wuxing_combinations(self, period_number: str) -> Dict[str, Any]:
        """分析五行组合的热门度"""
        print("🔥 开始五行2组合热门分析...")
        
        # 1. 生成五行组合
        wuxing_combos = self.combo_generator.generate_wuxing_2()
        print(f"   生成 {len(wuxing_combos)} 个五行2组合")
        
        # 2. 加载历史数据
        historical_data = self.data_manager.load_data()
        if not historical_data:
            print("❌ 历史数据加载失败")
            return self._get_fallback_result()
        
        print(f"   加载 {len(historical_data)} 条历史记录")
        
        # 3. 分析每个组合的表现
        combo_analysis = {}
        
        for combo in wuxing_combos:
            combo_key = combo['key']
            combo_members = combo['members']
            
            analysis = self._analyze_single_combo(
                combo_members, historical_data, period_number
            )
            combo_analysis[combo_key] = analysis
        
        # 4. 筛选热门组合
        hot_combos = self._filter_hot_combos(combo_analysis)
        
        # 5. 生成预测号码
        predicted_numbers = self._generate_numbers_from_hot_combos(
            hot_combos, period_number
        )
        
        return {
            'combo_analysis': combo_analysis,
            'hot_combos': hot_combos,
            'predicted_numbers': predicted_numbers,
            'analysis_summary': self._generate_summary(combo_analysis, hot_combos)
        }
    
    def _analyze_single_combo(self, combo_members: List[str], 
                             historical_data: List[Dict], 
                             current_period: str) -> Dict[str, Any]:
        """分析单个五行组合的表现"""
        
        # 统计命中情况
        hit_count = 0
        miss_count = 0
        current_miss_streak = 0
        last_hit_period = None
        hit_periods = []
        
        # 分析最近的期数
        recent_data = historical_data[-self.recent_periods:] if len(historical_data) > self.recent_periods else historical_data
        
        for record in recent_data:
            period = record.get('period_number', '')
            special_code = record.get('special_code', 0)
            
            if special_code:
                # 获取特码的五行属性
                try:
                    wuxing = self.mapping_system.get_wuxing_for_number(special_code, period)
                    
                    # 检查是否命中组合
                    if wuxing in combo_members:
                        hit_count += 1
                        hit_periods.append(period)
                        last_hit_period = period
                        current_miss_streak = 0
                    else:
                        miss_count += 1
                        current_miss_streak += 1
                        
                except Exception as e:
                    miss_count += 1
                    current_miss_streak += 1
        
        # 计算统计指标
        total_periods = len(recent_data)
        hit_rate = hit_count / total_periods if total_periods > 0 else 0
        
        # 计算热门度评分
        hot_score = self._calculate_hot_score(hit_rate, current_miss_streak, hit_count)
        
        return {
            'hit_count': hit_count,
            'miss_count': miss_count,
            'hit_rate': hit_rate,
            'current_miss_streak': current_miss_streak,
            'last_hit_period': last_hit_period,
            'hit_periods': hit_periods,
            'hot_score': hot_score,
            'total_analyzed': total_periods
        }
    
    def _calculate_hot_score(self, hit_rate: float, current_miss: int, hit_count: int) -> float:
        """计算热门度评分"""
        
        # 基础评分 = 命中率
        base_score = hit_rate
        
        # 遗漏调整：当前遗漏越短，热门度越高
        miss_factor = max(0, 1 - (current_miss / self.max_miss_periods))
        
        # 命中次数调整：命中次数越多，越稳定
        hit_factor = min(1.0, hit_count / 10)  # 10次命中为满分
        
        # 综合评分
        hot_score = (base_score * 0.5 + miss_factor * 0.3 + hit_factor * 0.2)
        
        return round(hot_score, 3)
    
    def _filter_hot_combos(self, combo_analysis: Dict[str, Any]) -> List[Tuple[str, Dict]]:
        """筛选热门组合"""
        
        hot_combos = []
        
        for combo_key, analysis in combo_analysis.items():
            hit_rate = analysis['hit_rate']
            current_miss = analysis['current_miss_streak']
            hot_score = analysis['hot_score']
            
            # 应用过滤条件
            if (hit_rate >= self.min_hit_rate and 
                current_miss <= self.max_miss_periods and
                hot_score >= self.hot_threshold):
                
                hot_combos.append((combo_key, analysis))
        
        # 按热门度评分排序
        hot_combos.sort(key=lambda x: x[1]['hot_score'], reverse=True)
        
        return hot_combos
    
    def _generate_numbers_from_hot_combos(self, hot_combos: List[Tuple[str, Dict]], 
                                        period_number: str) -> List[int]:
        """从热门组合生成预测号码"""
        
        if not hot_combos:
            # 如果没有热门组合，返回平衡的号码
            return self._generate_balanced_numbers()
        
        # 获取最热门的组合
        top_combo = hot_combos[0]
        combo_key = top_combo[0]
        
        # 解析组合成员
        combo_members = combo_key.replace('五行2组合 - ', '').split('+')
        
        # 为每个五行元素生成对应的号码
        predicted_numbers = []
        
        for wuxing in combo_members:
            numbers = self._get_numbers_for_wuxing(wuxing, period_number)
            predicted_numbers.extend(numbers)
        
        # 去重并排序
        predicted_numbers = sorted(list(set(predicted_numbers)))
        
        # 确保有8个号码
        while len(predicted_numbers) < 8:
            # 添加补充号码
            supplement = self._generate_supplement_numbers(predicted_numbers)
            predicted_numbers.extend(supplement)
            predicted_numbers = sorted(list(set(predicted_numbers)))
        
        return predicted_numbers[:8]
    
    def _get_numbers_for_wuxing(self, wuxing: str, period_number: str) -> List[int]:
        """获取指定五行的号码（确定性版本）"""

        # 五行号码映射 (基于传统对应关系)
        wuxing_numbers = {
            '金': [2, 3, 10, 11, 18, 19, 26, 27, 34, 35, 42, 43],
            '木': [1, 8, 9, 16, 17, 24, 25, 32, 33, 40, 41, 48],
            '水': [6, 7, 14, 15, 22, 23, 30, 31, 38, 39, 46, 47],
            '火': [4, 5, 12, 13, 20, 21, 28, 29, 36, 37, 44, 45],
            '土': [49, 48, 47, 46]  # 扩展土的号码
        }

        base_numbers = wuxing_numbers.get(wuxing, [])

        # 确定性选择：根据五行特性选择最具代表性的4个号码
        if len(base_numbers) >= 4:
            # 选择策略：小号2个 + 大号2个，确保平衡
            small_numbers = [n for n in base_numbers if n <= 25]
            big_numbers = [n for n in base_numbers if n > 25]

            selected = []
            # 选择最小的2个小号
            if len(small_numbers) >= 2:
                selected.extend(sorted(small_numbers)[:2])
            else:
                selected.extend(small_numbers)

            # 选择最小的2个大号
            if len(big_numbers) >= 2:
                selected.extend(sorted(big_numbers)[:2])
            else:
                selected.extend(big_numbers)

            # 如果不足4个，补充剩余的
            while len(selected) < 4 and len(selected) < len(base_numbers):
                remaining = [n for n in base_numbers if n not in selected]
                if remaining:
                    selected.append(min(remaining))
                else:
                    break

            return sorted(selected[:4])
        else:
            return base_numbers
    
    def _generate_balanced_numbers(self) -> List[int]:
        """生成平衡的号码组合（确定性版本）"""

        # 确保奇偶平衡、大小平衡
        small_odd = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]
        small_even = [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24]
        big_odd = [25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49]
        big_even = [26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48]

        # 确定性选择：选择每组的前2个
        numbers = []
        numbers.extend(small_odd[:2])   # [1, 3]
        numbers.extend(small_even[:2])  # [2, 4]
        numbers.extend(big_odd[:2])     # [25, 27]
        numbers.extend(big_even[:2])    # [26, 28]

        return sorted(numbers)
    
    def _generate_supplement_numbers(self, existing: List[int]) -> List[int]:
        """生成补充号码（确定性版本）"""

        all_numbers = list(range(1, 50))
        available = [n for n in all_numbers if n not in existing]

        if available:
            # 确定性选择：选择最小的2个可用号码
            return sorted(available)[:min(2, len(available))]
        else:
            return []
    
    def _generate_summary(self, combo_analysis: Dict, hot_combos: List) -> str:
        """生成分析摘要"""
        
        total_combos = len(combo_analysis)
        hot_count = len(hot_combos)
        
        summary = f"五行2组合分析摘要:\n"
        summary += f"- 总组合数: {total_combos}\n"
        summary += f"- 热门组合: {hot_count}\n"
        
        if hot_combos:
            top_combo = hot_combos[0]
            summary += f"- 最热组合: {top_combo[0]}\n"
            summary += f"- 热门评分: {top_combo[1]['hot_score']}\n"
            summary += f"- 命中率: {top_combo[1]['hit_rate']:.1%}\n"
        
        return summary
    
    def _get_fallback_result(self) -> Dict[str, Any]:
        """获取备用结果"""
        
        return {
            'combo_analysis': {},
            'hot_combos': [],
            'predicted_numbers': [5, 10, 15, 17, 20, 24, 38, 41],  # 原始号码作为备用
            'analysis_summary': "数据加载失败，使用备用预测"
        }

def test_optimizer():
    """测试优化器"""
    print("🧪 测试wuxing_2_hot优化器")
    print("="*50)
    
    optimizer = WuXing2HotOptimizer()
    
    # 运行分析
    result = optimizer.analyze_wuxing_combinations("2025199")
    
    print(f"✅ 分析完成")
    print(f"📊 预测号码: {result['predicted_numbers']}")
    print(f"📋 分析摘要:\n{result['analysis_summary']}")
    
    # 显示热门组合
    hot_combos = result['hot_combos']
    if hot_combos:
        print(f"\n🔥 热门组合排行:")
        for i, (combo_key, analysis) in enumerate(hot_combos[:5], 1):
            print(f"   {i}. {combo_key}")
            print(f"      热门评分: {analysis['hot_score']}")
            print(f"      命中率: {analysis['hit_rate']:.1%}")
            print(f"      当前遗漏: {analysis['current_miss_streak']} 期")
    else:
        print(f"\n⚠️ 当前无热门组合")

if __name__ == "__main__":
    test_optimizer()
