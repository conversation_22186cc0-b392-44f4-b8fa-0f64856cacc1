#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索系统中所有与wuxing相关的文件和内容
"""

import os
import glob

def search_wuxing_files():
    """搜索wuxing相关文件"""
    print("🔍 搜索wuxing相关文件")
    print("="*50)
    
    # 搜索文件名包含wuxing的文件
    wuxing_files = []
    
    # 搜索Python文件
    for pattern in ['*wuxing*.py', '*五行*.py']:
        files = glob.glob(pattern)
        wuxing_files.extend(files)
    
    print(f"📁 包含wuxing的文件:")
    if wuxing_files:
        for file in wuxing_files:
            print(f"   - {file}")
    else:
        print("   ❌ 未找到包含wuxing的文件名")
    
    # 搜索文件内容包含wuxing_2_hot的文件
    print(f"\n📄 内容包含'wuxing_2_hot'的文件:")
    
    python_files = glob.glob('*.py')
    found_files = []
    
    for file in python_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'wuxing_2_hot' in content:
                    found_files.append(file)
                    
                    # 统计出现次数
                    count = content.count('wuxing_2_hot')
                    print(f"   - {file}: {count} 次")
        except Exception as e:
            continue
    
    if not found_files:
        print("   ❌ 未找到内容包含wuxing_2_hot的文件")
    
    # 搜索可能的模组文件
    print(f"\n🔍 搜索可能的五行模组文件:")
    
    possible_patterns = [
        '*five_element*.py',
        '*wuxing*.py', 
        '*五行*.py',
        '*element*.py'
    ]
    
    module_files = []
    for pattern in possible_patterns:
        files = glob.glob(pattern)
        module_files.extend(files)
    
    # 去重
    module_files = list(set(module_files))
    
    if module_files:
        for file in module_files:
            print(f"   - {file}")
            
            # 检查文件内容
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查是否包含预测相关的方法
                prediction_keywords = ['predict', '预测', 'hot', 'analysis', '分析']
                found_keywords = []
                
                for keyword in prediction_keywords:
                    if keyword in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"     关键词: {', '.join(found_keywords)}")
                    
            except Exception as e:
                print(f"     ❌ 读取失败: {e}")
    else:
        print("   ❌ 未找到可能的五行模组文件")

def check_original_prediction():
    """检查原始预测号码的来源"""
    print(f"\n🎯 检查原始预测号码来源")
    print("="*50)
    
    original_numbers = [5, 10, 15, 17, 20, 24, 38, 41]
    current_numbers = [2, 8, 13, 19, 24, 30, 35, 41]
    
    print(f"原始号码: {original_numbers}")
    print(f"当前号码: {current_numbers}")
    
    # 搜索这些号码在代码中的出现
    python_files = glob.glob('*.py')
    
    print(f"\n🔍 搜索原始号码在代码中的出现:")
    
    for file in python_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含原始号码
            original_str = str(original_numbers).replace(' ', '')
            if original_str in content.replace(' ', ''):
                print(f"✅ 在 {file} 中找到原始号码")
                
                # 显示相关行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if '5' in line and '10' in line and '15' in line:
                        print(f"   第{i}行: {line.strip()}")
                        
        except Exception as e:
            continue
    
    print(f"\n🔍 搜索当前号码在代码中的出现:")
    
    for file in python_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含当前号码
            current_str = str(current_numbers).replace(' ', '')
            if current_str in content.replace(' ', ''):
                print(f"✅ 在 {file} 中找到当前号码")
                
                # 显示相关行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if '2' in line and '8' in line and '13' in line:
                        print(f"   第{i}行: {line.strip()}")
                        
        except Exception as e:
            continue

def main():
    """主搜索流程"""
    print("🔍 搜索wuxing_2_hot模组")
    print("="*60)
    
    # 搜索wuxing相关文件
    search_wuxing_files()
    
    # 检查原始预测号码来源
    check_original_prediction()
    
    print(f"\n🎯 搜索结论:")
    print("1. 系统中没有独立的wuxing_2_hot.py文件")
    print("2. wuxing_2_hot只是strategy_config.yaml中的一个策略ID")
    print("3. 真正的五行分析逻辑在combo_generator.py和extreme_stat_tracker.py中")
    print("4. 原始预测号码[5,10,15,17,20,24,38,41]可能来自:")
    print("   - 旧版本的代码")
    print("   - 其他独立的模组")
    print("   - 手动设置的测试数据")

if __name__ == "__main__":
    main()
