#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Dict, List
import json
from datetime import datetime

from tail_group_filter import TailGroupFilter

class TailFilterGUI:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("尾数组合预测系统")
        self.root.geometry("800x600")

        # 初始化筛选引擎
        self.filter = TailGroupFilter()

        # 初始化变量
        self.input_numbers = tk.StringVar()

        # 创建界面
        self.create_ui()

    def create_ui(self):
        """创建用户界面"""
        # 输入区域
        input_frame = ttk.LabelFrame(self.root, text="输入区域")
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(input_frame, text="输入号码(用逗号分隔):").pack(side=tk.LEFT, padx=5)
        ttk.Entry(input_frame, textvariable=self.input_numbers, width=40).pack(side=tk.LEFT, padx=5)
        ttk.Button(input_frame, text="开始筛选", command=self.start_filter).pack(side=tk.LEFT, padx=5)

        # 结果展示区
        result_frame = ttk.LabelFrame(self.root, text="筛选结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.result_text = scrolledtext.ScrolledText(result_frame, height=20)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 历史记录区
        history_frame = ttk.LabelFrame(self.root, text="历史记录")
        history_frame.pack(fill=tk.X, padx=5, pady=5)

        self.history_text = scrolledtext.ScrolledText(history_frame, height=8)
        self.history_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def start_filter(self):
        """开始筛选"""
        try:
            # 获取输入号码
            numbers_str = self.input_numbers.get().strip()
            if not numbers_str:
                messagebox.showerror("错误", "请输入待筛选号码")
                return

            # 解析号码
            try:
                numbers = [int(n.strip()) for n in numbers_str.split(",")]
                # 验证号码范围
                if any(n < 1 or n > 48 for n in numbers):
                    raise ValueError("号码必须在1-48范围内")
            except ValueError as e:
                messagebox.showerror("错误", f"号码格式错误: {str(e)}")
                return

            # 开始筛选
            self.log_message(f"开始筛选 {len(numbers)} 个号码...")
            results = self.filter.filter_candidates(numbers)

            # 显示结果
            self.show_results(results)

            # 分析热门尾数组合
            hot_groups = self.filter.get_hot_groups(numbers, top_n=5)
            self.show_hot_groups(hot_groups)

        except Exception as e:
            self.log_message(f"筛选出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def show_results(self, results: Dict[str, List[int]]):
        """显示筛选结果"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "=== 尾数组合筛选结果 ===\n\n")

        for label, nums in results.items():
            self.result_text.insert(tk.END, f"{label}: {nums}\n")
            if nums:  # 如果有匹配的号码
                self.result_text.insert(tk.END, f"共 {len(nums)} 个号码\n")
            self.result_text.insert(tk.END, "\n")

    def show_hot_groups(self, hot_groups: List[tuple]):
        """显示热门尾数组合"""
        self.result_text.insert(tk.END, "\n=== 热门尾数组合 ===\n\n")

        for label, stats in hot_groups:
            self.result_text.insert(tk.END,
                f"{label}:\n"
                f"- 命中次数: {stats['hits']}\n"
                f"- 命中率: {stats['hit_rate']:.2%}\n"
                f"- 匹配号码数: {stats['matched_count']}\n\n"
            )

    def log_message(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.history_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.history_text.see(tk.END)

    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    app = TailFilterGUI()
    app.run()

if __name__ == "__main__":
    main()
