import json
import pickle
import numpy as np
from dynamic_mapping_system import DynamicMappingSystem
from data_attributes import ZodiacGroup

class AdvancedBacktestEngine:
    def __init__(self, combos_path='combos.json', history_path='lottery_data_20250717.csv', cache_path='zodiac_data_calibrated.pkl'):
        self.combos_path = combos_path
        self.history_path = history_path
        self.cache_path = cache_path
        self.mapper = DynamicMappingSystem()
        self.zodiac_groups = []

    def run_calibration(self, force_recalibrate=False):
        """Main method to run the full calibration process."""
        if not force_recalibrate:
            if self._load_from_cache():
                print("Successfully loaded calibrated data from cache.")
                return
            else:
                print("Cache not found. Starting full calibration...")
        
        self._initialize_groups()
        history_data = self._load_history()
        self._run_dual_layer_backtest(history_data)
        self._calculate_statistical_metrics()
        self._save_to_cache()
        print("Calibration complete and data saved to cache.")

    def _load_from_cache(self):
        try:
            with open(self.cache_path, 'rb') as f:
                self.zodiac_groups = pickle.load(f)
            return True
        except FileNotFoundError:
            return False

    def _save_to_cache(self):
        with open(self.cache_path, 'wb') as f:
            pickle.dump(self.zodiac_groups, f)

    def _initialize_groups(self):
        with open(self.combos_path, 'r', encoding='utf-8') as f:
            combos_data = json.load(f)
        self.zodiac_groups = [ZodiacGroup(**data) for data in combos_data]

    def _load_history(self):
        try:
            import pandas as pd
        except ImportError:
            print("Pandas library is not installed. Please install it with 'pip install pandas'")
            return []
        
        try:
            # Assuming the CSV has columns 'year' and 'winning_number'
            data = pd.read_csv(self.history_path)
            # Ensure the required columns are present
            if 'year' not in data.columns or 'winning_number' not in data.columns:
                raise ValueError("CSV file must contain 'year' and 'winning_number' columns.")
            return data.to_dict('records')
        except FileNotFoundError:
            print(f"Error: History file not found at {self.history_path}")
            return []

    def _run_dual_layer_backtest(self, history_data):
        print(f"Running backtest on {len(history_data)} historical records...")
        for record in history_data:
            year = record['year']
            winning_number = record['winning_number']
            winning_zodiac = self.mapper.get_zodiac(winning_number, year)

            for group in self.zodiac_groups:
                for subgroup_key, stats in group.stats.items():
                    subgroup_members = group.subgroups[subgroup_key]
                    
                    # Macro-level update (subgroup)
                    if winning_zodiac in subgroup_members:
                        stats.miss_history.append(stats.current_miss)
                        if stats.current_miss > stats.max_miss:
                            stats.max_miss = stats.current_miss
                        stats.current_miss = 0
                    else:
                        stats.current_miss += 1

                    # Micro-level update (internal members)
                    for member in stats.internal_misses.keys():
                        if member == winning_zodiac:
                            stats.internal_misses[member] = 0
                        else:
                            stats.internal_misses[member] += 1
        print("Backtest finished.")

    def _calculate_statistical_metrics(self):
        print("Calculating statistical metrics...")
        for group in self.zodiac_groups:
            for subgroup_key, stats in group.stats.items():
                if len(stats.miss_history) > 1:
                    stats.miss_mean = np.mean(stats.miss_history)
                    stats.miss_std_dev = np.std(stats.miss_history)
                    if stats.miss_std_dev == 0: # Avoid division by zero
                        stats.miss_std_dev = 1.0
        print("Statistical metrics calculated.")

if __name__ == '__main__':
    engine = AdvancedBacktestEngine()
    engine.run_calibration(force_recalibrate=True)
