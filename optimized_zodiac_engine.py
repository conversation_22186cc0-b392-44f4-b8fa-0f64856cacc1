#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的高级生肖引擎
实现预计算+缓存+增量更新机制
"""

import pickle
import os
import time
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
from itertools import combinations
from dataclasses import dataclass, asdict
from dynamic_mapping_system import DynamicMappingSystem

@dataclass
class OptimizedZodiacGroup:
    """优化的生肖小组数据结构"""
    group_id: str
    members: List[str]
    current_miss: int = 0
    max_miss: int = 0
    miss_history: List[int] = None
    internal_misses: Dict[str, int] = None
    miss_mean: float = 0.0
    miss_std_dev: float = 0.0
    z_score: float = 0.0
    last_updated_period: str = ""
    hit_count: int = 0
    
    def __post_init__(self):
        if self.miss_history is None:
            self.miss_history = []
        if self.internal_misses is None:
            self.internal_misses = {member: 0 for member in self.members}

class OptimizedZodiacEngine:
    """优化的高级生肖引擎"""
    
    def __init__(self, cache_file: str = "zodiac_cache.pkl"):
        self.cache_file = cache_file
        self.zodiac_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        self.all_groups: Dict[str, OptimizedZodiacGroup] = {}
        self.z_score_threshold = 2.0
        self.min_history_length = 20
        self.dynamic_mapper = DynamicMappingSystem()
        self.last_processed_period = ""
        
        # 尝试加载缓存，否则初始化
        if not self.load_cache():
            self._initialize_from_scratch()
    
    def load_cache(self) -> bool:
        """加载缓存文件"""
        if not os.path.exists(self.cache_file):
            print("📁 缓存文件不存在，需要初始化")
            return False
        
        try:
            print("📁 加载缓存文件...")
            start_time = time.time()
            
            with open(self.cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            self.all_groups = cache_data.get('groups', {})
            self.last_processed_period = cache_data.get('last_period', '')
            
            load_time = time.time() - start_time
            print(f"✅ 缓存加载完成: {len(self.all_groups):,} 个小组, 用时 {load_time:.2f} 秒")
            print(f"📊 最后处理期号: {self.last_processed_period}")
            
            return True
            
        except Exception as e:
            print(f"❌ 缓存加载失败: {e}")
            return False
    
    def save_cache(self):
        """保存缓存文件"""
        try:
            print("💾 保存缓存文件...")
            start_time = time.time()
            
            cache_data = {
                'groups': self.all_groups,
                'last_period': self.last_processed_period,
                'cache_time': datetime.now().isoformat(),
                'total_groups': len(self.all_groups)
            }
            
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            save_time = time.time() - start_time
            print(f"✅ 缓存保存完成, 用时 {save_time:.2f} 秒")
            
        except Exception as e:
            print(f"❌ 缓存保存失败: {e}")
    
    def _initialize_from_scratch(self):
        """从头初始化（仅在首次运行时）"""
        print("🔧 首次初始化，生成全策略空间...")
        start_time = time.time()
        
        # 生成所有4-4-4分区
        all_4_combinations = list(combinations(self.zodiac_list, 4))
        partition_count = 0
        
        for i, group_a in enumerate(all_4_combinations):
            remaining_after_a = [z for z in self.zodiac_list if z not in group_a]
            
            for group_b in combinations(remaining_after_a, 4):
                group_c = tuple(z for z in remaining_after_a if z not in group_b)
                
                partition_id = f"P{partition_count:04d}"
                
                for group_idx, group_members in enumerate([group_a, group_b, group_c]):
                    group_id = f"{partition_id}-{chr(65+group_idx)}"
                    
                    self.all_groups[group_id] = OptimizedZodiacGroup(
                        group_id=group_id,
                        members=list(group_members)
                    )
                
                partition_count += 1
        
        init_time = time.time() - start_time
        print(f"✅ 初始化完成: {partition_count} 个分区, {len(self.all_groups)} 个小组, 用时 {init_time:.2f} 秒")
    
    def incremental_update(self, new_history_data: List[Dict]) -> int:
        """增量更新（只处理新数据）"""
        if not new_history_data:
            return 0
        
        print(f"🔄 增量更新: {len(new_history_data)} 期新数据")
        start_time = time.time()
        
        updated_count = 0
        
        for period_data in new_history_data:
            period = period_data.get('period', '')
            special_code = period_data.get('special_code')
            
            if not period or not special_code:
                continue
            
            # 跳过已处理的期号
            if self.last_processed_period and period <= self.last_processed_period:
                continue
            
            # 使用动态映射获取生肖
            zodiac = self.dynamic_mapper.get_zodiac_for_number(special_code, period)
            if not zodiac or zodiac == "未知":
                continue
            
            # 只更新包含该生肖的小组（约8,662个，而不是全部103,950个）
            affected_groups = 0
            for group in self.all_groups.values():
                if zodiac in group.members:
                    # 命中：记录遗漏历史，清零当前遗漏
                    if group.current_miss > 0:
                        group.miss_history.append(group.current_miss)
                        group.max_miss = max(group.max_miss, group.current_miss)
                    
                    group.current_miss = 0
                    group.internal_misses[zodiac] = 0
                    group.hit_count += 1
                    affected_groups += 1
                else:
                    # 未命中：遗漏+1
                    group.current_miss += 1
                
                # 更新组内其他生肖遗漏
                for member in group.members:
                    if member != zodiac:
                        group.internal_misses[member] += 1
                
                group.last_updated_period = period
            
            self.last_processed_period = period
            updated_count += 1
            
            if updated_count % 10 == 0:
                print(f"   处理进度: {updated_count}/{len(new_history_data)}")
        
        # 重新计算统计指标
        self._recalculate_statistics()
        
        update_time = time.time() - start_time
        print(f"✅ 增量更新完成: {updated_count} 期, 用时 {update_time:.2f} 秒")
        
        return updated_count
    
    def _recalculate_statistics(self):
        """重新计算统计指标"""
        for group in self.all_groups.values():
            if len(group.miss_history) >= self.min_history_length:
                group.miss_mean = np.mean(group.miss_history)
                group.miss_std_dev = np.std(group.miss_history)
                
                # 计算Z-Score
                if group.miss_std_dev > 0:
                    group.z_score = (group.current_miss - group.miss_mean) / group.miss_std_dev
                else:
                    group.z_score = 0
            else:
                # 数据不足，使用默认值
                group.miss_mean = 5.0
                group.miss_std_dev = 2.0
                group.z_score = 0
    
    def find_candidates_fast(self, threshold: float = None) -> List[Dict[str, Any]]:
        """快速查找候选小组"""
        if threshold is None:
            threshold = self.z_score_threshold
        
        candidates = []
        
        for group in self.all_groups.values():
            if group.z_score >= threshold:
                # 组内能量分析
                energy_analysis = sorted(
                    group.internal_misses.items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                
                # 确定紧迫程度
                if group.z_score >= 3.0:
                    urgency = "极高"
                    strength = 0.9
                elif group.z_score >= 2.5:
                    urgency = "高"
                    strength = 0.7
                else:
                    urgency = "中等"
                    strength = 0.5
                
                candidates.append({
                    'group_id': group.group_id,
                    'members': group.members,
                    'z_score': group.z_score,
                    'current_miss': group.current_miss,
                    'urgency_level': urgency,
                    'recommendation_strength': strength,
                    'internal_energy': dict(energy_analysis),
                    'hit_count': group.hit_count,
                    'max_miss': group.max_miss
                })
        
        # 按Z-Score排序
        candidates.sort(key=lambda x: x['z_score'], reverse=True)
        
        return candidates
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        total_groups = len(self.all_groups)
        active_candidates = len([g for g in self.all_groups.values() if g.z_score >= self.z_score_threshold])
        
        if self.all_groups:
            z_scores = [g.z_score for g in self.all_groups.values()]
            avg_z_score = np.mean(z_scores)
            max_z_score = np.max(z_scores)
        else:
            avg_z_score = 0
            max_z_score = 0
        
        return {
            "total_groups": total_groups,
            "active_candidates": active_candidates,
            "avg_z_score": avg_z_score,
            "max_z_score": max_z_score,
            "threshold": self.z_score_threshold,
            "last_processed": self.last_processed_period,
            "cache_available": os.path.exists(self.cache_file)
        }
    
    def force_full_rebuild(self, history_data: List[Dict]):
        """强制完全重建（仅在必要时使用）"""
        print("🔄 强制完全重建缓存...")
        start_time = time.time()
        
        # 重置所有小组
        for group in self.all_groups.values():
            group.current_miss = 0
            group.miss_history = []
            group.max_miss = 0
            group.internal_misses = {member: 0 for member in group.members}
            group.hit_count = 0
            group.last_updated_period = ""
        
        # 处理所有历史数据
        self.incremental_update(history_data)
        
        # 保存缓存
        self.save_cache()
        
        rebuild_time = time.time() - start_time
        print(f"✅ 完全重建完成, 用时 {rebuild_time:.2f} 秒")

if __name__ == "__main__":
    # 测试优化引擎
    print("🧪 测试优化的高级生肖引擎")
    print("="*50)
    
    # 初始化引擎
    engine = OptimizedZodiacEngine()
    
    # 显示状态
    status = engine.get_system_status()
    print(f"📊 系统状态: {status}")
    
    # 快速查找候选
    print(f"\n🔍 快速查找候选小组...")
    start_time = time.time()
    candidates = engine.find_candidates_fast()
    search_time = time.time() - start_time
    
    print(f"✅ 查找完成: {len(candidates)} 个候选, 用时 {search_time:.3f} 秒")
    
    # 显示前5个候选
    for i, candidate in enumerate(candidates[:5], 1):
        print(f"   {i}. {candidate['members']} - Z-Score: {candidate['z_score']:.3f}")
    
    print(f"\n🎯 性能测试完成!")
