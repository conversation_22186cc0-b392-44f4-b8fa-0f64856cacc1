{"test_strategy": {"strategy_name": "测试策略", "total_periods": 5, "hit_count": 5, "hit_rate": 1.0, "avg_miss_interval": 0, "max_miss_streak": 0, "current_miss_streak": 0, "avg_hit_rank": 1, "coverage_rate": 0.6122448979591837, "sharpe_ratio": 0.0, "max_drawdown": 0.0, "profit_factor": Infinity, "details": [{"period": "2025001", "predicted": [25, 13, 8, 35, 42, 1, 2, 3, 4, 5], "actual": 25, "is_hit": true, "hit_rank": 1, "miss_before": 0}, {"period": "2025002", "predicted": [13, 25, 8, 35, 42, 6, 7, 9, 10, 11], "actual": 13, "is_hit": true, "hit_rank": 1, "miss_before": 0}, {"period": "2025003", "predicted": [8, 25, 13, 35, 42, 12, 14, 15, 16, 17], "actual": 8, "is_hit": true, "hit_rank": 1, "miss_before": 0}, {"period": "2025004", "predicted": [35, 25, 13, 8, 42, 18, 19, 20, 21, 22], "actual": 35, "is_hit": true, "hit_rank": 1, "miss_before": 0}, {"period": "2025005", "predicted": [42, 25, 13, 8, 35, 23, 24, 26, 27, 28], "actual": 42, "is_hit": true, "hit_rank": 1, "miss_before": 0}]}}