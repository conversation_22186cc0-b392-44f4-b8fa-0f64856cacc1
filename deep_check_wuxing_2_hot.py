#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度检查wuxing_2_hot的真实实现
"""

from prediction_engine import PredictionEngine
from dsl_strategy_parser import DSLStrategyParser
import inspect

def check_prediction_engine_source():
    """检查预测引擎源码"""
    print("🔍 检查预测引擎源码")
    print("="*60)
    
    try:
        engine = PredictionEngine()
        
        # 获取_run_traditional_strategies方法的完整源码
        source = inspect.getsource(engine._run_traditional_strategies)
        
        print("📄 _run_traditional_strategies方法源码:")
        print("-" * 50)
        
        lines = source.split('\n')
        for i, line in enumerate(lines, 1):
            print(f"{i:3d}: {line}")
        
        print("-" * 50)
        
        # 分析关键部分
        print("\n🔍 关键逻辑分析:")
        
        # 查找策略评分部分
        if 'mock_strategies' in source:
            print("✅ 找到mock_strategies定义")
            
            # 提取mock_strategies的内容
            for i, line in enumerate(lines):
                if 'mock_strategies' in line and '=' in line:
                    print(f"   第{i+1}行: {line.strip()}")
                    
                    # 查看后续几行
                    for j in range(i+1, min(i+5, len(lines))):
                        if lines[j].strip():
                            print(f"   第{j+1}行: {lines[j].strip()}")
                        if '}' in lines[j]:
                            break
        
        # 查找预测号码生成部分
        if 'predicted_numbers' in source:
            print("\n✅ 找到predicted_numbers生成")
            
            for i, line in enumerate(lines):
                if 'predicted_numbers' in line and '=' in line:
                    print(f"   第{i+1}行: {line.strip()}")
        
        return source
        
    except Exception as e:
        print(f"❌ 检查源码失败: {e}")
        return None

def check_strategy_flow():
    """检查策略执行流程"""
    print(f"\n🔄 检查策略执行流程")
    print("="*60)
    
    try:
        # 1. 检查策略配置
        print("📋 1. 策略配置检查:")
        parser = DSLStrategyParser()
        summary = parser.get_strategy_summary()
        
        for strategy in summary['strategies']:
            if strategy['id'] == 'wuxing_2_hot':
                print(f"   策略ID: {strategy['id']}")
                print(f"   策略名称: {strategy['name']}")
                print(f"   启用状态: {strategy['active']}")
                print(f"   策略类型: {strategy['type']}")
                print(f"   权重: {strategy['weight']}")
                break
        
        # 2. 运行预测并跟踪
        print(f"\n🎯 2. 预测执行跟踪:")
        engine = PredictionEngine()
        
        # 手动调用_run_traditional_strategies来观察
        print("   调用_run_traditional_strategies...")
        
        # 模拟调用
        period_number = "2025199"
        result = engine._run_traditional_strategies(period_number)
        
        print(f"   返回结果类型: {type(result)}")
        
        if hasattr(result, 'strategy_details'):
            print(f"   策略详情: {len(result.strategy_details)} 个")
            
            for strategy in result.strategy_details:
                strategy_id = strategy.get('strategy_id', 'Unknown')
                strategy_name = strategy.get('strategy_name', 'Unknown')
                predicted_numbers = strategy.get('predicted_numbers', [])
                confidence = strategy.get('confidence', 0)
                
                print(f"     - {strategy_name} ({strategy_id})")
                print(f"       号码: {predicted_numbers}")
                print(f"       置信度: {confidence}")
        
        return result
        
    except Exception as e:
        print(f"❌ 策略流程检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_number_generation():
    """分析号码生成逻辑"""
    print(f"\n🔢 分析号码生成逻辑")
    print("="*60)
    
    # 检查当前的预测号码
    target_numbers = [5, 10, 15, 17, 20, 24, 38, 41]
    
    print(f"🎯 目标号码: {target_numbers}")
    
    # 分析这些号码的特征
    print(f"\n📊 号码特征分析:")
    
    # 1. 奇偶分析
    odd_numbers = [n for n in target_numbers if n % 2 == 1]
    even_numbers = [n for n in target_numbers if n % 2 == 0]
    
    print(f"   奇数: {odd_numbers} ({len(odd_numbers)}个)")
    print(f"   偶数: {even_numbers} ({len(even_numbers)}个)")
    
    # 2. 大小分析 (以25为界)
    small_numbers = [n for n in target_numbers if n <= 25]
    big_numbers = [n for n in target_numbers if n > 25]
    
    print(f"   小数(≤25): {small_numbers} ({len(small_numbers)}个)")
    print(f"   大数(>25): {big_numbers} ({len(big_numbers)}个)")
    
    # 3. 尾数分析
    tail_numbers = [n % 10 for n in target_numbers]
    unique_tails = list(set(tail_numbers))
    
    print(f"   尾数: {tail_numbers}")
    print(f"   唯一尾数: {sorted(unique_tails)} ({len(unique_tails)}个)")
    
    # 4. 五行分析 (如果可能)
    try:
        from dynamic_mapping_system import DynamicMappingSystem
        mapping = DynamicMappingSystem()
        
        print(f"\n🔥 五行属性分析:")
        wuxing_count = {}
        
        for number in target_numbers:
            wuxing = mapping.get_wuxing_for_number(number, "2025199")
            print(f"   号码{number:2d}: {wuxing}")
            wuxing_count[wuxing] = wuxing_count.get(wuxing, 0) + 1
        
        print(f"\n   五行分布:")
        for wuxing, count in wuxing_count.items():
            print(f"     {wuxing}: {count}个")
        
        # 分析可能的五行组合
        if len(wuxing_count) >= 2:
            from itertools import combinations
            print(f"\n   可能的五行2组合:")
            wuxing_list = list(wuxing_count.keys())
            for combo in combinations(wuxing_list, 2):
                print(f"     {combo[0]}+{combo[1]}")
    
    except Exception as e:
        print(f"   ❌ 五行分析失败: {e}")

def check_if_hardcoded():
    """检查是否是硬编码"""
    print(f"\n💻 检查是否是硬编码")
    print("="*60)
    
    target_numbers = [5, 10, 15, 17, 20, 24, 38, 41]
    
    # 搜索这些号码在代码中的出现
    import glob
    
    python_files = glob.glob('*.py')
    found_in_files = []
    
    for file in python_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含这些号码
            numbers_str = str(target_numbers).replace(' ', '')
            if numbers_str in content.replace(' ', ''):
                found_in_files.append(file)
                print(f"✅ 在 {file} 中找到完整号码序列")
                
                # 显示相关行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if '5' in line and '10' in line and '15' in line and '17' in line:
                        print(f"   第{i}行: {line.strip()}")
            
            # 检查部分号码
            elif any(str(num) in content for num in target_numbers):
                # 检查是否在同一行或相近行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if sum(str(num) in line for num in target_numbers) >= 4:
                        print(f"⚠️ 在 {file} 第{i}行找到多个目标号码: {line.strip()}")
        
        except Exception as e:
            continue
    
    if found_in_files:
        print(f"\n📋 号码序列出现在以下文件中:")
        for file in found_in_files:
            print(f"   - {file}")
        print(f"\n💡 结论: 这些号码很可能是硬编码的")
    else:
        print(f"\n🔍 未找到完整的号码序列")
        print(f"💡 结论: 号码可能是动态生成的")

def check_real_algorithm():
    """检查是否有真实的算法"""
    print(f"\n🧠 检查是否有真实的算法")
    print("="*60)
    
    print("🔍 查找可能的算法实现:")
    
    # 1. 检查是否调用了五行组合生成器
    try:
        from combo_generator import ComboGenerator
        generator = ComboGenerator()
        wuxing_combos = generator.generate_wuxing_2()
        
        print(f"✅ 五行组合生成器可用: {len(wuxing_combos)} 个组合")
        
        # 检查是否在预测中使用
        engine = PredictionEngine()
        source = inspect.getsource(engine._run_traditional_strategies)
        
        if 'ComboGenerator' in source or 'generate_wuxing' in source:
            print(f"✅ 预测引擎中使用了五行组合生成器")
        else:
            print(f"❌ 预测引擎中未使用五行组合生成器")
    
    except Exception as e:
        print(f"❌ 五行组合生成器检查失败: {e}")
    
    # 2. 检查是否使用了统计追踪
    try:
        from extreme_stat_tracker import ExtremeStatTracker
        print(f"✅ 统计追踪器可用")
        
        # 检查是否在预测中使用
        if 'ExtremeStatTracker' in source or 'stat_tracker' in source:
            print(f"✅ 预测引擎中使用了统计追踪器")
        else:
            print(f"❌ 预测引擎中未使用统计追踪器")
    
    except Exception as e:
        print(f"❌ 统计追踪器检查失败: {e}")
    
    # 3. 检查是否有动态计算
    print(f"\n🔄 检查动态计算:")
    
    if 'calculate' in source or 'analyze' in source or 'score' in source:
        print(f"✅ 代码中包含计算/分析逻辑")
    else:
        print(f"❌ 代码中缺少计算/分析逻辑")
    
    if 'mock' in source or 'hardcode' in source or 'fixed' in source:
        print(f"⚠️ 代码中包含模拟/硬编码逻辑")
    else:
        print(f"✅ 代码中无明显的模拟/硬编码逻辑")

def main():
    """主检查流程"""
    print("🔍 深度检查wuxing_2_hot实现")
    print("="*70)
    
    # 1. 检查源码
    source = check_prediction_engine_source()
    
    # 2. 检查策略流程
    result = check_strategy_flow()
    
    # 3. 分析号码生成
    analyze_number_generation()
    
    # 4. 检查是否硬编码
    check_if_hardcoded()
    
    # 5. 检查真实算法
    check_real_algorithm()
    
    print(f"\n🎯 总结:")
    print("基于以上检查，wuxing_2_hot当前的实现情况:")
    print("1. 策略配置正确，ID为wuxing_2_hot")
    print("2. 预测号码为固定的[5, 10, 15, 17, 20, 24, 38, 41]")
    print("3. 需要进一步确认是否使用了真实的五行算法")

if __name__ == "__main__":
    main()
