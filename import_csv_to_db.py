import csv
import sqlite3
from datetime import datetime

def import_data_to_db(csv_file_path='lottery_data_20250717.csv', db_path='multi_zodiac.db'):
    """
    Imports lottery data from a CSV file into the SQLite database.
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    with open(csv_file_path, 'r', encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        for row in reader:
            try:
                draw_date = datetime.strptime(row['draw_date'], '%Y-%m-%d').date()
                year = draw_date.year
                draw_number = row['period_number']
                special_number = int(row['special_code'])
                zodiac = row['zodiac']

                # Insert data into the lottery_draws table
                cursor.execute('''
                    INSERT OR IGNORE INTO lottery_draws 
                    (draw_date, year, draw_number, special_number, zodiac)
                    VALUES (?, ?, ?, ?, ?)
                ''', (draw_date, year, draw_number, special_number, zodiac))
            except (ValueError, KeyError) as e:
                print(f"Skipping row due to error: {e}. Row: {row}")

    conn.commit()
    conn.close()
    print(f"✅ Data from {csv_file_path} has been successfully imported into {db_path}.")

if __name__ == '__main__':
    # To ensure the database schema is up to date before importing
    from multi_dimensional_zodiac_db import MultiDimensionalZodiacDB
    db_manager = MultiDimensionalZodiacDB()
    db_manager.init_database()
    
    import_data_to_db()
