#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试刷新分析按钮问题
"""

import tkinter as tk
from gui_main import LotteryPredictionGUI
import time

def debug_refresh_button():
    """调试刷新按钮问题"""
    print("🔍 调试刷新分析按钮问题")
    print("="*60)
    
    try:
        # 创建GUI
        print("📊 创建GUI实例...")
        root = tk.Tk()
        gui = LotteryPredictionGUI(root)
        
        print("✅ GUI创建成功")
        
        # 检查关键组件
        print("\n🔧 检查关键组件...")
        
        # 检查预测引擎
        if hasattr(gui, 'prediction_engine'):
            print("✅ 预测引擎存在")
            
            # 检查高级生肖引擎
            if hasattr(gui.prediction_engine, 'advanced_zodiac_engine'):
                print("✅ 高级生肖引擎存在")
                
                # 检查引擎状态
                engine = gui.prediction_engine.advanced_zodiac_engine
                print(f"   小组数量: {len(engine.all_groups):,}")
                print(f"   最后处理期号: {engine.last_processed_period}")
            else:
                print("❌ 高级生肖引擎缺失")
        else:
            print("❌ 预测引擎缺失")
        
        # 检查Z-Score阈值变量
        if hasattr(gui, 'zscore_threshold_var'):
            print("✅ Z-Score阈值变量存在")
            print(f"   当前值: {gui.zscore_threshold_var.get()}")
        else:
            print("❌ Z-Score阈值变量缺失")
        
        # 检查候选显示组件
        components = [
            ('candidates_tree', '候选小组表格'),
            ('status_text', '状态显示'),
            ('numbers_text', '推荐号码显示'),
            ('energy_tree', '能量分析表格')
        ]
        
        for attr, desc in components:
            if hasattr(gui, attr):
                print(f"✅ {desc}存在")
            else:
                print(f"❌ {desc}缺失")
        
        # 手动测试刷新方法
        print(f"\n🧪 手动测试刷新方法...")
        
        try:
            print("   调用 refresh_advanced_analysis()...")
            gui.refresh_advanced_analysis()
            print("✅ 刷新方法调用成功")
            
            # 检查结果
            candidates_count = len(gui.candidates_tree.get_children())
            print(f"   候选小组数: {candidates_count}")
            
            if candidates_count > 0:
                print("✅ 候选小组显示正常")
                
                # 显示前3个候选
                for i, item in enumerate(gui.candidates_tree.get_children()[:3], 1):
                    values = gui.candidates_tree.item(item)['values']
                    if values:
                        print(f"     {i}. {values[1]} - Z-Score: {values[2]}")
            else:
                print("⚠️ 无候选小组显示")
                
        except Exception as e:
            print(f"❌ 刷新方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试按钮绑定
        print(f"\n🔘 测试按钮绑定...")
        
        # 查找刷新按钮
        refresh_button = None
        
        def find_refresh_button(widget):
            """递归查找刷新按钮"""
            if isinstance(widget, tk.Widget):
                if hasattr(widget, 'cget'):
                    try:
                        text = widget.cget('text')
                        if '刷新分析' in str(text):
                            return widget
                    except:
                        pass
                
                # 递归查找子组件
                try:
                    for child in widget.winfo_children():
                        result = find_refresh_button(child)
                        if result:
                            return result
                except:
                    pass
            return None
        
        refresh_button = find_refresh_button(root)
        
        if refresh_button:
            print("✅ 找到刷新按钮")
            
            # 检查按钮命令
            try:
                command = refresh_button.cget('command')
                print(f"   按钮命令: {command}")
                
                if command:
                    print("✅ 按钮命令已绑定")
                    
                    # 手动触发按钮
                    print("   手动触发按钮...")
                    command()
                    print("✅ 按钮触发成功")
                else:
                    print("❌ 按钮命令未绑定")
                    
            except Exception as e:
                print(f"❌ 检查按钮命令失败: {e}")
        else:
            print("❌ 未找到刷新按钮")
        
        # 检查日志输出
        print(f"\n📝 检查日志输出...")
        if hasattr(gui, 'log_text'):
            log_content = gui.log_text.get(1.0, tk.END)
            recent_logs = log_content.strip().split('\n')[-5:]  # 最近5条日志
            
            print("   最近日志:")
            for log in recent_logs:
                if log.strip():
                    print(f"     {log}")
        
        print(f"\n🎯 调试完成!")
        print("如果问题仍然存在，请检查:")
        print("1. 是否有异常信息在日志中")
        print("2. 数据文件是否完整")
        print("3. 系统内存是否充足")
        
        # 不启动主循环，直接返回结果
        root.destroy()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def test_prediction_engine_directly():
    """直接测试预测引擎"""
    print(f"\n🧠 直接测试预测引擎")
    print("="*60)
    
    try:
        from prediction_engine import PredictionEngine
        
        print("📊 创建预测引擎...")
        engine = PredictionEngine()
        
        print("✅ 预测引擎创建成功")
        
        # 测试高级分析
        print("🔍 测试高级分析...")
        result = engine.get_advanced_zodiac_analysis()
        
        if 'error' in result:
            print(f"❌ 高级分析失败: {result['error']}")
        else:
            print("✅ 高级分析成功")
            print(f"   候选数量: {len(result.get('candidates', []))}")
            print(f"   推荐号码: {len(result.get('top_recommendations', []))}")
            print(f"   能量分析: {len(result.get('energy_analysis', {}))}")
            
            # 显示前3个候选
            candidates = result.get('candidates', [])
            if candidates:
                print(f"   前3个候选:")
                for i, candidate in enumerate(candidates[:3], 1):
                    members = ", ".join(candidate.get('members', []))
                    z_score = candidate.get('z_score', 0)
                    print(f"     {i}. {members} - Z-Score: {z_score:.3f}")
        
    except Exception as e:
        print(f"❌ 预测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 直接测试预测引擎
    test_prediction_engine_directly()
    
    # 调试GUI按钮
    debug_refresh_button()
