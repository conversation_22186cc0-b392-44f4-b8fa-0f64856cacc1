#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用4肖组合历史分析器
为GUI界面提供详细的历史遗漏分析数据
"""

import numpy as np
from typing import Dict, List, Any, Optional
from optimized_zodiac_engine import OptimizedZodiacEngine

class ZodiacGroupAnalyzer:
    """4肖组合历史分析器"""
    
    def __init__(self):
        self.engine = OptimizedZodiacEngine()
        self.zodiac_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
    
    def analyze_group(self, members: List[str]) -> Optional[Dict[str, Any]]:
        """分析指定4肖组合的历史数据"""
        if len(members) != 4:
            return None
        
        # 查找匹配的小组
        target_group = None
        for group_id, group in self.engine.all_groups.items():
            if set(group.members) == set(members):
                target_group = group
                break
        
        if not target_group:
            return None
        
        # 基础统计信息
        analysis_result = {
            'group_id': target_group.group_id,
            'members': target_group.members,
            'current_miss': target_group.current_miss,
            'max_miss_history': target_group.max_miss,
            'hit_count': target_group.hit_count,
            'miss_history_count': len(target_group.miss_history),
            'z_score': getattr(target_group, 'z_score', 0),
            'miss_mean': getattr(target_group, 'miss_mean', 0),
            'miss_std_dev': getattr(target_group, 'miss_std_dev', 0)
        }
        
        # 详细统计分析
        if target_group.miss_history:
            miss_array = np.array(target_group.miss_history)
            
            analysis_result.update({
                'avg_miss': float(np.mean(miss_array)),
                'std_miss': float(np.std(miss_array)),
                'min_miss': int(np.min(miss_array)),
                'max_miss': int(np.max(miss_array)),
                'median_miss': float(np.median(miss_array)),
                'percentile_75': float(np.percentile(miss_array, 75)),
                'percentile_90': float(np.percentile(miss_array, 90)),
                'percentile_95': float(np.percentile(miss_array, 95))
            })
            
            # 遗漏分布统计
            miss_distribution = self._calculate_miss_distribution(miss_array)
            analysis_result['miss_distribution'] = miss_distribution
            
            # 最近遗漏记录
            recent_count = min(10, len(target_group.miss_history))
            analysis_result['recent_misses'] = target_group.miss_history[-recent_count:]
        
        # 组内能量分析
        internal_energy = {}
        for member in target_group.members:
            miss_count = target_group.internal_misses.get(member, 0)
            pressure_level = self._get_pressure_level(miss_count)
            internal_energy[member] = {
                'miss_count': miss_count,
                'pressure_level': pressure_level['level'],
                'pressure_icon': pressure_level['icon']
            }
        
        analysis_result['internal_energy'] = internal_energy
        
        # 紧迫度评估
        urgency_info = self._assess_urgency(target_group.current_miss, target_group.max_miss, analysis_result.get('z_score', 0))
        analysis_result['urgency_assessment'] = urgency_info
        
        # 历史排名信息
        ranking_info = self._get_group_ranking(target_group.max_miss)
        analysis_result['ranking_info'] = ranking_info
        
        return analysis_result
    
    def _calculate_miss_distribution(self, miss_array: np.ndarray) -> Dict[str, Dict[str, Any]]:
        """计算遗漏分布统计"""
        total_count = len(miss_array)
        
        ranges = {
            "0-2期": (0, 2),
            "3-5期": (3, 5),
            "6-10期": (6, 10),
            "11-15期": (11, 15),
            "16-20期": (16, 20),
            "20期以上": (21, float('inf'))
        }
        
        distribution = {}
        for range_name, (min_val, max_val) in ranges.items():
            if max_val == float('inf'):
                count = len([m for m in miss_array if m >= min_val])
            else:
                count = len([m for m in miss_array if min_val <= m <= max_val])
            
            percentage = count / total_count * 100 if total_count > 0 else 0
            distribution[range_name] = {
                'count': count,
                'percentage': percentage
            }
        
        return distribution
    
    def _get_pressure_level(self, miss_count: int) -> Dict[str, str]:
        """获取压力等级"""
        if miss_count >= 20:
            return {'level': '极高', 'icon': '🔥'}
        elif miss_count >= 15:
            return {'level': '高', 'icon': '⚡'}
        elif miss_count >= 10:
            return {'level': '中等', 'icon': '💧'}
        elif miss_count >= 5:
            return {'level': '低', 'icon': '❄️'}
        else:
            return {'level': '极低', 'icon': '🌟'}
    
    def _assess_urgency(self, current_miss: int, max_miss: int, z_score: float) -> Dict[str, Any]:
        """评估紧迫度"""
        if max_miss == 0:
            return {
                'level': '低',
                'icon': '❄️',
                'description': '无历史数据',
                'distance_to_max': 0,
                'percentage_of_max': 0
            }
        
        distance_to_max = max_miss - current_miss
        percentage_of_max = (current_miss / max_miss) * 100
        
        # 基于多个指标综合评估
        if current_miss >= max_miss * 0.9 or z_score >= 3.0:
            urgency = {'level': '极高', 'icon': '🔥', 'description': '接近或超过历史极值'}
        elif current_miss >= max_miss * 0.8 or z_score >= 2.5:
            urgency = {'level': '高', 'icon': '⚡', 'description': '接近历史高位'}
        elif current_miss >= max_miss * 0.6 or z_score >= 2.0:
            urgency = {'level': '中等', 'icon': '💧', 'description': '超过历史平均水平'}
        elif current_miss >= max_miss * 0.4 or z_score >= 1.5:
            urgency = {'level': '低', 'icon': '❄️', 'description': '正常范围内'}
        else:
            urgency = {'level': '极低', 'icon': '🌟', 'description': '远低于历史平均'}
        
        urgency.update({
            'distance_to_max': distance_to_max,
            'percentage_of_max': percentage_of_max
        })
        
        return urgency
    
    def _get_group_ranking(self, max_miss: int) -> Dict[str, Any]:
        """获取小组在全系统中的排名信息"""
        # 统计所有小组的最大遗漏
        all_max_misses = []
        for group in self.engine.all_groups.values():
            if group.max_miss > 0:
                all_max_misses.append(group.max_miss)
        
        if not all_max_misses:
            return {'rank': 0, 'total': 0, 'percentile': 0}
        
        all_max_misses.sort(reverse=True)
        total_groups = len(all_max_misses)
        
        # 找到当前小组的排名
        rank = 1
        for miss in all_max_misses:
            if miss > max_miss:
                rank += 1
            else:
                break
        
        percentile = ((total_groups - rank + 1) / total_groups) * 100
        
        return {
            'rank': rank,
            'total': total_groups,
            'percentile': percentile,
            'max_in_system': all_max_misses[0] if all_max_misses else 0,
            'min_in_system': all_max_misses[-1] if all_max_misses else 0
        }
    
    def format_analysis_text(self, analysis_data: Dict[str, Any]) -> str:
        """格式化分析数据为显示文本"""
        if not analysis_data:
            return "未找到小组数据"
        
        members_str = ", ".join(analysis_data['members'])
        current_miss = analysis_data['current_miss']
        max_miss = analysis_data['max_miss_history']
        hit_count = analysis_data['hit_count']
        z_score = analysis_data['z_score']
        
        urgency = analysis_data['urgency_assessment']
        ranking = analysis_data['ranking_info']
        
        # 基础信息行
        line1 = f"小组: {members_str}"
        line2 = f"当前遗漏: {current_miss} 期  |  历史最大遗漏: {max_miss} 期  |  距离极值: {urgency['distance_to_max']} 期"
        line3 = f"Z-Score: {z_score:.2f}  |  紧迫度: {urgency['icon']} {urgency['level']}  |  总命中: {hit_count} 次"
        
        # 详细统计信息
        if 'avg_miss' in analysis_data:
            avg_miss = analysis_data['avg_miss']
            std_miss = analysis_data['std_miss']
            min_miss = analysis_data['min_miss']
            median_miss = analysis_data['median_miss']
            line4 = f"平均遗漏: {avg_miss:.1f} 期  |  标准差: {std_miss:.1f} 期  |  最小遗漏: {min_miss} 期  |  中位数: {median_miss:.1f} 期"
        else:
            line4 = "历史数据: 暂无足够统计数据"
        
        # 排名信息
        rank = ranking['rank']
        total = ranking['total']
        percentile = ranking['percentile']
        line5 = f"系统排名: 第 {rank} 名 / {total} 个小组  |  百分位: {percentile:.1f}%  |  {urgency['description']}"
        
        return f"{line1}\n{line2}\n{line3}\n{line4}\n{line5}"
    
    def get_top_groups_by_urgency(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取按紧迫度排序的前N个小组（优化版本）"""
        urgent_groups = []

        # 第一步：快速筛选高Z-Score的小组
        high_zscore_groups = []
        for group_id, group in self.engine.all_groups.items():
            if len(group.members) == 4 and group.max_miss > 0:
                # 快速计算Z-Score（不进行完整分析）
                z_score = getattr(group, 'z_score', 0)
                if z_score >= 1.5:  # 只保留高Z-Score的小组
                    high_zscore_groups.append({
                        'group': group,
                        'z_score': z_score
                    })

        # 按Z-Score排序，只取前面的进行详细分析
        high_zscore_groups.sort(key=lambda x: x['z_score'], reverse=True)

        # 第二步：对前N*2个小组进行详细分析
        analysis_limit = min(limit * 2, len(high_zscore_groups))

        for item in high_zscore_groups[:analysis_limit]:
            group = item['group']
            try:
                analysis = self.analyze_group(group.members)
                if analysis:
                    urgent_groups.append(analysis)
            except Exception:
                # 如果分析失败，创建简化版本
                urgent_groups.append({
                    'group_id': getattr(group, 'group_id', 'unknown'),
                    'members': group.members,
                    'current_miss': group.current_miss,
                    'max_miss_history': group.max_miss,
                    'z_score': item['z_score'],
                    'urgency_assessment': {
                        'icon': '🔥' if item['z_score'] >= 3.0 else '⚡' if item['z_score'] >= 2.0 else '💧',
                        'level': '极高' if item['z_score'] >= 3.0 else '高' if item['z_score'] >= 2.0 else '中等'
                    }
                })

        # 最终排序
        urgent_groups.sort(key=lambda x: x['z_score'], reverse=True)

        return urgent_groups[:limit]

if __name__ == "__main__":
    # 测试分析器
    analyzer = ZodiacGroupAnalyzer()
    
    # 测试虎兔羊狗小组
    test_groups = [
        ["虎", "兔", "羊", "狗"],
        ["鼠", "牛", "龙", "蛇"],
        ["马", "猴", "鸡", "猪"]
    ]
    
    print("🔍 测试4肖组合历史分析器")
    print("="*70)
    
    for members in test_groups:
        print(f"\n📊 分析小组: {', '.join(members)}")
        analysis = analyzer.analyze_group(members)
        
        if analysis:
            formatted_text = analyzer.format_analysis_text(analysis)
            print(formatted_text)
            
            # 显示组内能量
            print(f"\n⚡ 组内能量分析:")
            for zodiac, energy in analysis['internal_energy'].items():
                icon = energy['pressure_icon']
                level = energy['pressure_level']
                miss = energy['miss_count']
                print(f"   {icon} {zodiac}: {miss} 期 - {level}压力")
        else:
            print("   ❌ 未找到匹配的小组数据")
    
    # 测试紧迫度排行
    print(f"\n🔥 当前最紧迫的10个4肖组合:")
    top_urgent = analyzer.get_top_groups_by_urgency(10)
    
    for i, group_data in enumerate(top_urgent, 1):
        members_str = ", ".join(group_data['members'])
        z_score = group_data['z_score']
        current_miss = group_data['current_miss']
        urgency_icon = group_data['urgency_assessment']['icon']
        print(f"   {i:2d}. {urgency_icon} {members_str}: Z-Score {z_score:.2f}, 遗漏 {current_miss} 期")
    
    print(f"\n🎯 分析器测试完成!")
