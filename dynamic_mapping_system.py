#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态映射系统
支持每年春节切换的生肖和五行映射
"""

from datetime import datetime, date
from typing import Dict, List, Tuple, Any

class DynamicMappingSystem:
    """动态映射系统"""
    
    def __init__(self):
        # 春节日期数据
        self.spring_festival_dates = {
            2020: date(2020, 1, 25),
            2021: date(2021, 2, 12),
            2022: date(2022, 2, 1),
            2023: date(2023, 1, 22),
            2024: date(2024, 2, 10),
            2025: date(2025, 1, 29),
            2026: date(2026, 2, 17),
        }
        
        # 生肖顺序（固定）
        self.zodiac_order = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        
        # 年度生肖映射（根据您提供的准确数据）
        self.yearly_zodiac_mapping = {
            2020: {
                "鼠": [1, 13, 25, 37, 49],
                "牛": [12, 24, 36, 48],
                "虎": [11, 23, 35, 47],
                "兔": [10, 22, 34, 46],
                "龙": [9, 21, 33, 45],
                "蛇": [8, 20, 32, 44],
                "马": [7, 19, 31, 43],
                "羊": [6, 18, 30, 42],
                "猴": [5, 17, 29, 41],
                "鸡": [4, 16, 28, 40],
                "狗": [3, 15, 27, 39],
                "猪": [2, 14, 26, 38]
            },
            2021: {
                "鼠": [2, 14, 26, 38],
                "牛": [1, 13, 25, 37, 49],
                "虎": [12, 24, 36, 48],
                "兔": [11, 23, 35, 47],
                "龙": [10, 22, 34, 46],
                "蛇": [9, 21, 33, 45],
                "马": [8, 20, 32, 44],
                "羊": [7, 19, 31, 43],
                "猴": [6, 18, 30, 42],
                "鸡": [5, 17, 29, 41],
                "狗": [4, 16, 28, 40],
                "猪": [3, 15, 27, 39]
            },
            2022: {
                "鼠": [3, 15, 27, 39],
                "牛": [2, 14, 26, 38],
                "虎": [1, 13, 25, 37, 49],
                "兔": [12, 24, 36, 48],
                "龙": [11, 23, 35, 47],
                "蛇": [10, 22, 34, 46],
                "马": [9, 21, 33, 45],
                "羊": [8, 20, 32, 44],
                "猴": [7, 19, 31, 43],
                "鸡": [6, 18, 30, 42],
                "狗": [5, 17, 29, 41],
                "猪": [4, 16, 28, 40]
            },
            2023: {
                "鼠": [4, 16, 28, 40],
                "牛": [3, 15, 27, 39],
                "虎": [2, 14, 26, 38],
                "兔": [1, 13, 25, 37, 49],
                "龙": [12, 24, 36, 48],
                "蛇": [11, 23, 35, 47],
                "马": [10, 22, 34, 46],
                "羊": [9, 21, 33, 45],
                "猴": [8, 20, 32, 44],
                "鸡": [7, 19, 31, 43],
                "狗": [6, 18, 30, 42],
                "猪": [5, 17, 29, 41]
            },
            2024: {
                "鼠": [5, 17, 29, 41],
                "牛": [4, 16, 28, 40],
                "虎": [3, 15, 27, 39],
                "兔": [2, 14, 26, 38],
                "龙": [1, 13, 25, 37, 49],
                "蛇": [12, 24, 36, 48],
                "马": [11, 23, 35, 47],
                "羊": [10, 22, 34, 46],
                "猴": [9, 21, 33, 45],
                "鸡": [8, 20, 32, 44],
                "狗": [7, 19, 31, 43],
                "猪": [6, 18, 30, 42]
            },
            2025: {
                "鼠": [6, 18, 30, 42],
                "牛": [5, 17, 29, 41],
                "虎": [4, 16, 28, 40],
                "兔": [3, 15, 27, 39],
                "龙": [2, 14, 26, 38],
                "蛇": [1, 13, 25, 37, 49],
                "马": [12, 24, 36, 48],
                "羊": [11, 23, 35, 47],
                "猴": [10, 22, 34, 46],  # ✅ 2025年猴包含34号！
                "鸡": [9, 21, 33, 45],
                "狗": [8, 20, 32, 44],
                "猪": [7, 19, 31, 43]
            },
            2026: {
                "鼠": [7, 19, 31, 43],
                "牛": [6, 18, 30, 42],
                "虎": [5, 17, 29, 41],
                "兔": [4, 16, 28, 40],
                "龙": [3, 15, 27, 39],
                "蛇": [2, 14, 26, 38],
                "马": [1, 13, 25, 37, 49],
                "羊": [12, 24, 36, 48],
                "猴": [11, 23, 35, 47],
                "鸡": [10, 22, 34, 46],
                "狗": [9, 21, 33, 45],
                "猪": [8, 20, 32, 44]
            }
        }
        
        # 年度五行映射（根据您提供的数据）
        self.yearly_five_element_mapping = {
            2020: {
                "金": [6, 7, 20, 21, 28, 29, 36, 37],
                "木": [2, 3, 10, 11, 18, 19, 32, 33, 40, 41, 48, 49],
                "水": [8, 9, 16, 17, 24, 25, 38, 39, 46, 47],
                "火": [4, 5, 12, 13, 26, 27, 34, 35, 42, 43],
                "土": [1, 14, 15, 22, 23, 30, 31, 44, 45]
            },
            2021: {
                "金": [7, 8, 21, 22, 29, 30, 37, 38],
                "木": [3, 4, 11, 12, 19, 20, 33, 34, 41, 42, 49],
                "水": [9, 10, 17, 18, 25, 26, 39, 40, 47, 48],
                "火": [5, 6, 13, 14, 27, 28, 35, 36, 43, 44],
                "土": [1, 2, 15, 16, 23, 24, 31, 32, 45, 46]
            },
            2022: {
                "金": [1, 8, 9, 22, 23, 30, 31, 38, 39],
                "木": [4, 5, 12, 13, 20, 21, 34, 35, 42, 43],
                "水": [10, 11, 18, 19, 26, 27, 40, 41, 48, 49],
                "火": [6, 7, 14, 15, 28, 29, 36, 37, 44, 45],
                "土": [2, 3, 16, 17, 24, 25, 32, 33, 46, 47]
            },
            2023: {
                "金": [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
                "木": [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
                "水": [11, 12, 19, 20, 27, 28, 41, 42, 49],
                "火": [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
                "土": [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
            },
            2024: {
                "金": [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
                "木": [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
                "水": [12, 13, 20, 21, 28, 29, 42, 43],
                "火": [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
                "土": [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
            },
            2025: {
                "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
                "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
                "水": [13, 14, 21, 22, 29, 30, 43, 44],
                "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
                "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
            }
        }
    
    def get_year_from_period(self, period: str) -> int:
        """从期号获取年份"""
        try:
            if len(period) >= 4:
                return int(period[:4])
            return datetime.now().year
        except:
            return datetime.now().year
    
    def get_zodiac_for_number(self, number: int, period: str) -> str:
        """根据期号获取号码对应的生肖"""
        year = self.get_year_from_period(period)
        
        if year not in self.yearly_zodiac_mapping:
            # 如果没有该年数据，使用最近的年份
            available_years = sorted(self.yearly_zodiac_mapping.keys())
            if year < min(available_years):
                year = min(available_years)
            elif year > max(available_years):
                year = max(available_years)
            else:
                year = min(available_years, key=lambda x: abs(x - year))
        
        zodiac_mapping = self.yearly_zodiac_mapping[year]
        
        for zodiac, numbers in zodiac_mapping.items():
            if number in numbers:
                return zodiac
        
        return "未知"
    
    def get_five_element_for_number(self, number: int, period: str) -> str:
        """根据期号获取号码对应的五行"""
        year = self.get_year_from_period(period)
        
        if year not in self.yearly_five_element_mapping:
            # 使用最近的年份
            available_years = sorted(self.yearly_five_element_mapping.keys())
            if year < min(available_years):
                year = min(available_years)
            elif year > max(available_years):
                year = max(available_years)
            else:
                year = min(available_years, key=lambda x: abs(x - year))
        
        element_mapping = self.yearly_five_element_mapping[year]
        
        for element, numbers in element_mapping.items():
            if number in numbers:
                return element
        
        return "未知"
    
    def get_zodiac_numbers(self, zodiac: str, period: str) -> List[int]:
        """根据期号获取生肖对应的号码"""
        year = self.get_year_from_period(period)
        
        if year not in self.yearly_zodiac_mapping:
            available_years = sorted(self.yearly_zodiac_mapping.keys())
            if year < min(available_years):
                year = min(available_years)
            elif year > max(available_years):
                year = max(available_years)
            else:
                year = min(available_years, key=lambda x: abs(x - year))
        
        zodiac_mapping = self.yearly_zodiac_mapping[year]
        return zodiac_mapping.get(zodiac, [])
    
    def get_complete_attributes(self, number: int, period: str) -> Dict[str, Any]:
        """获取号码的完整属性"""
        zodiac = self.get_zodiac_for_number(number, period)
        five_element = self.get_five_element_for_number(number, period)
        
        # 波色映射（相对固定）
        wave_color_mapping = {
            "红波": [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
            "蓝波": [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
            "绿波": [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        }
        
        wave_color = "未知"
        for color, numbers in wave_color_mapping.items():
            if number in numbers:
                wave_color = color
                break
        
        return {
            "zodiac": zodiac,
            "five_element": five_element,
            "wave_color": wave_color,
            "big_small": "大" if number >= 25 else "小",
            "odd_even": "单" if number % 2 == 1 else "双",
            "tail_number": number % 10
        }

    def get_numbers_for_zodiac(self, zodiac: str, period_number: str) -> List[int]:
        """获取指定生肖对应的号码列表"""
        year = self.get_year_from_period(period_number)

        if year in self.yearly_zodiac_mapping:
            zodiac_mapping = self.yearly_zodiac_mapping[year]
            return zodiac_mapping.get(zodiac, [])
        else:
            # 如果年份不在映射中，使用最近的年份
            available_years = sorted(self.yearly_zodiac_mapping.keys())
            if available_years:
                closest_year = min(available_years, key=lambda x: abs(x - year))
                zodiac_mapping = self.yearly_zodiac_mapping[closest_year]
                return zodiac_mapping.get(zodiac, [])
            else:
                return []

    def verify_mapping(self, test_cases: List[Tuple[int, str, str]]):
        """验证映射正确性"""
        print("🔍 验证动态映射系统")
        print("="*50)
        
        for number, period, expected_zodiac in test_cases:
            actual_zodiac = self.get_zodiac_for_number(number, period)
            year = self.get_year_from_period(period)
            
            status = "✅" if actual_zodiac == expected_zodiac else "❌"
            print(f"{status} {period}期 {number}号 → {actual_zodiac} (期望: {expected_zodiac}, 年份: {year})")

if __name__ == "__main__":
    # 测试动态映射系统
    mapper = DynamicMappingSystem()
    
    # 验证关键测试案例
    test_cases = [
        (34, "2025197", "猴"),  # 您提到的案例
        (34, "2024197", "羊"),  # 2024年34号应该是羊
        (34, "2023197", "马"),  # 2023年34号应该是马
        (1, "2025197", "蛇"),   # 2025年1号应该是蛇
        (49, "2025197", "蛇"),  # 2025年49号应该是蛇
    ]
    
    mapper.verify_mapping(test_cases)
    
    # 显示2025年完整映射
    print(f"\n📊 2025年生肖映射:")
    for zodiac, numbers in mapper.yearly_zodiac_mapping[2025].items():
        numbers_str = "-".join(f"{n:02d}" for n in numbers)
        print(f"   {zodiac}: {numbers_str}")
    
    # 验证牛蛇羊猴小组在2025年的号码
    print(f"\n🎯 2025年牛蛇羊猴小组对应号码:")
    target_zodiacs = ["牛", "蛇", "羊", "猴"]
    all_numbers = []
    
    for zodiac in target_zodiacs:
        numbers = mapper.get_zodiac_numbers(zodiac, "2025197")
        all_numbers.extend(numbers)
        print(f"   {zodiac}: {numbers}")
    
    print(f"   小组全部号码: {sorted(all_numbers)}")
    print(f"   34号在小组中: {'是' if 34 in all_numbers else '否'}")
