#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试期号格式问题
"""

from prediction_engine import PredictionEngine
from optimized_zodiac_engine import OptimizedZodiacEngine

def debug_period_format():
    """调试期号格式"""
    print("🔍 调试期号格式问题")
    print("="*50)
    
    # 加载历史数据
    pred_engine = PredictionEngine()
    history_data = pred_engine.load_historical_data()
    
    print(f"📊 历史数据总数: {len(history_data)}")
    
    # 检查前10期和后10期的数据
    print(f"\n📋 前10期数据:")
    for i, record in enumerate(history_data[:10]):
        period = record.get('period', '')
        special_code = record.get('special_code', '')
        zodiac = record.get('zodiac', '')
        print(f"   {i+1:2d}. 期号: {period}, 特码: {special_code}, 生肖: {zodiac}")
    
    print(f"\n📋 后10期数据:")
    for i, record in enumerate(history_data[-10:]):
        period = record.get('period', '')
        special_code = record.get('special_code', '')
        zodiac = record.get('zodiac', '')
        print(f"   {i+1:2d}. 期号: {period}, 特码: {special_code}, 生肖: {zodiac}")
    
    # 检查期号排序
    periods = [record.get('period', '') for record in history_data if record.get('period')]
    print(f"\n📈 期号范围:")
    print(f"   最早期号: {min(periods) if periods else 'N/A'}")
    print(f"   最晚期号: {max(periods) if periods else 'N/A'}")
    print(f"   期号数量: {len(periods)}")
    
    # 检查期号格式
    print(f"\n🔍 期号格式分析:")
    period_lengths = {}
    for period in periods[:20]:  # 检查前20个
        length = len(period)
        if length not in period_lengths:
            period_lengths[length] = []
        period_lengths[length].append(period)
    
    for length, examples in period_lengths.items():
        print(f"   长度 {length}: {examples[:5]}")  # 显示前5个例子
    
    # 测试动态映射
    print(f"\n🧠 测试动态映射:")
    engine = OptimizedZodiacEngine()
    
    for record in history_data[:5]:
        period = record.get('period', '')
        special_code = record.get('special_code')
        original_zodiac = record.get('zodiac', '')
        
        if special_code:
            mapped_zodiac = engine.dynamic_mapper.get_zodiac_for_number(special_code, period)
            match = "✅" if mapped_zodiac == original_zodiac else "❌"
            print(f"   {period}: {special_code}号 → 原始:{original_zodiac}, 映射:{mapped_zodiac} {match}")
    
    # 模拟增量更新逻辑
    print(f"\n🔄 模拟增量更新逻辑:")
    engine.last_processed_period = ""  # 模拟初始状态
    
    processed_count = 0
    skipped_count = 0
    
    for record in history_data[:10]:
        period = record.get('period', '')
        special_code = record.get('special_code')
        
        if not period or not special_code:
            print(f"   跳过无效数据: period={period}, code={special_code}")
            continue
        
        # 模拟跳过逻辑
        if engine.last_processed_period and period <= engine.last_processed_period:
            print(f"   跳过已处理: {period} <= {engine.last_processed_period}")
            skipped_count += 1
            continue
        
        # 模拟动态映射
        zodiac = engine.dynamic_mapper.get_zodiac_for_number(special_code, period)
        if not zodiac or zodiac == "未知":
            print(f"   跳过未知生肖: {period}, {special_code}号 → {zodiac}")
            continue
        
        print(f"   ✅ 处理: {period}, {special_code}号 → {zodiac}")
        processed_count += 1
        
        # 更新last_processed_period
        engine.last_processed_period = period
    
    print(f"\n📊 模拟结果:")
    print(f"   处理期数: {processed_count}")
    print(f"   跳过期数: {skipped_count}")
    print(f"   最后处理期号: {engine.last_processed_period}")

if __name__ == "__main__":
    debug_period_format()
