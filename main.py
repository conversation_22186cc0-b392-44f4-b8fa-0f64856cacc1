import csv
import json
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from combo_generator import ComboGenerator
from extreme_stat_tracker import ExtremeStatTracker
from strategy_scorer import StrategyScorerAndFusionEngine
from prediction_viewer import PredictionResultViewer
from prediction_manager import PredictionManager

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("六合彩预测系统")
        self.setMinimumSize(1024, 768)

        # 初始化组件
        self.combo_gen = ComboGenerator()
        self.prediction_manager = PredictionManager()

        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 创建布局
        layout = QVBoxLayout()

        # 创建并添加预测结果查看器
        self.result_viewer = PredictionResultViewer()
        layout.addWidget(self.result_viewer)

        # 设置布局
        main_widget.setLayout(layout)

        # 初始化数据
        self.init_data()

    def init_data(self):
        """初始化系统数据"""
        try:
            # 1. 生成组合
            self.shengxiao_4_combos = self.combo_gen.generate_shengxiao_4()
            self.wuxing_2_combos = self.combo_gen.generate_wuxing_2()

            # 2. 加载历史数据
            history_draws = self.load_historical_data()
            if not history_draws:
                QMessageBox.warning(self, "警告", "无法加载历史数据，请检查数据文件。")
                return

            # 3. 加载最新预测结果
            self.load_latest_predictions()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def load_historical_data(self, file_path=None):
        """加载历史数据"""
        if file_path is None:
            # 使用当前目录下的数据文件
            file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lottery_data_20250717.csv')

        history = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'

                    history.append({
                        'period': row[period_key],
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
        except FileNotFoundError:
            QMessageBox.warning(self, "警告", f"找不到数据文件: {file_path}")
            return []

        return list(reversed(history))

    def load_latest_predictions(self):
        """加载最新预测结果"""
        try:
            # 获取最新预测
            latest = self.prediction_manager.get_latest_predictions(limit=10)
            if latest:
                # 获取最新预测的详细信息
                details = self.prediction_manager.get_prediction_details(latest[0]['prediction_id'])
                # 更新界面显示
                self.result_viewer.update_latest_prediction(details)
                self.result_viewer.update_history(latest)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载预测结果失败: {str(e)}")

def main():
    # 创建应用
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
    # For this example, we'll just simulate running it.
    # We are not using the output of the tracker directly in this simplified main script,
    # but in a full application, this data would feed into the strategy definitions.
    print("Running statistics tracker (output suppressed for brevity)...")
    tracker = ExtremeStatTracker(db_connection=None, combo_list=all_combos, history_draws=history_draws)
    tracker.run_tracking()
    print("Statistics tracker run complete.")


    # 4. Define Strategies (mocked for this example)
    # In a real system, this would come from a DSL/config file and be based on tracker results.
    strategies = {
        "shengxiao_4_extreme": {
            "hit_count": 5, # Mock data
            "avg_omit_before_hit": 8, # Mock data
            "predicted_numbers": [3, 10, 17, 24, 31, 38, 45] # Mock prediction
        },
        "wuxing_2_hot": {
            "hit_count": 10, # Mock data
            "avg_omit_before_hit": 4, # Mock data
            "predicted_numbers": [5, 10, 15, 20, 25, 30, 35] # Mock prediction
        }
    }

    # 5. Score Strategies and Fuse Predictions
    scorer = StrategyScorerAndFusionEngine(strategies, history_draws)
    strategy_scores = scorer.calculate_scores()

    print("\nCalculated Strategy Scores:")
    print(json.dumps(strategy_scores, indent=2))

    # Prepare predictions for fusion
    predictions_to_fuse = [
        {
            "strategy_id": strat_id,
            "weight": strategy_scores[strat_id]["score"],
            "numbers": strat_data["predicted_numbers"]
        }
        for strat_id, strat_data in strategies.items()
    ]

    final_prediction = scorer.fuse_predictions(predictions_to_fuse, method='weighted_union', top_n=12)

    # 6. Present Final Prediction
    print("\n--- Final Prediction ---")
    print(f"Based on the fusion of {len(strategies)} strategies.")
    print("Predicted Numbers (Top 12):")
    print(sorted(final_prediction))

if __name__ == '__main__':
    main()
