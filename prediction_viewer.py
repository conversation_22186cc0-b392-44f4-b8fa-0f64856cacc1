from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, QTabWidget,
                             QTextEdit, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from typing import List, Dict
import json
from datetime import datetime
import os

class PredictionResultViewer(QWidget):
    """预测结果查看器"""
    exportRequested = pyqtSignal(str)  # 导出信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        
        # 1. 创建标签栏
        self.tabs = QTabWidget()
        self.tabs.addTab(self._create_latest_tab(), "最新预测")
        self.tabs.addTab(self._create_history_tab(), "历史记录")
        self.tabs.addTab(self._create_analysis_tab(), "分析详情")
        
        # 2. 创建控制按钮区
        btnLayout = QHBoxLayout()
        
        exportBtn = QPushButton("导出预测报告")
        exportBtn.clicked.connect(self._export_results)
        
        refreshBtn = QPushButton("刷新结果")
        refreshBtn.clicked.connect(self.refresh_results)
        
        openFolderBtn = QPushButton("打开结果文件夹")
        openFolderBtn.clicked.connect(self._open_output_folder)
        
        btnLayout.addWidget(exportBtn)
        btnLayout.addWidget(refreshBtn)
        btnLayout.addWidget(openFolderBtn)
        
        # 3. 添加到主布局
        layout.addWidget(self.tabs)
        layout.addLayout(btnLayout)
        
        self.setLayout(layout)
        
    def _create_latest_tab(self) -> QWidget:
        """创建最新预测标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 融合结果显示
        fusionGroup = QWidget()
        fusionLayout = QVBoxLayout()
        
        titleLabel = QLabel("融合推荐号码")
        titleLabel.setFont(QFont("Arial", 12, QFont.Bold))
        
        self.numbersLabel = QLabel()
        self.numbersLabel.setStyleSheet(
            "QLabel { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }"
        )
        
        fusionLayout.addWidget(titleLabel)
        fusionLayout.addWidget(self.numbersLabel)
        fusionGroup.setLayout(fusionLayout)
        
        # 模型预测表格
        self.modelTable = QTableWidget()
        self.modelTable.setColumnCount(3)
        self.modelTable.setHorizontalHeaderLabels(["模型", "预测号码", "置信度"])
        
        # 极限策略提示
        self.strategyText = QTextEdit()
        self.strategyText.setReadOnly(True)
        self.strategyText.setMaximumHeight(100)
        
        layout.addWidget(fusionGroup)
        layout.addWidget(self.modelTable)
        layout.addWidget(QLabel("触发策略:"))
        layout.addWidget(self.strategyText)
        
        tab.setLayout(layout)
        return tab
        
    def _create_history_tab(self) -> QWidget:
        """创建历史记录标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        self.historyTable = QTableWidget()
        self.historyTable.setColumnCount(5)
        self.historyTable.setHorizontalHeaderLabels([
            "预测日期", "推荐号码", "命中数", "命中率", "详情"
        ])
        
        layout.addWidget(self.historyTable)
        tab.setLayout(layout)
        return tab
        
    def _create_analysis_tab(self) -> QWidget:
        """创建分析详情标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        self.analysisText = QTextEdit()
        self.analysisText.setReadOnly(True)
        
        layout.addWidget(self.analysisText)
        tab.setLayout(layout)
        return tab
        
    def update_latest_prediction(self, data: Dict):
        """更新最新预测结果"""
        # 更新融合号码
        numbers = data.get('fusion_result', {}).get('numbers', '').split(',')
        self.numbersLabel.setText(
            f"{'、'.join(numbers)} (共{len(numbers)}个)"
        )
        
        # 更新模型预测表格
        model_predictions = data.get('model_predictions', [])
        self.modelTable.setRowCount(len(model_predictions))
        
        for i, pred in enumerate(model_predictions):
            self.modelTable.setItem(i, 0, QTableWidgetItem(pred['model_name']))
            self.modelTable.setItem(i, 1, QTableWidgetItem(pred['numbers']))
            self.modelTable.setItem(i, 2, QTableWidgetItem(f"{pred['confidence']:.2f}"))
            
        # 更新策略信息
        strategy_text = ""
        for strategy in data.get('strategies', []):
            if strategy.get('is_extreme'):
                strategy_text += f"🚨 {strategy['description']}\n"
            else:
                strategy_text += f"✅ {strategy['description']}\n"
                
        self.strategyText.setText(strategy_text)
        
    def update_history(self, history_data: List[Dict]):
        """更新历史记录"""
        self.historyTable.setRowCount(len(history_data))
        
        for i, record in enumerate(history_data):
            date = datetime.fromisoformat(record['prediction_date'])
            
            self.historyTable.setItem(i, 0, 
                QTableWidgetItem(date.strftime('%Y-%m-%d %H:%M')))
            self.historyTable.setItem(i, 1,
                QTableWidgetItem(record['fusion_numbers']))
            self.historyTable.setItem(i, 2,
                QTableWidgetItem(str(record.get('hit_count', '-'))))
            
            hit_rate = record.get('hit_rate', 0)
            rate_item = QTableWidgetItem(f"{hit_rate:.1%}" if hit_rate else '-')
            if hit_rate:
                rate_item.setBackground(
                    QColor("#d4edda") if hit_rate > 0.3 else QColor("#fff3cd")
                )
            self.historyTable.setItem(i, 3, rate_item)
            
            detailBtn = QPushButton("查看")
            detailBtn.clicked.connect(
                lambda checked, pid=record['prediction_id']: 
                self._show_prediction_detail(pid)
            )
            self.historyTable.setCellWidget(i, 4, detailBtn)
            
    def update_analysis(self, analysis_data: Dict):
        """更新分析详情"""
        text = ""
        
        # 1. 整体统计
        text += "==== 整体统计 ====\n"
        text += f"总预测次数: {analysis_data.get('total_predictions', 0)}\n"
        text += f"平均命中率: {analysis_data.get('avg_hit_rate', 0):.1%}\n"
        text += f"最佳命中率: {analysis_data.get('best_hit_rate', 0):.1%}\n\n"
        
        # 2. 模型表现
        text += "==== 模型表现 ====\n"
        for model in analysis_data.get('model_performance', []):
            text += f"● {model['name']}:\n"
            text += f"  命中率: {model['hit_rate']:.1%}\n"
            text += f"  稳定性: {model['stability']:.2f}\n\n"
            
        # 3. 策略分析
        text += "==== 策略分析 ====\n"
        for strategy in analysis_data.get('strategy_analysis', []):
            text += f"● {strategy['name']}:\n"
            text += f"  触发次数: {strategy['trigger_count']}\n"
            text += f"  成功率: {strategy['success_rate']:.1%}\n\n"
            
        self.analysisText.setText(text)
        
    def refresh_results(self):
        """刷新所有结果"""
        # 发送刷新信号
        pass
        
    def _export_results(self):
        """导出预测结果"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "选择保存位置",
            "预测报告_" + datetime.now().strftime("%Y%m%d_%H%M") + ".pdf",
            "PDF文件 (*.pdf);;Excel文件 (*.xlsx)"
        )
        
        if filename:
            self.exportRequested.emit(filename)
            
    def _open_output_folder(self):
        """打开输出文件夹"""
        os.startfile("outputs")
        
    def _show_prediction_detail(self, prediction_id: str):
        """显示预测详情"""
        # TODO: 实现预测详情对话框
        pass
