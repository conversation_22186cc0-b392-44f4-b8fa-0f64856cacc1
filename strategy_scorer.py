from collections import defaultdict

class StrategyScorerAndFusionEngine:
    def __init__(self, strategies, history_data):
        self.strategies = strategies
        self.history = history_data
        self.scores = {}

    def calculate_scores(self, hit_rate_weight=0.7, avg_omit_weight=0.3):
        """
        Calculates scores for each strategy based on historical performance.
        """
        for strat_id, strat_data in self.strategies.items():
            # This is a simplified scoring logic. A real implementation would
            # need to run the strategy over the historical data to get these metrics.
            # For now, we'll use placeholder values.
            hit_count = strat_data.get("hit_count", 0)
            total_periods = len(self.history)
            avg_omit_before_hit = strat_data.get("avg_omit_before_hit", 1)

            if total_periods == 0:
                hit_rate = 0
            else:
                hit_rate = hit_count / total_periods

            # Score calculation based on the formula provided in the document
            score = (hit_rate * hit_rate_weight) + \
                    (1 / (avg_omit_before_hit + 0.001)) * avg_omit_weight
            
            self.scores[strat_id] = {
                "score": score,
                "hit_rate": hit_rate,
                "avg_omit_before_hit": avg_omit_before_hit
            }
        return self.scores

    def fuse_predictions(self, predictions, method='weighted_union', top_n=12):
        """
        Fuses predictions from multiple strategies.
        'predictions' should be a list of dicts, e.g.:
        [
            {"strategy_id": "shengxiao_4", "weight": 0.8, "numbers": [1, 5, 10]},
            {"strategy_id": "wuxing_2", "weight": 0.6, "numbers": [5, 12, 18]},
        ]
        """
        if method == 'weighted_union':
            score_map = defaultdict(float)
            for pred in predictions:
                weight = pred.get("weight", 1.0)
                for number in pred["numbers"]:
                    score_map[number] += weight
            
            sorted_candidates = sorted(score_map.items(), key=lambda x: x[1], reverse=True)
            return [n for n, _ in sorted_candidates[:top_n]]
        
        elif method == 'intersection':
            if not predictions:
                return []
            
            number_sets = [set(p['numbers']) for p in predictions]
            intersected_numbers = set.intersection(*number_sets)
            return list(intersected_numbers)

        else:
            raise ValueError(f"Unsupported fusion method: {method}")

if __name__ == '__main__':
    # Example Usage
    
    # 1. Mock strategies and historical data
    mock_strategies = {
        "shengxiao_4_extreme": {
            "hit_count": 15,
            "avg_omit_before_hit": 5
        },
        "wuxing_2_hot": {
            "hit_count": 25,
            "avg_omit_before_hit": 3
        }
    }
    mock_history = [i for i in range(100)] # 100 periods

    # 2. Initialize and score strategies
    scorer = StrategyScorerAndFusionEngine(mock_strategies, mock_history)
    strategy_scores = scorer.calculate_scores()
    print("Strategy Scores:")
    print(strategy_scores)

    # 3. Mock predictions from these strategies
    mock_predictions = [
        {
            "strategy_id": "shengxiao_4_extreme", 
            "weight": strategy_scores["shengxiao_4_extreme"]["score"], 
            "numbers": [1, 8, 15, 22, 29, 36, 43]
        },
        {
            "strategy_id": "wuxing_2_hot", 
            "weight": strategy_scores["wuxing_2_hot"]["score"], 
            "numbers": [5, 8, 12, 22, 30, 36, 49]
        }
    ]

    # 4. Fuse predictions
    final_prediction_weighted = scorer.fuse_predictions(mock_predictions, method='weighted_union', top_n=10)
    print("\nFused Prediction (Weighted Union):")
    print(final_prediction_weighted)

    final_prediction_intersect = scorer.fuse_predictions(mock_predictions, method='intersection')
    print("\nFused Prediction (Intersection):")
    print(final_prediction_intersect)
