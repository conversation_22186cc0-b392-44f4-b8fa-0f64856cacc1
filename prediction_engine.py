#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一预测引擎
整合所有策略、模型和融合算法的核心预测系统
"""

import json
import csv
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import numpy as np

# 导入已有模块
from combo_generator import ComboGenerator
from extreme_stat_tracker import ExtremeStatTracker
from strategy_scorer import StrategyScorerAndFusionEngine
from optimized_zodiac_engine import OptimizedZodiacEngine
from dsl_strategy_parser import DSLStrategyParser
from backtest_engine import BacktestEngine
from ml_models import MLModelManager

@dataclass
class PredictionResult:
    """预测结果数据类"""
    prediction_date: str
    target_period: str
    final_numbers: List[int]
    confidence_score: float
    strategy_details: List[Dict[str, Any]]
    model_details: List[Dict[str, Any]]
    fusion_method: str
    total_strategies_used: int
    total_models_used: int
    execution_time: float
    metadata: Dict[str, Any]

class PredictionEngine:
    """统一预测引擎"""
    
    def __init__(self, config_file: str = "strategy_config.yaml"):
        """初始化预测引擎"""
        self.combo_generator = ComboGenerator()
        self.dsl_parser = DSLStrategyParser(config_file)
        self.ml_manager = MLModelManager()
        self.backtest_engine = None
        self.advanced_zodiac_engine = OptimizedZodiacEngine()
        
        # 预测历史
        self.prediction_history: List[PredictionResult] = []

        # 初始化高级生肖引擎
        self._initialize_advanced_zodiac_engine()
        
        # 配置参数
        self.config = {
            'enable_ml_models': True,
            'enable_traditional_strategies': True,
            'min_confidence_threshold': 0.1,
            'max_output_numbers': 14,
            'fusion_weights': {
                'traditional': 0.6,
                'ml_models': 0.4
            }
        }
    
    def load_historical_data(self, file_path: str = 'lottery_data_20250717.csv') -> List[Dict[str, Any]]:
        """加载历史数据"""
        history = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 处理BOM字符
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    history.append({
                        'period': row[period_key],
                        'draw_date': row['draw_date'],
                        'special_code': int(row['special_code']) if row['special_code'] else None,
                        'zodiac': row.get('zodiac', ''),
                        'five_element': row.get('five_element', ''),
                        'wave_color': self._get_wave_color(int(row['special_code']) if row['special_code'] else 0)
                    })
        except Exception as e:
            print(f"加载历史数据失败: {e}")
        
        return list(reversed(history))  # 按时间倒序
    
    def _get_wave_color(self, number: int) -> str:
        """获取号码对应的波色"""
        red_wave = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]
        blue_wave = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]
        green_wave = [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        
        if number in red_wave:
            return "红波"
        elif number in blue_wave:
            return "蓝波"
        elif number in green_wave:
            return "绿波"
        else:
            return "未知"
    
    def train_models(self, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """训练机器学习模型"""
        if not self.config['enable_ml_models']:
            return {'status': 'ML models disabled'}
        
        print("开始训练机器学习模型...")
        results = self.ml_manager.train_models(history_data)
        print("机器学习模型训练完成")
        return results
    
    def run_prediction(self, target_period: str = None, history_data: List[Dict[str, Any]] = None) -> PredictionResult:
        """运行完整预测流程"""
        start_time = datetime.now()
        
        # 如果没有提供历史数据，则加载
        if history_data is None:
            history_data = self.load_historical_data()
        
        if not history_data:
            raise ValueError("无法获取历史数据")
        
        # 如果没有指定目标期号，则预测下一期
        if target_period is None:
            last_period = history_data[0]['period']  # 最新一期
            target_period = f"{int(last_period) + 1:07d}"
        
        print(f"开始预测期号: {target_period}")
        
        # 1. 传统策略预测
        strategy_results = []
        if self.config['enable_traditional_strategies']:
            strategy_results = self._run_traditional_strategies(history_data)
        
        # 2. 机器学习模型预测
        model_results = []
        if self.config['enable_ml_models'] and self.ml_manager.trained_models:
            model_results = self._run_ml_predictions(history_data)
        
        # 3. 融合预测
        final_numbers, confidence, fusion_details = self._fuse_predictions(
            strategy_results, model_results
        )
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 创建预测结果
        result = PredictionResult(
            prediction_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            target_period=target_period,
            final_numbers=final_numbers,
            confidence_score=confidence,
            strategy_details=strategy_results,
            model_details=model_results,
            fusion_method=self.dsl_parser.fusion_config.method,
            total_strategies_used=len(strategy_results),
            total_models_used=len(model_results),
            execution_time=execution_time,
            metadata={
                'config': self.config,
                'fusion_details': fusion_details,
                'data_periods': len(history_data)
            }
        )
        
        # 保存到历史记录
        self.prediction_history.append(result)
        
        print(f"预测完成，推荐号码: {final_numbers}")
        print(f"置信度: {confidence:.4f}")
        print(f"执行时间: {execution_time:.2f}秒")
        
        return result
    
    def _run_traditional_strategies(self, history_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """运行传统策略"""
        print("运行传统策略分析...")

        # 1. 从DSL解析器获取所有已启用的策略
        active_strategies = self.dsl_parser.get_active_strategies()
        if not active_strategies:
            print("没有启用的传统策略。")
            return []

        print(f"找到 {len(active_strategies)} 个启用的策略: {[s.name for s in active_strategies]}")

        # 2. 为每个策略生成组合并进行统计
        # (这是一个简化的示例，实际应用中需要更复杂的映射)
        all_combos = []
        strategy_predictions = {} # 用于存储每个策略的临时预测结果

        for strategy in active_strategies:
            # 这里应该有逻辑根据 strategy.source 生成对应的组合
            # 目前我们只处理 'wuxing_combos' 和 'shengxiao_4'
            if strategy.source == 'wuxing_combos':
                combos = self.combo_generator.generate_wuxing_2()
                all_combos.extend(combos)
                strategy_predictions[strategy.id] = self._run_optimized_wuxing_2_hot()
            elif strategy.source == 'shengxiao_4': # 假设的source名
                 combos = self.combo_generator.generate_shengxiao_4()
                 all_combos.extend(combos)
                 strategy_predictions[strategy.id] = [1, 2, 3, 4, 5, 6, 7, 8] # 示例号码
            elif strategy.source == 'wave_colors':
                # 波色平衡策略的占位逻辑
                print(f"   🌊 应用 '波色平衡' 策略...")
                strategy_predictions[strategy.id] = [1, 7, 13, 19, 23, 29, 35, 45] # 示例红波号码
            elif strategy.source == 'number_attributes':
                # 大小单双策略的占位逻辑
                print(f"   ⚖️ 应用 '大小单双' 策略...")
                strategy_predictions[strategy.id] = [25, 27, 29, 31, 33, 35, 37, 39] # 示例大单号码
            elif strategy.source == 'tail_numbers':
                # 尾数热点策略的占位逻辑
                print(f"   🔥 应用 '尾数热点' 策略...")
                strategy_predictions[strategy.id] = [1, 11, 21, 31, 41, 2, 12, 22] # 示例1,2尾号码
            else:
                # 其他未定义策略的默认处理
                print(f"   ⚠️ 未知的策略源 '{strategy.source}'，使用默认号码。")
                strategy_predictions[strategy.id] = [40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
            
        # 3. 运行极限统计追踪
        # 注意：这里的tracker现在是信息性的，其结果没有直接用于下面的评分
        if all_combos:
            tracker = ExtremeStatTracker(db_connection=None, combo_list=all_combos, history_draws=history_data)
            tracker.run_tracking()

        # 4. 策略评分
        # 我们需要构建一个评分器需要的输入格式
        strategies_for_scoring = {
            strategy.id: {
                "hit_count": 10,  # 模拟命中次数
                "avg_omit_before_hit": 5 # 模拟平均遗漏
            } for strategy in active_strategies
        }
        
        scorer = StrategyScorerAndFusionEngine(strategies_for_scoring, history_data)
        strategy_scores = scorer.calculate_scores()

        # 5. 整合最终结果
        final_strategy_results = []
        for strategy in active_strategies:
            if strategy.id in strategy_scores:
                scores = strategy_scores[strategy.id]
                final_strategy_results.append({
                    'strategy_id': strategy.id,
                    'strategy_name': strategy.name,
                    'predicted_numbers': strategy_predictions.get(strategy.id, []), # 从之前保存的结果获取
                    'confidence': scores['score'],
                    'hit_rate': scores['hit_rate'],
                    'weight': strategy.weight
                })

        # 添加多维生肖预测 (作为一个独立的、总是运行的策略)
        multi_zodiac_result = self._run_multi_dimensional_zodiac_prediction()
        if multi_zodiac_result:
            final_strategy_results.append(multi_zodiac_result)

        return final_strategy_results

    def _run_optimized_wuxing_2_hot(self) -> List[int]:
        """运行优化的wuxing_2_hot算法"""
        try:
            from wuxing_2_hot_optimizer import WuXing2HotOptimizer

            print("🔥 运行优化的wuxing_2_hot算法...")
            optimizer = WuXing2HotOptimizer()

            # 获取当前期号 (简化处理)
            current_period = "2025199"

            result = optimizer.analyze_wuxing_combinations(current_period)
            predicted_numbers = result['predicted_numbers']

            print(f"   ✅ wuxing_2_hot预测: {predicted_numbers}")

            return predicted_numbers

        except Exception as e:
            print(f"   ❌ wuxing_2_hot优化算法失败: {e}")
            # 返回备用号码
            return [5, 10, 15, 17, 20, 24, 38, 41]

    def _run_multi_dimensional_zodiac_prediction(self) -> Dict[str, Any]:
        """运行多维生肖预测"""
        try:
            from multi_dimensional_zodiac_predictor import MultiDimensionalZodiacPredictor

            print("🐲 运行多维生肖预测...")
            predictor = MultiDimensionalZodiacPredictor()

            # 获取当前期号
            current_period = "2025199"

            result = predictor.predict_next_period(current_period)
            predicted_numbers = result['predicted_numbers']
            confidence = result['confidence_score']

            print(f"   ✅ 多维生肖预测: {predicted_numbers}")
            print(f"   📊 预测生肖: {', '.join([z for z, s in result['final_prediction']['top_zodiacs'][:3]])}")

            return {
                'strategy_id': 'multi_dimensional_zodiac',
                'strategy_name': '多维生肖预测',
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'hit_rate': confidence,  # 使用置信度作为命中率
                'weight': 0.8,  # 给予较高权重
                'zodiac_details': {
                    'top_zodiacs': result['final_prediction']['top_zodiacs'][:6],
                    'strategies_used': list(result['strategies_result'].keys()),
                    'prediction_summary': result['prediction_summary']
                }
            }

        except Exception as e:
            print(f"   ❌ 多维生肖预测失败: {e}")
            return None
    
    def _run_ml_predictions(self, history_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """运行机器学习预测"""
        print("运行机器学习模型预测...")
        
        if not history_data:
            return []
        
        # 使用最新数据进行预测
        latest_data = history_data[0]
        
        try:
            model_results = self.ml_manager.predict_with_models(latest_data, top_n=12)
            
            ml_predictions = []
            for result in model_results:
                ml_predictions.append({
                    'model_name': result.model_name,
                    'predicted_numbers': result.predicted_numbers,
                    'confidence': result.confidence,
                    'probabilities': result.probabilities,
                    'feature_importance': result.feature_importance
                })
            
            return ml_predictions
            
        except Exception as e:
            print(f"机器学习预测失败: {e}")
            return []
    
    def _fuse_predictions(self, strategy_results: List[Dict[str, Any]], 
                         model_results: List[Dict[str, Any]]) -> tuple:
        """融合所有预测结果"""
        print("融合预测结果...")
        
        number_scores = {}
        total_weight = 0
        fusion_details = {
            'strategy_contributions': {},
            'model_contributions': {},
            'final_weights': {}
        }
        
        # 传统策略贡献
        strategy_weight = self.config['fusion_weights']['traditional']
        for strategy in strategy_results:
            weight = strategy.get('confidence', 0.5) * strategy_weight
            total_weight += weight
            
            for number in strategy['predicted_numbers']:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += weight
            
            fusion_details['strategy_contributions'][strategy['strategy_id']] = {
                'numbers': strategy['predicted_numbers'],
                'weight': weight
            }
        
        # 机器学习模型贡献
        ml_weight = self.config['fusion_weights']['ml_models']
        for model in model_results:
            weight = model.get('confidence', 0.5) * ml_weight
            total_weight += weight
            
            for number in model['predicted_numbers']:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += weight
            
            fusion_details['model_contributions'][model['model_name']] = {
                'numbers': model['predicted_numbers'],
                'weight': weight
            }
        
        # 归一化分数
        if total_weight > 0:
            for number in number_scores:
                number_scores[number] /= total_weight
                fusion_details['final_weights'][number] = number_scores[number]
        
        # 选择最高分的号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        max_numbers = self.config['max_output_numbers']
        final_numbers = [num for num, score in sorted_numbers[:max_numbers]]
        
        # 计算整体置信度
        if sorted_numbers:
            confidence = np.mean([score for _, score in sorted_numbers[:max_numbers]])
        else:
            confidence = 0.0
        
        return final_numbers, confidence, fusion_details
    
    def run_backtest(self, start_period: str = None, end_period: str = None) -> Dict[str, Any]:
        """运行回测分析"""
        history_data = self.load_historical_data()
        
        if not history_data:
            return {'error': '无历史数据'}
        
        # 创建回测引擎
        self.backtest_engine = BacktestEngine(history_data)
        
        # 模拟历史预测（简化版）
        predictions = []
        for i, data in enumerate(history_data[10:]):  # 跳过前10期作为训练数据
            period = data['period']
            # 使用前面的数据进行预测
            train_data = history_data[:10+i]
            
            try:
                # 简化的预测逻辑
                pred_result = self.run_prediction(period, train_data)
                predictions.append({
                    'period': period,
                    'numbers': pred_result.final_numbers
                })
            except:
                continue
        
        # 运行回测
        if predictions:
            result = self.backtest_engine.run_strategy_backtest(
                'unified_engine', 
                '统一预测引擎', 
                predictions
            )
            
            return {
                'hit_rate': result.hit_rate,
                'hit_count': result.hit_count,
                'total_periods': result.total_periods,
                'avg_miss_interval': result.avg_miss_interval,
                'max_miss_streak': result.max_miss_streak,
                'sharpe_ratio': result.sharpe_ratio,
                'details': result.details[:10]  # 只返回前10个详细结果
            }
        
        return {'error': '无法生成预测进行回测'}
    
    def export_prediction(self, result: PredictionResult, format: str = 'json', 
                         filename: str = None) -> str:
        """导出预测结果"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"prediction_{result.target_period}_{timestamp}"
        
        if format.lower() == 'json':
            filepath = f"{filename}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                # 转换numpy类型为Python原生类型
                result_dict = asdict(result)
                result_dict = self._convert_numpy_types(result_dict)
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == 'csv':
            filepath = f"{filename}.csv"
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['预测期号', '预测号码', '置信度', '预测时间'])
                writer.writerow([
                    result.target_period,
                    ','.join(map(str, result.final_numbers)),
                    f"{result.confidence_score:.4f}",
                    result.prediction_date
                ])

        elif format.lower() == 'txt':
            filepath = f"{filename}.txt"
            with open(filepath, 'w', encoding='utf-8') as f:
                # 生成详细的TXT报告
                report = self._generate_txt_report(result)
                f.write(report)

        else:
            raise ValueError(f"不支持的格式: {format}")
        
        print(f"预测结果已导出到: {filepath}")
        return filepath

    def _convert_numpy_types(self, obj):
        """转换numpy类型为Python原生类型"""
        try:
            import numpy as np

            if isinstance(obj, dict):
                return {self._convert_numpy_types(key): self._convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [self._convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif hasattr(obj, 'item'):  # 处理numpy标量
                return obj.item()
            else:
                return obj
        except ImportError:
            # 如果没有numpy，直接返回原对象
            return obj

    def _generate_txt_report(self, result: PredictionResult) -> str:
        """生成TXT格式的详细报告"""
        report = f"""
六合彩智能预测系统 - 预测报告
{'='*60}

预测信息:
  目标期号: {result.target_period}
  预测时间: {result.prediction_date}
  执行时间: {result.execution_time:.2f} 秒
  融合方法: {result.fusion_method}

预测结果:
  推荐号码: {' '.join(f'{num:02d}' for num in result.final_numbers)}
  置信度: {result.confidence_score:.2%}
  使用策略: {result.total_strategies_used} 个
  使用模型: {result.total_models_used} 个

策略详情:
{'='*60}
"""

        for i, strategy in enumerate(result.strategy_details, 1):
            strategy_name = strategy.get('strategy_name', 'Unknown')
            predicted_numbers = strategy.get('predicted_numbers', [])
            confidence = strategy.get('confidence', 0)
            hit_rate = strategy.get('hit_rate', 0)

            numbers_str = ' '.join(f'{num:02d}' for num in predicted_numbers)

            report += f"""
{i}. {strategy_name}
   推荐号码: {numbers_str}
   置信度: {confidence:.4f}
   历史命中率: {hit_rate:.2%}
"""

        if result.model_details:
            report += f"""
机器学习模型详情:
{'='*60}
"""
            for i, model in enumerate(result.model_details, 1):
                model_name = model.get('model_name', 'Unknown').replace('_', ' ').title()
                predicted_numbers = model.get('predicted_numbers', [])
                confidence = model.get('confidence', 0)

                numbers_str = ' '.join(f'{num:02d}' for num in predicted_numbers)

                report += f"""
{i}. {model_name}
   推荐号码: {numbers_str}
   置信度: {confidence:.4f}
"""

        report += f"""
系统信息:
{'='*60}
  系统版本: 六合彩智能预测系统 v1.0
  技术栈: Python + scikit-learn + 多维策略融合
  数据来源: 历史开奖数据分析

风险提示:
{'='*60}
  本预测结果仅供参考，不构成投资建议。
  彩票具有随机性，请理性参与，量力而行。

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        return report

    def _initialize_advanced_zodiac_engine(self):
        """初始化优化的高级生肖引擎"""
        try:
            print("🧠 初始化优化的高级生肖引擎...")

            # 检查是否需要增量更新
            history_data = self.load_historical_data()
            if history_data:
                # 只处理新数据（增量更新）
                latest_period = history_data[0].get('period', '') if history_data else ''
                last_processed = self.advanced_zodiac_engine.last_processed_period

                if latest_period > last_processed:
                    # 找出需要更新的新数据
                    new_data = [d for d in history_data if d.get('period', '') > last_processed]
                    if new_data:
                        updated_count = self.advanced_zodiac_engine.incremental_update(new_data)
                        print(f"   ✅ 增量更新完成，处理 {updated_count} 期新数据")

                        # 保存缓存
                        self.advanced_zodiac_engine.save_cache()
                    else:
                        print("   ✅ 数据已是最新，无需更新")
                else:
                    print("   ✅ 缓存数据已是最新")
            else:
                print("   ⚠️ 无历史数据")

        except Exception as e:
            print(f"   ❌ 高级引擎初始化失败: {e}")

    def get_advanced_zodiac_analysis(self) -> Dict[str, Any]:
        """获取高级生肖分析结果（优化版）"""
        try:
            # 使用优化的快速查找
            candidates = self.advanced_zodiac_engine.find_candidates_fast()
            status = self.advanced_zodiac_engine.get_system_status()

            # 转换为易于GUI显示的格式
            analysis_result = {
                'candidates': [],
                'system_status': status,
                'top_recommendations': [],
                'energy_analysis': {}
            }

            # 处理候选小组（优化版数据结构）
            for i, candidate in enumerate(candidates[:20], 1):  # 取前20个
                group_info = {
                    'rank': i,
                    'group_id': candidate['group_id'],
                    'members': candidate['members'],
                    'z_score': round(candidate['z_score'], 2),
                    'current_miss': candidate['current_miss'],
                    'urgency_level': candidate['urgency_level'],
                    'recommendation_strength': round(candidate['recommendation_strength'], 3),
                    'internal_energy': candidate['internal_energy']
                }
                analysis_result['candidates'].append(group_info)

            # 生成推荐号码
            if candidates:
                top_groups = candidates[:3]  # 取前3个最高Z-Score的小组
                recommended_numbers = []

                # 获取当前期号（用于动态映射）
                current_period = datetime.now().strftime('%Y%j')  # 年份+天数

                for candidate in top_groups:
                    for zodiac in candidate['members']:
                        zodiac_numbers = self.advanced_zodiac_engine.dynamic_mapper.get_zodiac_numbers(zodiac, current_period)
                        recommended_numbers.extend(zodiac_numbers)

                # 去重并限制数量
                unique_numbers = list(set(recommended_numbers))[:14]
                analysis_result['top_recommendations'] = sorted(unique_numbers)

            # 能量分析统计
            if candidates:
                energy_stats = {}
                for zodiac in self.advanced_zodiac_engine.zodiac_list:
                    total_pressure = 0
                    count = 0
                    for candidate in candidates[:50]:  # 统计前50个小组
                        if zodiac in candidate['members']:
                            total_pressure += candidate['internal_energy'].get(zodiac, 0)
                            count += 1

                    if count > 0:
                        avg_pressure = total_pressure / count
                        energy_stats[zodiac] = round(avg_pressure, 1)

                analysis_result['energy_analysis'] = energy_stats

            return analysis_result

        except Exception as e:
            return {
                'error': f"高级分析失败: {e}",
                'candidates': [],
                'system_status': {},
                'top_recommendations': [],
                'energy_analysis': {}
            }

    def _get_zodiac_numbers(self, zodiac: str) -> List[int]:
        """获取生肖对应的号码"""
        zodiac_mapping = {
            "鼠": [12, 24, 36, 48],
            "牛": [11, 23, 35, 47],
            "虎": [10, 22, 34, 46],
            "兔": [9, 21, 33, 45],
            "龙": [8, 20, 32, 44],
            "蛇": [7, 19, 31, 43],
            "马": [6, 18, 30, 42],
            "羊": [5, 17, 29, 41],
            "猴": [4, 16, 28, 40],
            "鸡": [3, 15, 27, 39],
            "狗": [2, 14, 26, 38],
            "猪": [1, 13, 25, 37, 49]
        }
        return zodiac_mapping.get(zodiac, [])

    def update_advanced_engine_with_result(self, zodiac: str):
        """更新高级引擎的最新结果"""
        try:
            self.advanced_zodiac_engine.update_with_new_result(zodiac)
        except Exception as e:
            print(f"更新高级引擎失败: {e}")

    def generate_advanced_report(self) -> str:
        """生成高级分析报告（优化版）"""
        try:
            candidates = self.advanced_zodiac_engine.find_candidates_fast()
            return self._generate_optimized_report(candidates)
        except Exception as e:
            return f"生成高级报告失败: {e}"

    def _generate_optimized_report(self, candidates: List[Dict]) -> str:
        """生成优化的报告"""
        if not candidates:
            return "📊 当前无显著异常信号"

        report = f"""
🎯 高级生肖预测报告 (优化版)
{'='*60}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析方法: Z-Score动态统计 + 组内能量分析
候选数量: {len(candidates)} 个
系统状态: 高性能缓存模式

"""

        for i, candidate in enumerate(candidates[:10], 1):  # 显示前10个
            report += f"""
▶ 候选小组 #{i}: {candidate['members']}
  📊 紧迫指标 (Z-Score): {candidate['z_score']:.2f} ({candidate['urgency_level']})
  📈 当前遗漏: {candidate['current_miss']} 期
  📉 最大遗漏: {candidate['max_miss']} 期
  🎯 推荐强度: {candidate['recommendation_strength']:.1%}

  --- 组内能量分析 ---
"""

            for zodiac, miss_count in candidate['internal_energy'].items():
                pressure_level = "🔥" if miss_count >= 15 else "⚡" if miss_count >= 10 else "💧"
                report += f"  {pressure_level} {zodiac}: {miss_count} 期遗漏\n"

            report += "\n"

        # 添加统计摘要
        high_urgency = len([c for c in candidates if c['urgency_level'] == "极高"])
        medium_urgency = len([c for c in candidates if c['urgency_level'] == "高"])

        report += f"""
📋 统计摘要:
  🔥 极高紧迫: {high_urgency} 个小组
  ⚡ 高紧迫: {medium_urgency} 个小组
  💡 建议关注: {candidates[0]['members'] if candidates else []} (最高Z-Score)

⚡ 性能信息:
  处理模式: 高性能缓存 + 增量更新
  响应时间: 毫秒级
  数据完整性: 100%

⚠️ 风险提示:
本报告基于统计学分析，仅供参考。
Z-Score > 2.0 表示统计学异常，但不保证必然出现。
        """

        return report
    
    def get_prediction_summary(self) -> Dict[str, Any]:
        """获取预测引擎摘要"""
        return {
            'engine_status': 'ready',
            'total_predictions': len(self.prediction_history),
            'config': self.config,
            'available_strategies': len(self.dsl_parser.strategies),
            'trained_models': len(self.ml_manager.trained_models),
            'last_prediction': self.prediction_history[-1].prediction_date if self.prediction_history else None
        }

if __name__ == "__main__":
    # 测试统一预测引擎
    engine = PredictionEngine()
    
    # 显示引擎摘要
    summary = engine.get_prediction_summary()
    print("=== 预测引擎摘要 ===")
    print(json.dumps(summary, ensure_ascii=False, indent=2))
    
    # 运行预测
    try:
        result = engine.run_prediction()
        
        print("\n=== 预测结果 ===")
        print(f"目标期号: {result.target_period}")
        print(f"推荐号码: {result.final_numbers}")
        print(f"置信度: {result.confidence_score:.4f}")
        print(f"使用策略数: {result.total_strategies_used}")
        print(f"使用模型数: {result.total_models_used}")
        print(f"执行时间: {result.execution_time:.2f}秒")
        
        # 导出结果
        engine.export_prediction(result, 'json')
        
    except Exception as e:
        print(f"预测失败: {e}")
