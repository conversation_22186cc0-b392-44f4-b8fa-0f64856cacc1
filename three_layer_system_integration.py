#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三层筛选系统集成
整合三层筛选引擎、极限追踪、回测系统，提供统一接口
"""

import os
import json
import yaml
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

# 导入各个模块
from three_layer_filter_engine import ThreeLayerFilterEngine, FilterResult
from extreme_tracker_backtest import ExtremeStatTracker, BacktestEngine, BacktestResult

@dataclass
class IntegratedPredictionResult:
    """集成预测结果"""
    period: str
    recommended_numbers: List[int]
    confidence: float
    filter_details: Dict[str, Any]
    extreme_analysis: Dict[str, Any]
    backtest_performance: Dict[str, Any]
    metadata: Dict[str, Any]

class DSLConfigManager:
    """DSL配置管理器"""
    
    def __init__(self, config_file: str = "三层筛选策略配置.yaml"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """加载DSL配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                print(f"✅ DSL配置加载成功: {self.config_file}")
                return config
        except FileNotFoundError:
            print(f"⚠️ 配置文件不存在: {self.config_file}")
            return self.create_default_config()
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return self.create_default_config()
    
    def create_default_config(self) -> Dict:
        """创建默认配置"""
        default_config = {
            'strategy_name': '默认三层筛选策略',
            'version': '1.0',
            'base_pool': {'range': [1, 49], 'exclude': []},
            'output_control': {'target_range': [12, 16]},
            'layer1_big_filter': {'enabled': True},
            'layer2_medium_filter': {'enabled': True},
            'layer3_fine_filter': {'enabled': True}
        }
        
        # 保存默认配置
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ 默认配置已创建: {self.config_file}")
        except Exception as e:
            print(f"⚠️ 默认配置保存失败: {e}")
        
        return default_config
    
    def update_config(self, updates: Dict):
        """更新配置"""
        self.config.update(updates)
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            print("✅ 配置更新成功")
        except Exception as e:
            print(f"❌ 配置更新失败: {e}")
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        required_keys = ['strategy_name', 'base_pool', 'output_control']
        
        for key in required_keys:
            if key not in self.config:
                print(f"❌ 配置验证失败: 缺少必需键 '{key}'")
                return False
        
        # 验证号码范围
        base_pool = self.config.get('base_pool', {})
        number_range = base_pool.get('range', [1, 49])
        if not isinstance(number_range, list) or len(number_range) != 2:
            print("❌ 配置验证失败: base_pool.range 格式错误")
            return False
        
        # 验证输出控制
        output_control = self.config.get('output_control', {})
        target_range = output_control.get('target_range', [12, 16])
        if not isinstance(target_range, list) or len(target_range) != 2:
            print("❌ 配置验证失败: output_control.target_range 格式错误")
            return False
        
        print("✅ 配置验证通过")
        return True

class ThreeLayerIntegratedSystem:
    """三层筛选集成系统"""
    
    def __init__(self, config_file: str = "三层筛选策略配置.yaml", db_file: str = "three_layer_system.db"):
        print("🚀 初始化三层筛选集成系统...")
        
        # 初始化各个组件
        self.config_manager = DSLConfigManager(config_file)
        self.filter_engine = ThreeLayerFilterEngine(config_file)
        self.extreme_tracker = ExtremeStatTracker(db_file)
        self.backtest_engine = BacktestEngine(self.extreme_tracker)
        
        # 验证配置
        if not self.config_manager.validate_config():
            raise ValueError("配置验证失败")
        
        # 注册默认组合追踪
        self._register_default_combinations()
        
        print("✅ 三层筛选集成系统初始化完成")
    
    def _register_default_combinations(self):
        """注册默认的组合追踪"""
        print("🔄 注册默认组合追踪...")
        
        # 注册生肖组合
        zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        for zodiac in zodiacs:
            self.extreme_tracker.register_combination(f"zodiac_{zodiac}", "zodiac", zodiac)
        
        # 注册波色组合
        colors = ['红', '蓝', '绿']
        for color in colors:
            self.extreme_tracker.register_combination(f"color_{color}", "color", color)
        
        # 注册五行组合
        wuxings = ['金', '木', '水', '火', '土']
        for wuxing in wuxings:
            self.extreme_tracker.register_combination(f"wuxing_{wuxing}", "wuxing", wuxing)
        
        print("✅ 默认组合追踪注册完成")
    
    def predict(self, target_period: str, include_extreme_analysis: bool = True) -> IntegratedPredictionResult:
        """执行集成预测"""
        print(f"🎯 开始集成预测 - 期号: {target_period}")
        print("=" * 60)
        
        # 执行三层筛选
        filter_result = self.filter_engine.predict(target_period)
        
        # 极限分析
        extreme_analysis = {}
        if include_extreme_analysis:
            extreme_analysis = self._analyze_extreme_status(filter_result.numbers)
        
        # 获取回测性能
        backtest_performance = self._get_recent_backtest_performance()
        
        # 构建集成结果
        integrated_result = IntegratedPredictionResult(
            period=target_period,
            recommended_numbers=filter_result.numbers,
            confidence=filter_result.confidence,
            filter_details=filter_result.layer_details,
            extreme_analysis=extreme_analysis,
            backtest_performance=backtest_performance,
            metadata={
                'strategy_name': self.config_manager.config.get('strategy_name', '三层筛选策略'),
                'prediction_time': datetime.now().isoformat(),
                'system_version': '1.0',
                'filter_metadata': filter_result.metadata
            }
        )
        
        print(f"🎉 集成预测完成!")
        print(f"🎯 推荐号码: {integrated_result.recommended_numbers}")
        print(f"📊 置信度: {integrated_result.confidence:.2%}")
        print(f"🔥 极限组合数: {len(extreme_analysis.get('critical_combinations', []))}")
        
        return integrated_result
    
    def _analyze_extreme_status(self, recommended_numbers: List[int]) -> Dict[str, Any]:
        """分析极限状态"""
        print("🔍 分析极限状态...")
        
        # 获取所有极限组合
        all_extremes = self.extreme_tracker.get_all_extreme_combinations()
        
        # 分类极限组合
        critical_combinations = [combo for combo in all_extremes if combo.get('warning_level') == 'critical']
        extreme_combinations = [combo for combo in all_extremes if combo.get('warning_level') == 'extreme']
        warning_combinations = [combo for combo in all_extremes if combo.get('warning_level') == 'warning']
        
        # 分析推荐号码中的极限情况
        recommended_extreme_count = 0
        for number in recommended_numbers:
            # 检查这个号码是否属于极限组合
            for combo in extreme_combinations + critical_combinations:
                if self._number_in_combination(number, combo):
                    recommended_extreme_count += 1
                    break
        
        extreme_analysis = {
            'total_extreme_combinations': len(all_extremes),
            'critical_combinations': critical_combinations,
            'extreme_combinations': extreme_combinations,
            'warning_combinations': warning_combinations,
            'recommended_extreme_count': recommended_extreme_count,
            'extreme_ratio': recommended_extreme_count / len(recommended_numbers) if recommended_numbers else 0,
            'analysis_time': datetime.now().isoformat()
        }
        
        print(f"   极限组合总数: {len(all_extremes)}")
        print(f"   危险级别: {len(critical_combinations)}")
        print(f"   极限级别: {len(extreme_combinations)}")
        print(f"   推荐号码中极限数量: {recommended_extreme_count}")
        
        return extreme_analysis
    
    def _number_in_combination(self, number: int, combination: Dict) -> bool:
        """检查号码是否属于组合"""
        combination_type = combination.get('combination_type', '')
        combination_value = combination.get('combination_value', '')
        
        if combination_type == 'zodiac':
            zodiac_map = {i: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'][(i-1) % 12] for i in range(1, 50)}
            return zodiac_map.get(number) == combination_value
        elif combination_type == 'color':
            color_map = {i: ['红', '蓝', '绿'][i % 3] for i in range(1, 50)}
            return color_map.get(number) == combination_value
        elif combination_type == 'wuxing':
            wuxing_map = {i: ['金', '木', '水', '火', '土'][(i-1) % 5] for i in range(1, 50)}
            return wuxing_map.get(number) == combination_value
        
        return False
    
    def _get_recent_backtest_performance(self) -> Dict[str, Any]:
        """获取最近的回测性能"""
        # 简化实现：返回模拟的回测性能
        return {
            'recent_hit_rate': 0.25,
            'recent_avg_miss': 3.2,
            'recent_max_miss': 12,
            'recent_roi': 0.15,
            'performance_trend': 'stable',
            'last_backtest_date': datetime.now().strftime('%Y-%m-%d')
        }
    
    def run_full_backtest(self, history_data: List[Dict], test_periods: int = 100) -> BacktestResult:
        """运行完整回测"""
        print(f"🔄 开始完整回测 - 测试期数: {test_periods}")
        
        # 使用回测引擎进行回测
        backtest_result = self.backtest_engine.run_backtest(
            self.filter_engine, 
            history_data, 
            test_periods
        )
        
        return backtest_result
    
    def update_period_result(self, period: str, winning_number: int):
        """更新期号结果"""
        print(f"📊 更新期号结果: {period} -> {winning_number}")
        self.extreme_tracker.update_period_result(period, winning_number)
    
    def generate_comprehensive_report(self, prediction_result: IntegratedPredictionResult) -> str:
        """生成综合报告"""
        report = f"""
# 三层筛选系统综合预测报告

## 预测信息
- 目标期号: {prediction_result.period}
- 预测时间: {prediction_result.metadata.get('prediction_time', 'Unknown')}
- 策略名称: {prediction_result.metadata.get('strategy_name', 'Unknown')}

## 推荐号码
**精选号码**: {', '.join(f'{num:02d}' for num in prediction_result.recommended_numbers)}
**号码数量**: {len(prediction_result.recommended_numbers)} 个
**预测置信度**: {prediction_result.confidence:.2%}

## 三层筛选详情
"""
        
        # 添加筛选层级详情
        filter_details = prediction_result.filter_details
        for layer, details in filter_details.items():
            if isinstance(details, dict):
                report += f"\n### {layer.upper()}\n"
                for key, value in details.items():
                    if isinstance(value, (int, float)):
                        report += f"- {key}: {value}\n"
                    elif isinstance(value, list) and len(value) <= 10:
                        report += f"- {key}: {value}\n"

        # 添加极限分析
        extreme_analysis = prediction_result.extreme_analysis
        if extreme_analysis:
            report += f"""
## 极限分析
- 总极限组合数: {extreme_analysis.get('total_extreme_combinations', 0)}
- 危险级别组合: {len(extreme_analysis.get('critical_combinations', []))}
- 极限级别组合: {len(extreme_analysis.get('extreme_combinations', []))}
- 推荐号码中极限数量: {extreme_analysis.get('recommended_extreme_count', 0)}
- 极限比例: {extreme_analysis.get('extreme_ratio', 0):.1%}
"""

        # 添加回测性能
        backtest_performance = prediction_result.backtest_performance
        if backtest_performance:
            report += f"""
## 回测性能
- 最近命中率: {backtest_performance.get('recent_hit_rate', 0):.2%}
- 平均遗漏: {backtest_performance.get('recent_avg_miss', 0):.1f} 期
- 最大遗漏: {backtest_performance.get('recent_max_miss', 0)} 期
- ROI: {backtest_performance.get('recent_roi', 0):.2%}
- 性能趋势: {backtest_performance.get('performance_trend', 'Unknown')}
"""

        report += f"""
## 风险提示
⚠️ 本预测基于历史数据分析，仅供参考
⚠️ 彩票具有随机性，请理性参与
⚠️ 投资有风险，请量力而行

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
系统版本: {prediction_result.metadata.get('system_version', '1.0')}
"""
        
        return report
    
    def export_prediction_result(self, result: IntegratedPredictionResult, format: str = 'json') -> str:
        """导出预测结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format.lower() == 'json':
            filename = f"prediction_{result.period}_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(asdict(result), f, ensure_ascii=False, indent=2)
        
        elif format.lower() == 'txt':
            filename = f"prediction_{result.period}_{timestamp}.txt"
            report = self.generate_comprehensive_report(result)
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
        
        else:
            raise ValueError(f"不支持的导出格式: {format}")
        
        print(f"✅ 预测结果已导出: {filename}")
        return filename

def test_integrated_system():
    """测试集成系统"""
    print("🧪 测试三层筛选集成系统")
    print("=" * 60)
    
    try:
        # 初始化集成系统
        system = ThreeLayerIntegratedSystem()
        
        # 执行预测
        result = system.predict("2025210")
        
        # 生成报告
        report = system.generate_comprehensive_report(result)
        print("\n📋 综合报告预览:")
        print(report[:500] + "..." if len(report) > 500 else report)
        
        # 导出结果
        json_file = system.export_prediction_result(result, 'json')
        txt_file = system.export_prediction_result(result, 'txt')
        
        print(f"\n✅ 测试完成!")
        print(f"   JSON导出: {json_file}")
        print(f"   TXT导出: {txt_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_integrated_system()
