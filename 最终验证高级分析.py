#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证高级分析 - 确认GUI中的高级分析功能完全正常
"""

def final_verification():
    """最终验证"""
    print("🎯 最终验证高级分析功能")
    print("=" * 60)
    
    try:
        # 模拟GUI调用方式
        from prediction_engine_adapter import PredictionEngineAdapter
        
        print("1️⃣ 初始化适配器（模拟GUI初始化）...")
        adapter = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        print("\n2️⃣ 调用高级分析（模拟GUI调用）...")
        analysis_result = adapter.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 高级分析失败:")
            print("   错误信息:", analysis_result['error'])
            return False
        
        print("✅ 高级分析调用成功")
        
        print("\n3️⃣ 验证返回数据结构...")
        
        # 检查必要的键
        required_keys = ['candidates', 'system_status', 'top_recommendations', 'energy_analysis']
        for key in required_keys:
            if key in analysis_result:
                print("   ✅", key, "存在")
            else:
                print("   ❌", key, "缺失")
                return False
        
        print("\n4️⃣ 验证数据内容...")
        
        candidates = analysis_result['candidates']
        recommendations = analysis_result['top_recommendations']
        system_status = analysis_result['system_status']
        energy_analysis = analysis_result['energy_analysis']
        
        print("   候选组合数量:", len(candidates))
        print("   推荐组合数量:", len(recommendations))
        print("   系统状态:", system_status.get('status', 'unknown'))
        print("   平均能量:", round(energy_analysis.get('avg_energy', 0), 3))
        
        print("\n5️⃣ 测试报告生成...")
        report = adapter.generate_advanced_report()
        
        if "报告生成失败" in report:
            print("❌ 报告生成失败")
            return False
        
        print("✅ 报告生成成功，长度:", len(report), "字符")
        
        print("\n6️⃣ 多次调用测试（检查稳定性）...")
        for i in range(3):
            test_result = adapter.get_advanced_zodiac_analysis()
            if 'error' in test_result:
                print("❌ 第", i+1, "次调用失败")
                return False
            print("   ✅ 第", i+1, "次调用成功")
        
        print("\n🎉 所有验证通过！")
        return True
        
    except Exception as e:
        print("❌ 验证过程中出现异常:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        
        import traceback
        print("\n详细错误:")
        traceback.print_exc()
        
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔧 测试GUI集成")
    print("-" * 40)
    
    try:
        # 测试GUI主要组件
        print("1️⃣ 测试GUI主要组件导入...")
        
        # 测试适配器在GUI环境中的工作
        from prediction_engine_adapter import PredictionEngineAdapter
        adapter = PredictionEngineAdapter()
        
        # 测试GUI可能调用的所有方法
        methods_to_test = [
            'get_advanced_zodiac_analysis',
            'generate_advanced_report',
            'run_prediction',
            'get_prediction_summary',
            'load_historical_data'
        ]
        
        print("2️⃣ 测试GUI调用的方法...")
        for method_name in methods_to_test:
            try:
                method = getattr(adapter, method_name)
                
                if method_name == 'run_prediction':
                    result = method("2025210")
                    assert hasattr(result, 'final_numbers')
                elif method_name == 'get_advanced_zodiac_analysis':
                    result = method()
                    assert 'candidates' in result or 'error' in result
                elif method_name == 'generate_advanced_report':
                    result = method()
                    assert isinstance(result, str)
                elif method_name == 'get_prediction_summary':
                    result = method()
                    assert isinstance(result, dict)
                elif method_name == 'load_historical_data':
                    result = method()
                    assert isinstance(result, list)
                
                print("   ✅", method_name, "测试通过")
                
            except Exception as e:
                print("   ❌", method_name, "测试失败:", str(e))
                return False
        
        print("✅ GUI集成测试通过")
        return True
        
    except Exception as e:
        print("❌ GUI集成测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动最终验证...")
    
    # 运行验证
    verification_result = final_verification()
    integration_result = test_gui_integration()
    
    print("\n" + "="*60)
    print("📋 最终验证结果")
    print("="*60)
    
    print("高级分析功能验证:", "✅ 通过" if verification_result else "❌ 失败")
    print("GUI集成测试:", "✅ 通过" if integration_result else "❌ 失败")
    
    overall_success = verification_result and integration_result
    
    print("\n🎯 总体结果:", "✅ 完全成功" if overall_success else "❌ 存在问题")
    
    if overall_success:
        print("\n🎉 恭喜！高级分析功能已完全修复")
        print("✅ 错误 'unsupported format string passed to dict.format' 已彻底解决")
        print("✅ GUI中的高级分析功能可以正常使用")
        print("✅ 所有格式化问题已修复")
        print("✅ 适配器与GUI完美集成")
        
        print("\n💡 现在可以安全使用:")
        print("   - start_lottery_gui.bat 启动GUI")
        print("   - GUI中的高级分析功能")
        print("   - 所有预测和分析功能")
        
    else:
        print("\n⚠️ 仍有问题需要解决")
    
    return overall_success

if __name__ == "__main__":
    main()
