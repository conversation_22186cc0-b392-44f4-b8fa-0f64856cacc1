from typing import Dict, List
from datetime import datetime, timedelta
import sqlite3

class ContextTracker:
    """会话上下文跟踪器"""
    def __init__(self, db_path: str = 'lottery_data.db'):
        self.db_path = db_path
        self._init_context_table()
        
    def _init_context_table(self):
        """初始化上下文跟踪表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS context_records (
                    record_id TEXT PRIMARY KEY,
                    session_id TEXT,
                    context_type TEXT,  -- 'prediction', 'analysis', 'backtest'
                    start_time TEXT,
                    end_time TEXT,
                    duration INTEGER,   -- 持续时间(秒)
                    context_data TEXT   -- 上下文数据JSON
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS context_statistics (
                    stat_type TEXT PRIMARY KEY,
                    total_sessions INTEGER,
                    avg_duration INTEGER,
                    max_duration INTEGER,
                    last_update TEXT
                )
            ''')
            
    def get_context_statistics(self) -> Dict:
        """获取上下文统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            # 基本统计
            basic_stats = conn.execute('''
                SELECT 
                    COUNT(DISTINCT session_id) as total_sessions,
                    AVG(duration) as avg_duration,
                    MAX(duration) as max_duration
                FROM context_records
                WHERE start_time >= date('now', '-30 day')
            ''').fetchone()
            
            # 按类型统计
            type_stats = conn.execute('''
                SELECT 
                    context_type,
                    COUNT(*) as count,
                    AVG(duration) as avg_duration
                FROM context_records
                WHERE start_time >= date('now', '-30 day')
                GROUP BY context_type
            ''').fetchall()
            
            # 会话长度分布
            duration_dist = conn.execute('''
                SELECT 
                    CASE 
                        WHEN duration < 300 THEN '5分钟内'
                        WHEN duration < 900 THEN '5-15分钟'
                        WHEN duration < 1800 THEN '15-30分钟'
                        ELSE '30分钟以上'
                    END as duration_range,
                    COUNT(*) as count
                FROM context_records
                WHERE start_time >= date('now', '-30 day')
                GROUP BY duration_range
            ''').fetchall()
            
        return {
            'basic_stats': {
                'total_sessions': basic_stats[0],
                'avg_duration_minutes': round(basic_stats[1] / 60, 1) if basic_stats[1] else 0,
                'max_duration_minutes': round(basic_stats[2] / 60, 1) if basic_stats[2] else 0
            },
            'type_stats': {
                t[0]: {'count': t[1], 'avg_duration_minutes': round(t[2] / 60, 1)}
                for t in type_stats
            },
            'duration_distribution': {
                d[0]: d[1] for d in duration_dist
            }
        }
        
    def get_current_session_context(self) -> Dict:
        """获取当前会话的上下文信息"""
        with sqlite3.connect(self.db_path) as conn:
            # 获取最近的会话记录
            recent = conn.execute('''
                SELECT 
                    session_id,
                    context_type,
                    start_time,
                    context_data
                FROM context_records
                WHERE start_time >= datetime('now', '-1 hour')
                ORDER BY start_time DESC
                LIMIT 1
            ''').fetchone()
            
            if not recent:
                return {'status': 'no_active_session'}
                
            # 获取该会话的所有上下文记录
            session_records = conn.execute('''
                SELECT 
                    context_type,
                    start_time,
                    end_time,
                    duration,
                    context_data
                FROM context_records
                WHERE session_id = ?
                ORDER BY start_time DESC
            ''', (recent[0],)).fetchall()
            
        return {
            'session_id': recent[0],
            'current_context': {
                'type': recent[1],
                'start_time': recent[2],
                'data': recent[3]
            },
            'session_history': [
                {
                    'type': r[0],
                    'start_time': r[1],
                    'end_time': r[2],
                    'duration': r[3],
                    'data': r[4]
                }
                for r in session_records
            ]
        }
        
    def format_context_report(self) -> str:
        """生成人类可读的上下文报告"""
        stats = self.get_context_statistics()
        current = self.get_current_session_context()
        
        report = []
        report.append("=== 上下文分析报告 ===")
        report.append(f"\n1. 基本统计(近30天)")
        report.append(f"- 总会话数: {stats['basic_stats']['total_sessions']}")
        report.append(f"- 平均持续时间: {stats['basic_stats']['avg_duration_minutes']}分钟")
        report.append(f"- 最长会话时间: {stats['basic_stats']['max_duration_minutes']}分钟")
        
        report.append("\n2. 按类型统计")
        for t, s in stats['type_stats'].items():
            report.append(f"- {t}: {s['count']}次, 平均{s['avg_duration_minutes']}分钟")
            
        report.append("\n3. 会话时长分布")
        for d, c in stats['duration_distribution'].items():
            report.append(f"- {d}: {c}次")
            
        if current.get('session_id'):
            report.append("\n4. 当前会话")
            report.append(f"- 会话ID: {current['session_id']}")
            report.append(f"- 当前上下文类型: {current['current_context']['type']}")
            report.append(f"- 开始时间: {current['current_context']['start_time']}")
            report.append(f"\n历史记录:")
            for h in current['session_history'][:5]:  # 只显示最近5条
                report.append(f"- [{h['type']}] {h['start_time']} ({h['duration']}秒)")
                
        return "\n".join(report)
