import unittest
from tail_group_filter import TailGroupFilter
import random
from typing import List, Set

class TestTailGroupFilterComprehensive(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        cls.filter = TailGroupFilter()
        cls.test_pool = list(range(1, 49))
        # 生成一些随机的历史数据用于测试
        cls.history_data = random.sample(cls.test_pool, 20)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.filter)
        self.assertIsInstance(self.filter.groups, list)
        self.assertIsInstance(self.filter.cache, dict)

    def test_get_numbers_by_tail(self):
        """测试尾数匹配功能"""
        # 测试基本尾数匹配
        tails = [1, 2]
        result = self.filter.get_numbers_by_tail(tails)
        self.assertTrue(all(num % 10 in tails for num in result))
        
        # 测试边界条件
        tails_empty = []
        result_empty = self.filter.get_numbers_by_tail(tails_empty)
        self.assertEqual(len(result_empty), 0)
        
        # 测试缓存机制
        result_cached = self.filter.get_numbers_by_tail(tails)
        self.assertEqual(result, result_cached)

    def test_filter_candidates(self):
        """测试候选号码筛选"""
        candidates = list(range(1, 10))
        result = self.filter.filter_candidates(candidates)
        
        self.assertIsInstance(result, dict)
        for label, numbers in result.items():
            self.assertIsInstance(numbers, list)
            self.assertTrue(all(num in candidates for num in numbers))

    def test_analyze_history(self):
        """测试历史数据分析"""
        stats = self.filter.analyze_history(self.history_data)
        
        for label, stat in stats.items():
            self.assertIn('hits', stat)
            self.assertIn('hit_rate', stat)
            self.assertIn('matched_count', stat)
            self.assertTrue(0 <= stat['hit_rate'] <= 1)
            self.assertTrue(stat['hits'] <= len(self.history_data))

    def test_get_hot_groups(self):
        """测试热门组合获取"""
        top_n = 5
        hot_groups = self.filter.get_hot_groups(self.history_data, top_n)
        
        self.assertLessEqual(len(hot_groups), top_n)
        if hot_groups:
            # 验证排序是否正确
            rates = [group[1]['hit_rate'] for group in hot_groups]
            self.assertEqual(rates, sorted(rates, reverse=True))

    def test_performance(self):
        """测试性能"""
        import time
        
        # 测试大量数据处理性能
        start_time = time.time()
        large_history = random.sample(range(1, 1000), 100)
        self.filter.analyze_history(large_history)
        duration = time.time() - start_time
        
        self.assertLess(duration, 1.0)  # 确保处理时间在1秒以内

    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空数据
        empty_result = self.filter.analyze_history([])
        self.assertIsInstance(empty_result, dict)
        
        # 测试异常输入
        with self.assertRaises(TypeError):
            self.filter.get_numbers_by_tail(None)
        
        with self.assertRaises(TypeError):
            self.filter.filter_candidates(None)

    def test_data_consistency(self):
        """测试数据一致性"""
        # 测试多次调用结果一致性
        result1 = self.filter.get_hot_groups(self.history_data)
        result2 = self.filter.get_hot_groups(self.history_data)
        self.assertEqual(result1, result2)

if __name__ == '__main__':
    unittest.main(verbosity=2)
