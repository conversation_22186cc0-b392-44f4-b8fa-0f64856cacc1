#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测评估引擎
支持策略级和模型级的历史回测分析
"""

import json
import csv
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import statistics

@dataclass
class BacktestResult:
    """回测结果数据类"""
    strategy_id: str
    strategy_name: str
    total_periods: int
    hit_count: int
    hit_rate: float
    avg_miss_interval: float
    max_miss_streak: int
    current_miss_streak: int
    avg_hit_rank: float  # 命中时的平均排名
    coverage_rate: float  # 覆盖率
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float
    details: List[Dict[str, Any]]

class BacktestEngine:
    """回测评估引擎"""
    
    def __init__(self, history_data: List[Dict[str, Any]]):
        self.history_data = history_data
        self.results: Dict[str, BacktestResult] = {}
    
    def run_strategy_backtest(self, strategy_id: str, strategy_name: str,
                            predictions: List[Dict[str, Any]],
                             progress_callback=None) -> BacktestResult:
        """
        运行策略回测
        
        Args:
            strategy_id: 策略ID
            strategy_name: 策略名称
            predictions: 预测结果列表 [{'period': '2025001', 'numbers': [1,2,3...]}]
        
        Returns:
            BacktestResult: 回测结果
        """
        hit_count = 0
        miss_streaks = []
        current_miss = 0
        hit_ranks = []
        details = []
        
        # 创建历史数据索引
        history_index = {item['period']: item for item in self.history_data}
        
        for i, pred in enumerate(predictions):
            period = pred['period']
            predicted_numbers = pred['numbers']

            # 进度回调
            if progress_callback:
                progress = (i + 1) / len(predictions) * 100
                progress_callback(progress, f"回测进度: {i+1}/{len(predictions)} - 期号 {period}")
            
            if period not in history_index:
                continue
            
            actual_number = history_index[period].get('special_code')
            if actual_number is None:
                continue
            
            # 检查是否命中
            is_hit = actual_number in predicted_numbers
            
            if is_hit:
                hit_count += 1
                if current_miss > 0:
                    miss_streaks.append(current_miss)
                    current_miss = 0
                
                # 计算命中排名
                try:
                    hit_rank = predicted_numbers.index(actual_number) + 1
                    hit_ranks.append(hit_rank)
                except ValueError:
                    hit_rank = len(predicted_numbers) + 1
            else:
                current_miss += 1
            
            # 记录详细信息
            detail = {
                'period': period,
                'predicted': predicted_numbers,
                'actual': actual_number,
                'is_hit': is_hit,
                'hit_rank': hit_rank if is_hit else None,
                'miss_before': current_miss if is_hit else current_miss
            }
            details.append(detail)
        
        # 添加最后的遗漏期数
        if current_miss > 0:
            miss_streaks.append(current_miss)
        
        # 计算统计指标
        total_periods = len(predictions)
        hit_rate = hit_count / total_periods if total_periods > 0 else 0
        avg_miss_interval = statistics.mean(miss_streaks) if miss_streaks else 0
        max_miss_streak = max(miss_streaks) if miss_streaks else current_miss
        avg_hit_rank = statistics.mean(hit_ranks) if hit_ranks else 0
        # 计算覆盖率 - 统计所有预测中出现过的不同号码
        all_predicted_numbers = set()
        for pred in predictions:
            all_predicted_numbers.update(pred['numbers'])
        coverage_rate = len(all_predicted_numbers) / 49  # 假设1-49号码
        
        # 计算风险指标
        sharpe_ratio = self._calculate_sharpe_ratio(details)
        max_drawdown = self._calculate_max_drawdown(details)
        profit_factor = self._calculate_profit_factor(details)
        
        result = BacktestResult(
            strategy_id=strategy_id,
            strategy_name=strategy_name,
            total_periods=total_periods,
            hit_count=hit_count,
            hit_rate=hit_rate,
            avg_miss_interval=avg_miss_interval,
            max_miss_streak=max_miss_streak,
            current_miss_streak=current_miss,
            avg_hit_rank=avg_hit_rank,
            coverage_rate=coverage_rate,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            profit_factor=profit_factor,
            details=details
        )
        
        self.results[strategy_id] = result
        return result
    
    def _calculate_sharpe_ratio(self, details: List[Dict[str, Any]]) -> float:
        """计算夏普比率"""
        returns = []
        for detail in details:
            # 简化收益计算：命中=1，未命中=-0.1
            if detail['is_hit']:
                returns.append(1.0)
            else:
                returns.append(-0.1)
        
        if len(returns) < 2:
            return 0.0
        
        avg_return = statistics.mean(returns)
        std_return = statistics.stdev(returns)
        
        return avg_return / std_return if std_return > 0 else 0.0
    
    def _calculate_max_drawdown(self, details: List[Dict[str, Any]]) -> float:
        """计算最大回撤"""
        cumulative_returns = []
        cumulative = 0
        
        for detail in details:
            if detail['is_hit']:
                cumulative += 1
            else:
                cumulative -= 0.1
            cumulative_returns.append(cumulative)
        
        if not cumulative_returns:
            return 0.0
        
        peak = cumulative_returns[0]
        max_dd = 0.0
        
        for value in cumulative_returns:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak if peak > 0 else 0
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _calculate_profit_factor(self, details: List[Dict[str, Any]]) -> float:
        """计算盈利因子"""
        total_profit = sum(1.0 for d in details if d['is_hit'])
        total_loss = sum(0.1 for d in details if not d['is_hit'])
        
        return total_profit / total_loss if total_loss > 0 else float('inf')
    
    def compare_strategies(self, strategy_ids: List[str]) -> Dict[str, Any]:
        """比较多个策略的表现"""
        comparison = {
            'strategies': [],
            'best_hit_rate': None,
            'best_sharpe_ratio': None,
            'best_profit_factor': None,
            'summary': {}
        }
        
        best_hit_rate = 0
        best_sharpe = float('-inf')
        best_profit = 0
        
        for strategy_id in strategy_ids:
            if strategy_id not in self.results:
                continue
            
            result = self.results[strategy_id]
            strategy_summary = {
                'id': result.strategy_id,
                'name': result.strategy_name,
                'hit_rate': result.hit_rate,
                'hit_count': result.hit_count,
                'total_periods': result.total_periods,
                'avg_miss_interval': result.avg_miss_interval,
                'max_miss_streak': result.max_miss_streak,
                'avg_hit_rank': result.avg_hit_rank,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'profit_factor': result.profit_factor
            }
            comparison['strategies'].append(strategy_summary)
            
            # 更新最佳指标
            if result.hit_rate > best_hit_rate:
                best_hit_rate = result.hit_rate
                comparison['best_hit_rate'] = strategy_id
            
            if result.sharpe_ratio > best_sharpe:
                best_sharpe = result.sharpe_ratio
                comparison['best_sharpe_ratio'] = strategy_id
            
            if result.profit_factor > best_profit:
                best_profit = result.profit_factor
                comparison['best_profit_factor'] = strategy_id
        
        # 生成摘要
        if comparison['strategies']:
            hit_rates = [s['hit_rate'] for s in comparison['strategies']]
            comparison['summary'] = {
                'total_strategies': len(comparison['strategies']),
                'avg_hit_rate': statistics.mean(hit_rates),
                'best_hit_rate_value': best_hit_rate,
                'hit_rate_std': statistics.stdev(hit_rates) if len(hit_rates) > 1 else 0
            }
        
        return comparison
    
    def export_results(self, output_file: str, format: str = 'json'):
        """导出回测结果"""
        if format.lower() == 'json':
            self._export_json(output_file)
        elif format.lower() == 'csv':
            self._export_csv(output_file)
        else:
            raise ValueError(f"不支持的格式: {format}")
    
    def _export_json(self, output_file: str):
        """导出JSON格式"""
        export_data = {}
        for strategy_id, result in self.results.items():
            export_data[strategy_id] = {
                'strategy_name': result.strategy_name,
                'total_periods': result.total_periods,
                'hit_count': result.hit_count,
                'hit_rate': result.hit_rate,
                'avg_miss_interval': result.avg_miss_interval,
                'max_miss_streak': result.max_miss_streak,
                'current_miss_streak': result.current_miss_streak,
                'avg_hit_rank': result.avg_hit_rank,
                'coverage_rate': result.coverage_rate,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'profit_factor': result.profit_factor,
                'details': result.details
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"回测结果已导出到: {output_file}")
    
    def _export_csv(self, output_file: str):
        """导出CSV格式"""
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = [
                'strategy_id', 'strategy_name', 'total_periods', 'hit_count',
                'hit_rate', 'avg_miss_interval', 'max_miss_streak', 'current_miss_streak',
                'avg_hit_rank', 'coverage_rate', 'sharpe_ratio', 'max_drawdown', 'profit_factor'
            ]
            writer.writerow(headers)
            
            # 写入数据
            for result in self.results.values():
                row = [
                    result.strategy_id, result.strategy_name, result.total_periods,
                    result.hit_count, result.hit_rate, result.avg_miss_interval,
                    result.max_miss_streak, result.current_miss_streak, result.avg_hit_rank,
                    result.coverage_rate, result.sharpe_ratio, result.max_drawdown, result.profit_factor
                ]
                writer.writerow(row)
        
        print(f"回测结果已导出到: {output_file}")

if __name__ == "__main__":
    # 测试回测引擎
    # 模拟历史数据
    test_history = [
        {'period': '2025001', 'special_code': 25},
        {'period': '2025002', 'special_code': 13},
        {'period': '2025003', 'special_code': 8},
        {'period': '2025004', 'special_code': 35},
        {'period': '2025005', 'special_code': 42}
    ]
    
    # 模拟预测数据
    test_predictions = [
        {'period': '2025001', 'numbers': [25, 13, 8, 35, 42, 1, 2, 3, 4, 5]},
        {'period': '2025002', 'numbers': [13, 25, 8, 35, 42, 6, 7, 9, 10, 11]},
        {'period': '2025003', 'numbers': [8, 25, 13, 35, 42, 12, 14, 15, 16, 17]},
        {'period': '2025004', 'numbers': [35, 25, 13, 8, 42, 18, 19, 20, 21, 22]},
        {'period': '2025005', 'numbers': [42, 25, 13, 8, 35, 23, 24, 26, 27, 28]}
    ]
    
    # 创建回测引擎
    backtest = BacktestEngine(test_history)
    
    # 运行回测
    result = backtest.run_strategy_backtest(
        'test_strategy', 
        '测试策略', 
        test_predictions
    )
    
    print("=== 回测结果 ===")
    print(f"策略名称: {result.strategy_name}")
    print(f"总期数: {result.total_periods}")
    print(f"命中次数: {result.hit_count}")
    print(f"命中率: {result.hit_rate:.2%}")
    print(f"平均遗漏间隔: {result.avg_miss_interval:.2f}")
    print(f"最大遗漏: {result.max_miss_streak}")
    print(f"平均命中排名: {result.avg_hit_rank:.2f}")
    print(f"夏普比率: {result.sharpe_ratio:.4f}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"盈利因子: {result.profit_factor:.2f}")
    
    # 导出结果
    backtest.export_results('backtest_results.json', 'json')
