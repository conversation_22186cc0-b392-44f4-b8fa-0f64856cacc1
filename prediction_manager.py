from typing import Dict, List, Optional
import json
import os
from datetime import datetime
import sqlite3
import pandas as pd
from context_tracker import ContextTracker

class PredictionManager:
    """预测结果管理器"""
    def __init__(self, db_path: str = 'lottery_data.db'):
        self.db_path = db_path
        self._init_database()
        self._init_directories()
        
        # 初始化上下文跟踪器
        self.context_tracker = ContextTracker(db_path)
        
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS model_results (
                    prediction_id TEXT PRIMARY KEY,
                    model_name TEXT,
                    prediction_date TEXT,
                    numbers TEXT,
                    confidence REAL,
                    model_params TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fusion_output (
                    prediction_id TEXT PRIMARY KEY,
                    prediction_date TEXT,
                    numbers TEXT,
                    strategy_ids TEXT,
                    fusion_weight TEXT,
                    final_score REAL
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS backtest_records (
                    prediction_id TEXT,
                    actual_draw TEXT,
                    hit_count INTEGER,
                    hit_numbers TEXT,
                    hit_rate REAL
                )
            ''')
            
    def _init_directories(self):
        """初始化输出目录"""
        dirs = ['outputs', 'reports', 'backtest']
        for d in dirs:
            os.makedirs(d, exist_ok=True)
            
    def save_model_prediction(self, 
                            model_name: str,
                            numbers: List[int],
                            confidence: float,
                            model_params: Dict) -> str:
        """保存单个模型的预测结果"""
        prediction_id = f"{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                'INSERT INTO model_results VALUES (?, ?, ?, ?, ?, ?)',
                (
                    prediction_id,
                    model_name,
                    datetime.now().isoformat(),
                    ','.join(map(str, numbers)),
                    confidence,
                    json.dumps(model_params)
                )
            )
            
        # 同时保存到文本文件
        output_file = f"outputs/model_{model_name}_result.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型名称: {model_name}\n")
            f.write(f"预测号码: {numbers}\n")
            f.write(f"置信度: {confidence:.2f}\n")
            
        return prediction_id
        
    def save_fusion_result(self,
                          numbers: List[int],
                          strategy_ids: List[str],
                          fusion_weights: Dict[str, float]) -> str:
        """保存融合后的最终预测结果"""
        prediction_id = f"fusion_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                'INSERT INTO fusion_output VALUES (?, ?, ?, ?, ?, ?)',
                (
                    prediction_id,
                    datetime.now().isoformat(),
                    ','.join(map(str, numbers)),
                    ','.join(strategy_ids),
                    json.dumps(fusion_weights),
                    sum(fusion_weights.values())
                )
            )
            
        # 保存到文本文件
        output_file = f"outputs/fusion_result.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"融合推荐号码: {numbers}\n")
            f.write(f"策略IDs: {strategy_ids}\n")
            f.write(f"融合权重: {fusion_weights}\n")
            
        return prediction_id
        
    def get_latest_predictions(self, limit: int = 10) -> List[Dict]:
        """获取最近的预测结果"""
        with sqlite3.connect(self.db_path) as conn:
            df = pd.read_sql('''
                SELECT 
                    f.prediction_id,
                    f.prediction_date,
                    f.numbers as fusion_numbers,
                    f.strategy_ids,
                    b.hit_count,
                    b.hit_rate
                FROM fusion_output f
                LEFT JOIN backtest_records b
                ON f.prediction_id = b.prediction_id
                ORDER BY f.prediction_date DESC
                LIMIT ?
            ''', conn, params=[limit])
            
        return df.to_dict('records')
        
    def get_prediction_details(self, prediction_id: str) -> Dict:
        """获取特定预测的详细信息"""
        with sqlite3.connect(self.db_path) as conn:
            # 获取融合结果
            fusion = pd.read_sql(
                'SELECT * FROM fusion_output WHERE prediction_id = ?',
                conn,
                params=[prediction_id]
            ).to_dict('records')[0]
            
            # 获取相关模型结果
            models = pd.read_sql('''
                SELECT model_name, numbers, confidence
                FROM model_results 
                WHERE prediction_id LIKE ?
                AND prediction_date >= ?
            ''', conn, params=[
                f"%{prediction_id.split('_')[1]}%",
                fusion['prediction_date']
            ]).to_dict('records')
            
            # 获取回测记录
            backtest = pd.read_sql(
                'SELECT * FROM backtest_records WHERE prediction_id = ?',
                conn,
                params=[prediction_id]
            ).to_dict('records')
            
        return {
            'fusion_result': fusion,
            'model_predictions': models,
            'backtest_result': backtest[0] if backtest else None
        }
