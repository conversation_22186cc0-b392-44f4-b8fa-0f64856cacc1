# TailGroupFilter

## Overview
The `TailGroupFilter` module generates and integrates 210 tail number combinations for a lottery prediction system. It provides functionality to filter candidate numbers based on defined tail groups.

## Features
- Generates tail number combinations.
- Matches numbers against defined tail groups.
- Configurable via JSON for easy adjustments.

## Installation
To install the module, use npm:

```
npm install tail-group-filter
```

## Usage
To use the `TailGroupFilter`, import it in your project:

```typescript
import { TailGroupFilter } from 'tail-group-filter';

// Example usage
const filter = new TailGroupFilter();
const results = filter.filter(candidateNumbers);
```

## Configuration
The tail groups can be configured in the `src/config/filter-config.json` file. Each tail group should have a label and an array of tails.

## Running Tests
To run the unit tests for the `TailGroupFilter`, use the following command:

```
npm test
```

## License
This project is licensed under the MIT License.