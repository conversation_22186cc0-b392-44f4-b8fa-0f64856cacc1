#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试高级分析GUI显示问题
"""

from prediction_engine import PredictionEngine
import time

def debug_advanced_analysis():
    """调试高级分析"""
    print("🔍 调试高级分析GUI显示问题")
    print("="*60)
    
    # 初始化预测引擎
    print("📊 初始化预测引擎...")
    engine = PredictionEngine()
    
    # 测试高级分析功能
    print("\n🧠 测试高级分析功能...")
    start_time = time.time()
    
    try:
        analysis_result = engine.get_advanced_zodiac_analysis()
        analysis_time = time.time() - start_time
        
        print(f"✅ 高级分析完成, 用时: {analysis_time:.3f} 秒")
        
        # 检查返回结果
        if 'error' in analysis_result:
            print(f"❌ 分析失败: {analysis_result['error']}")
            return
        
        # 显示详细结果
        print(f"\n📊 分析结果详情:")
        
        # 系统状态
        status = analysis_result.get('system_status', {})
        print(f"   系统状态:")
        print(f"     总小组数: {status.get('total_groups', 0):,}")
        print(f"     活跃候选: {status.get('active_candidates', 0)}")
        print(f"     平均Z-Score: {status.get('avg_z_score', 0):.3f}")
        print(f"     最高Z-Score: {status.get('max_z_score', 0):.3f}")
        print(f"     最后处理期号: {status.get('last_processed', 'N/A')}")
        print(f"     缓存可用: {status.get('cache_available', False)}")
        
        # 候选小组
        candidates = analysis_result.get('candidates', [])
        print(f"\n   候选小组:")
        print(f"     候选数量: {len(candidates)}")
        
        if candidates:
            print(f"     前5个候选:")
            for i, candidate in enumerate(candidates[:5], 1):
                members_str = ", ".join(candidate.get('members', []))
                print(f"       {i}. {members_str} - Z-Score: {candidate.get('z_score', 0):.3f}")
        else:
            print(f"     ⚠️ 无候选小组 - 这可能是问题所在!")
        
        # 推荐号码
        recommendations = analysis_result.get('top_recommendations', [])
        print(f"\n   推荐号码:")
        print(f"     推荐数量: {len(recommendations)}")
        if recommendations:
            numbers_str = ', '.join(f"{n:02d}" for n in recommendations)
            print(f"     号码: {numbers_str}")
        else:
            print(f"     ⚠️ 无推荐号码")
        
        # 能量分析
        energy = analysis_result.get('energy_analysis', {})
        print(f"\n   能量分析:")
        print(f"     生肖数量: {len(energy)}")
        if energy:
            sorted_energy = sorted(energy.items(), key=lambda x: x[1], reverse=True)
            print(f"     前5个高能量生肖:")
            for zodiac, pressure in sorted_energy[:5]:
                print(f"       {zodiac}: {pressure:.1f}")
        else:
            print(f"     ⚠️ 无能量分析数据")
        
        # 检查数据完整性
        print(f"\n🔍 数据完整性检查:")
        
        # 检查高级引擎状态
        advanced_engine = engine.advanced_zodiac_engine
        print(f"   高级引擎类型: {type(advanced_engine).__name__}")
        print(f"   小组总数: {len(advanced_engine.all_groups):,}")
        print(f"   Z-Score阈值: {advanced_engine.z_score_threshold}")
        print(f"   最后处理期号: {advanced_engine.last_processed_period}")
        
        # 检查是否有Z-Score > 0的小组
        groups_with_zscore = 0
        max_zscore = 0
        groups_with_history = 0
        
        for group in advanced_engine.all_groups.values():
            if hasattr(group, 'z_score') and group.z_score > 0:
                groups_with_zscore += 1
                max_zscore = max(max_zscore, group.z_score)
            
            if hasattr(group, 'miss_history') and len(group.miss_history) > 0:
                groups_with_history += 1
        
        print(f"   有Z-Score的小组: {groups_with_zscore}")
        print(f"   最大Z-Score: {max_zscore:.3f}")
        print(f"   有历史数据的小组: {groups_with_history}")
        
        # 如果没有候选，分析原因
        if len(candidates) == 0:
            print(f"\n❓ 无候选小组的可能原因:")
            print(f"   1. Z-Score阈值过高 (当前: {advanced_engine.z_score_threshold})")
            print(f"   2. 历史数据不足 (需要至少 {advanced_engine.min_history_length} 期)")
            print(f"   3. 统计计算问题")
            
            # 尝试降低阈值
            print(f"\n🔧 尝试降低Z-Score阈值...")
            low_threshold_candidates = advanced_engine.find_candidates_fast(0.5)
            print(f"   阈值0.5时的候选数: {len(low_threshold_candidates)}")
            
            if low_threshold_candidates:
                print(f"   前3个低阈值候选:")
                for i, candidate in enumerate(low_threshold_candidates[:3], 1):
                    members_str = ", ".join(candidate.get('members', []))
                    print(f"     {i}. {members_str} - Z-Score: {candidate.get('z_score', 0):.3f}")
        
        return analysis_result
        
    except Exception as e:
        print(f"❌ 高级分析异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_gui_integration():
    """测试GUI集成"""
    print(f"\n🖥️ 测试GUI集成...")
    
    try:
        # 检查GUI相关的方法
        from gui_main import LotteryPredictionGUI
        
        print("✅ GUI模块导入成功")
        
        # 检查高级分析相关方法是否存在
        gui_methods = [
            'create_advanced_analysis_tab',
            'refresh_advanced_analysis', 
            'update_candidates_display',
            'update_advanced_status',
            'update_advanced_numbers',
            'update_energy_analysis'
        ]
        
        for method in gui_methods:
            if hasattr(LotteryPredictionGUI, method):
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")

if __name__ == "__main__":
    # 运行调试
    result = debug_advanced_analysis()
    
    # 测试GUI集成
    test_gui_integration()
    
    print(f"\n🎯 调试完成!")
