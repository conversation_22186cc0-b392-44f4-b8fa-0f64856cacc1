@echo off
title Enhanced Lottery Prediction System
color 0A

echo.
echo ================================================
echo    Enhanced Lottery Prediction System v3.0
echo    With Three-Layer Filter System
echo ================================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check Python environment
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found!
    echo.
    echo Please install Python 3.7+ and add to PATH
    echo Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: Check required files
echo.
echo [INFO] Checking required files...

if not exist "enhanced_gui.py" (
    echo [ERROR] Missing file: enhanced_gui.py
    goto :error
)

if not exist "three_layer_filter_engine.py" (
    echo [ERROR] Missing file: three_layer_filter_engine.py
    goto :error
)

if not exist "tail_group_filter.py" (
    echo [ERROR] Missing file: tail_group_filter.py
    goto :error
)

if not exist "prediction_engine_adapter.py" (
    echo [ERROR] Missing file: prediction_engine_adapter.py
    goto :error
)

echo [OK] All required files found

:: Check Python dependencies
echo.
echo [INFO] Checking Python dependencies...

python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] tkinter module not available
    echo Please reinstall Python with tkinter support
    pause
    exit /b 1
)

:: Start enhanced GUI
echo.
echo [INFO] Starting enhanced GUI...
echo.
echo ================================================
echo  Enhanced Features:
echo  - Three-Layer Filter System
echo  - Real-time Statistics
echo  - Advanced Analysis
echo ================================================
echo.

python enhanced_gui.py

if %errorlevel% neq 0 (
    goto :error
)

goto :end

:error
echo.
echo [ERROR] Program failed to start
echo Please check the error messages above
pause
exit /b 1

:end
echo.
echo [INFO] Program completed
pause
exit /b 0
