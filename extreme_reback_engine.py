#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极限4肖组合回补预测引擎
实现《极限4肖组合回补预测策略白皮书》V2.1
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from optimized_zodiac_engine import OptimizedZodiacEngine
from prediction_engine import PredictionEngine

@dataclass
class RebackPattern:
    """回补模式数据结构"""
    pattern_type: str  # 快速反弹型、缓慢回补型、爆发型、伪极限型
    trigger_miss: int  # 触顶前遗漏
    reback_window: int  # 回补窗口期数
    hit_frequency: int  # 窗口内命中次数
    hit_intervals: List[int]  # 命中间隔
    success_rate: float  # 回补成功率
    avg_reback_period: float  # 平均回补期数

@dataclass
class RebackCandidate:
    """回补候选组合"""
    group_id: str
    members: List[str]
    current_miss: int
    max_miss_history: int
    critical_ratio: float  # 当前遗漏/历史极限比例
    reback_score: float  # 回补评分
    predicted_pattern: str  # 预测回补模式
    recommendation_level: int  # 推荐等级 (1-5星)
    tracking_periods: int  # 建议跟踪期数
    risk_level: str  # 风险等级
    historical_patterns: List[RebackPattern]

class ExtremeRebackEngine:
    """极限4肖组合回补预测引擎"""
    
    def __init__(self):
        self.zodiac_engine = OptimizedZodiacEngine()
        self.prediction_engine = PredictionEngine()
        self.zodiac_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        
        # 回补预测参数
        self.critical_threshold = 0.70  # 临界回补期阈值 (70%历史极限)
        self.extreme_threshold = 0.85   # 极限警戒阈值 (85%历史极限)
        self.super_extreme_threshold = 1.0  # 超极限阈值 (100%历史极限)
        self.reback_window = 10  # 回补窗口期数
        self.min_history_for_pattern = 3  # 最少历史触顶次数
        
        # 评分权重
        self.score_weights = {
            'critical_ratio': 0.25,      # 当前遗漏/极限值比例
            'historical_reback_rate': 0.30,  # 历史回补频率
            'consecutive_hit_bonus': 0.15,   # 连续命中模式
            'seasonal_factor': 0.20,     # 季节性因素
            'overlap_penalty': 0.10      # 组合重合度
        }
    
    def identify_critical_groups(self) -> List[Dict[str, Any]]:
        """识别临界回补期组合"""
        critical_groups = []
        all_ratios = []  # 用于调试

        for group_id, group in self.zodiac_engine.all_groups.items():
            # 只分析4肖组合
            if len(group.members) != 4 or group.max_miss == 0:
                continue

            critical_ratio = group.current_miss / group.max_miss
            all_ratios.append(critical_ratio)

            # 筛选临界或极限组合
            if critical_ratio >= self.critical_threshold:
                urgency_level = self._get_urgency_level(critical_ratio)

                critical_groups.append({
                    'group_id': group_id,
                    'members': group.members,
                    'current_miss': group.current_miss,
                    'max_miss_history': group.max_miss,
                    'critical_ratio': critical_ratio,
                    'urgency_level': urgency_level,
                    'hit_count': group.hit_count,
                    'miss_history': group.miss_history
                })

        # 调试信息
        if all_ratios:
            max_ratio = max(all_ratios)
            avg_ratio = np.mean(all_ratios)
            ratios_above_50 = len([r for r in all_ratios if r >= 0.5])
            ratios_above_70 = len([r for r in all_ratios if r >= 0.7])
            print(f"📊 遗漏比例分析: 最高{max_ratio:.2f}, 平均{avg_ratio:.2f}")
            print(f"   ≥50%: {ratios_above_50}个, ≥70%: {ratios_above_70}个")

        # 按临界比例排序
        critical_groups.sort(key=lambda x: x['critical_ratio'], reverse=True)

        return critical_groups
    
    def analyze_reback_patterns(self, group_data: Dict[str, Any]) -> List[RebackPattern]:
        """分析历史回补模式"""
        miss_history = group_data['miss_history']
        max_miss = group_data['max_miss_history']
        
        if len(miss_history) < self.min_history_for_pattern:
            return []
        
        patterns = []
        
        # 查找历史触顶事件
        extreme_events = []
        for i, miss_period in enumerate(miss_history):
            if miss_period >= max_miss * 0.9:  # 接近极限的事件
                extreme_events.append({
                    'index': i,
                    'miss_period': miss_period,
                    'is_peak': miss_period >= max_miss * 0.95
                })
        
        # 分析每次触顶后的回补行为
        for event in extreme_events:
            if event['is_peak']:
                reback_pattern = self._analyze_single_reback(
                    miss_history, event['index'], max_miss
                )
                if reback_pattern:
                    patterns.append(reback_pattern)
        
        return patterns
    
    def _analyze_single_reback(self, miss_history: List[int], peak_index: int, max_miss: int) -> Optional[RebackPattern]:
        """分析单次回补事件"""
        if peak_index >= len(miss_history) - 1:
            return None

        trigger_miss = miss_history[peak_index]

        # 分析回补窗口内的表现
        window_end = min(peak_index + self.reback_window, len(miss_history))
        reback_window_data = miss_history[peak_index + 1:window_end]

        if not reback_window_data:
            return None

        # 计算回补指标 - 修复逻辑
        # miss_history中存储的是每次命中前的遗漏期数
        # 所以我们需要分析的是后续的遗漏模式
        hit_count = 0
        hit_positions = []

        # 重新分析：查看后续期数中的遗漏模式
        for i, miss_period in enumerate(reback_window_data):
            if miss_period <= 3:  # 短期内命中（遗漏≤3期认为是快速回补）
                hit_count += 1
                hit_positions.append(i)

        window_size = len(reback_window_data)

        # 计算命中间隔
        hit_intervals = []
        for i in range(1, len(hit_positions)):
            hit_intervals.append(hit_positions[i] - hit_positions[i-1])

        # 确定回补模式类型
        pattern_type = self._classify_reback_pattern(hit_count, window_size, hit_intervals, reback_window_data)

        # 计算成功率和平均回补期数
        success_rate = hit_count / window_size if window_size > 0 else 0

        # 计算平均回补期数：第一次快速命中的位置
        if hit_positions:
            avg_reback_period = hit_positions[0] + 1  # 第一次回补的期数
        else:
            avg_reback_period = window_size

        return RebackPattern(
            pattern_type=pattern_type,
            trigger_miss=trigger_miss,
            reback_window=window_size,
            hit_frequency=hit_count,
            hit_intervals=hit_intervals,
            success_rate=success_rate,
            avg_reback_period=avg_reback_period
        )
    
    def _classify_reback_pattern(self, hit_count: int, window_size: int, hit_intervals: List[int], reback_data: List[int]) -> str:
        """分类回补模式"""
        if hit_count == 0:
            return "伪极限型"

        hit_rate = hit_count / window_size

        # 检查前3期是否有快速回补
        early_reback = any(miss <= 2 for miss in reback_data[:3]) if len(reback_data) >= 3 else False

        # 快速反弹型：前3期内快速回补
        if early_reback and hit_rate >= 0.2:
            return "快速反弹型"

        # 爆发型：多次快速回补
        if hit_count >= 3 and hit_rate >= 0.4:
            return "爆发型"

        # 缓慢回补型：有回补但较慢
        if hit_count >= 1 and hit_rate >= 0.1:
            return "缓慢回补型"

        return "伪极限型"
    
    def calculate_reback_score(self, group_data: Dict[str, Any], patterns: List[RebackPattern]) -> float:
        """计算回补评分 (多因子模型)"""
        score = 0.0
        
        # 1. 当前遗漏/极限值比例 (25%)
        critical_ratio = group_data['critical_ratio']
        ratio_score = min(critical_ratio, 1.2) / 1.2  # 超过120%封顶
        score += ratio_score * self.score_weights['critical_ratio']
        
        # 2. 历史回补频率 (30%)
        if patterns:
            avg_success_rate = np.mean([p.success_rate for p in patterns])
            reback_score = avg_success_rate
        else:
            reback_score = 0.1  # 无历史数据给予基础分
        score += reback_score * self.score_weights['historical_reback_rate']
        
        # 3. 连续命中模式奖励 (15%)
        consecutive_bonus = 0.0
        for pattern in patterns:
            if pattern.pattern_type in ["快速反弹型", "爆发型"]:
                consecutive_bonus = max(consecutive_bonus, 0.8)
            elif pattern.pattern_type == "缓慢回补型":
                consecutive_bonus = max(consecutive_bonus, 0.4)
        score += consecutive_bonus * self.score_weights['consecutive_hit_bonus']
        
        # 4. 季节性因素 (20%) - 简化实现
        seasonal_factor = self._get_seasonal_factor()
        score += seasonal_factor * self.score_weights['seasonal_factor']
        
        # 5. 组合重合度惩罚 (10%) - 暂时简化
        overlap_penalty = 0.5  # 中等重合度
        score += overlap_penalty * self.score_weights['overlap_penalty']
        
        return min(score, 1.0)  # 评分封顶1.0
    
    def _get_seasonal_factor(self) -> float:
        """获取季节性因子 (简化实现)"""
        current_month = datetime.now().month
        
        # 简单的季节性权重 (可根据历史数据优化)
        seasonal_weights = {
            1: 0.8, 2: 0.9, 3: 0.7,   # 春季
            4: 0.6, 5: 0.5, 6: 0.6,   # 夏季  
            7: 0.7, 8: 0.8, 9: 0.9,   # 秋季
            10: 0.8, 11: 0.7, 12: 0.9  # 冬季
        }
        
        return seasonal_weights.get(current_month, 0.6)
    
    def _get_urgency_level(self, critical_ratio: float) -> str:
        """获取紧迫等级"""
        if critical_ratio >= self.super_extreme_threshold:
            return "超极限"
        elif critical_ratio >= self.extreme_threshold:
            return "极限警戒"
        elif critical_ratio >= self.critical_threshold:
            return "临界回补"
        else:
            return "正常"
    
    def predict_reback_candidates(self, limit: int = 20) -> List[RebackCandidate]:
        """预测回补候选组合"""
        critical_groups = self.identify_critical_groups()
        candidates = []
        processed_groups = set()  # 防止重复处理

        for group_data in critical_groups[:limit * 3]:  # 多取一些用于筛选
            # 防止重复处理相同的小组
            group_key = tuple(sorted(group_data['members']))
            if group_key in processed_groups:
                continue
            processed_groups.add(group_key)
            # 分析历史回补模式
            patterns = self.analyze_reback_patterns(group_data)
            
            # 计算回补评分
            reback_score = self.calculate_reback_score(group_data, patterns)
            
            # 预测回补模式
            predicted_pattern = self._predict_pattern(patterns, group_data['critical_ratio'])
            
            # 确定推荐等级
            recommendation_level = self._get_recommendation_level(reback_score, group_data['critical_ratio'])
            
            # 计算建议跟踪期数
            tracking_periods = self._calculate_tracking_periods(patterns, predicted_pattern)
            
            # 评估风险等级
            risk_level = self._assess_risk_level(patterns, group_data['critical_ratio'])
            
            candidate = RebackCandidate(
                group_id=group_data['group_id'],
                members=group_data['members'],
                current_miss=group_data['current_miss'],
                max_miss_history=group_data['max_miss_history'],
                critical_ratio=group_data['critical_ratio'],
                reback_score=reback_score,
                predicted_pattern=predicted_pattern,
                recommendation_level=recommendation_level,
                tracking_periods=tracking_periods,
                risk_level=risk_level,
                historical_patterns=patterns
            )
            
            candidates.append(candidate)
        
        # 按回补评分排序
        candidates.sort(key=lambda x: x.reback_score, reverse=True)
        
        return candidates[:limit]
    
    def _predict_pattern(self, patterns: List[RebackPattern], critical_ratio: float) -> str:
        """预测回补模式"""
        if not patterns:
            if critical_ratio >= 1.0:
                return "未知模式(超极限)"
            else:
                return "未知模式(首次临界)"
        
        # 统计历史模式
        pattern_counts = {}
        for pattern in patterns:
            pattern_counts[pattern.pattern_type] = pattern_counts.get(pattern.pattern_type, 0) + 1
        
        # 返回最常见的模式
        most_common_pattern = max(pattern_counts.items(), key=lambda x: x[1])[0]
        
        # 根据当前状态调整预测
        if critical_ratio >= 1.0 and most_common_pattern != "伪极限型":
            return f"{most_common_pattern}(超极限增强)"
        
        return most_common_pattern
    
    def _get_recommendation_level(self, reback_score: float, critical_ratio: float) -> int:
        """获取推荐等级 (1-5星)"""
        base_score = reback_score
        
        # 超极限奖励
        if critical_ratio >= 1.0:
            base_score += 0.2
        elif critical_ratio >= 0.95:
            base_score += 0.1
        
        if base_score >= 0.8:
            return 5  # ★★★★★
        elif base_score >= 0.65:
            return 4  # ★★★★☆
        elif base_score >= 0.5:
            return 3  # ★★★☆☆
        elif base_score >= 0.35:
            return 2  # ★★☆☆☆
        else:
            return 1  # ★☆☆☆☆
    
    def _calculate_tracking_periods(self, patterns: List[RebackPattern], predicted_pattern: str) -> int:
        """计算建议跟踪期数"""
        if not patterns:
            return 8  # 默认跟踪期数
        
        avg_reback_period = np.mean([p.avg_reback_period for p in patterns])
        
        # 根据预测模式调整
        if "快速反弹" in predicted_pattern:
            return max(3, int(avg_reback_period * 0.8))
        elif "爆发型" in predicted_pattern:
            return max(5, int(avg_reback_period * 1.2))
        elif "缓慢回补" in predicted_pattern:
            return max(8, int(avg_reback_period * 1.5))
        else:
            return max(6, int(avg_reback_period))
    
    def _assess_risk_level(self, patterns: List[RebackPattern], critical_ratio: float) -> str:
        """评估风险等级"""
        if not patterns:
            return "中等风险"
        
        # 计算历史成功率
        avg_success_rate = np.mean([p.success_rate for p in patterns])
        
        # 伪极限型比例
        pseudo_extreme_ratio = len([p for p in patterns if p.pattern_type == "伪极限型"]) / len(patterns)
        
        if avg_success_rate >= 0.6 and pseudo_extreme_ratio <= 0.2:
            return "低风险"
        elif avg_success_rate >= 0.3 and pseudo_extreme_ratio <= 0.5:
            return "中等风险"
        else:
            return "高风险"
    
    def generate_reback_report(self, candidates: List[RebackCandidate]) -> str:
        """生成回补预测报告"""
        if not candidates:
            return "📊 当前无极限回补候选组合"
        
        report = f"""
🔥 极限4肖组合回补预测报告
{'='*60}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析方法: 极限回补预测策略 V2.1
候选数量: {len(candidates)} 个
预测周期: 短期回补 (1-10期)

"""
        
        for i, candidate in enumerate(candidates[:10], 1):
            stars = "★" * candidate.recommendation_level + "☆" * (5 - candidate.recommendation_level)
            members_str = ", ".join(candidate.members)
            
            report += f"""
▶ 回补候选 #{i}: {members_str}
  🎯 推荐等级: {stars} ({candidate.recommendation_level}/5)
  📊 当前遗漏: {candidate.current_miss} 期 / 历史极限: {candidate.max_miss_history} 期
  📈 临界比例: {candidate.critical_ratio:.1%} ({candidate.critical_ratio >= 1.0 and '超极限' or '临界'})
  🔮 预测模式: {candidate.predicted_pattern}
  ⏱️ 建议跟踪: {candidate.tracking_periods} 期
  ⚠️ 风险等级: {candidate.risk_level}
  📊 回补评分: {candidate.reback_score:.3f}
  
  --- 历史回补模式 ---
"""
            
            if candidate.historical_patterns:
                for j, pattern in enumerate(candidate.historical_patterns, 1):
                    report += f"  {j}. {pattern.pattern_type}: 成功率{pattern.success_rate:.1%}, 平均{pattern.avg_reback_period:.1f}期回补\n"
            else:
                report += "  暂无历史回补数据 (首次触及极限)\n"
            
            report += "\n"
        
        # 添加策略说明
        report += f"""
📋 策略说明:
  🔥 超极限组合: 当前遗漏已超过历史最大值，回补概率极高
  ⚡ 极限警戒: 当前遗漏达到95%历史极限，密切关注
  💧 临界回补: 当前遗漏达到85%历史极限，开始关注
  
⚠️ 风险提示:
本预测基于历史回补模式分析，仅供参考。
极限回补理论具有统计学基础，但不保证必然发生。
建议结合其他策略综合判断，控制投注风险。
        """
        
        return report

if __name__ == "__main__":
    # 测试回补预测引擎
    print("🔥 测试极限4肖组合回补预测引擎")
    print("="*60)
    
    engine = ExtremeRebackEngine()
    
    # 识别临界组合
    print("🔍 识别临界回补期组合...")
    critical_groups = engine.identify_critical_groups()
    print(f"✅ 找到 {len(critical_groups)} 个临界组合")
    
    # 预测回补候选
    print("\n🎯 预测回补候选组合...")
    candidates = engine.predict_reback_candidates(10)
    print(f"✅ 生成 {len(candidates)} 个回补候选")
    
    # 生成报告
    print("\n📄 生成回补预测报告...")
    report = engine.generate_reback_report(candidates)
    print(report)
    
    print(f"\n🎯 回补预测引擎测试完成!")
