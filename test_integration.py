import unittest
import tempfile
import os
import json
import sqlite3
import time
from pathlib import Path
from tail_group_filter import TailGroupFilter
from config_manager import ConfigManager

class TestConfigIntegration(unittest.TestCase):
    """配置系统集成测试"""
    def setUp(self):
        # 创建临时文件
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_config = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
        self.temp_db.close()
        self.temp_config.close()

        # 设置初始配置
        initial_config = {
            "app_settings": {
                "theme": "light",
                "language": "zh_CN"
            },
            "analysis_settings": {
                "default_periods": 100,
                "hot_group_count": 10
            },
            "groups": [
                {
                    "label": "test_group_1",
                    "tails": [1, 3, 5],
                    "description": "Test group 1"
                }
            ]
        }

        with open(self.temp_config.name, 'w', encoding='utf-8') as f:
            json.dump(initial_config, f, indent=4)

        self.config_manager = ConfigManager(self.temp_config.name)
        self.filter = TailGroupFilter(
            db_path=self.temp_db.name,
            config_file=self.temp_config.name
        )

    def tearDown(self):
        if self.filter.conn:
            self.filter.conn.close()
        os.unlink(self.temp_db.name)
        os.unlink(self.temp_config.name)

    def test_config_update_propagation(self):
        """测试配置更新是否正确传播到过滤器"""
        # 更新配置
        self.config_manager.set('analysis_settings.hot_group_count', 5)

        # 验证过滤器是否收到更新
        hot_groups = self.filter.get_hot_groups()
        self.assertLessEqual(len(hot_groups), 5)

    def test_config_reload(self):
        """测试配置重载功能"""
        # 直接修改配置文件
        new_config = {
            "analysis_settings": {
                "default_periods": 50,
                "hot_group_count": 3
            }
        }
        json.dump(new_config, open(self.temp_config.name, 'w', encoding='utf-8'))

        # 重载配置
        self.filter.reload_config()

        # 验证新配置是否生效
        hot_groups = self.filter.get_hot_groups()
        self.assertLessEqual(len(hot_groups), 3)

class TestPerformance(unittest.TestCase):
    """性能测试"""
    @classmethod
    def setUpClass(cls):
        """创建大量测试数据"""
        cls.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        cls.temp_config = tempfile.NamedTemporaryFile(suffix='.json', delete=False)

        # 创建数据库和测试数据
        with sqlite3.connect(cls.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE lottery_results (
                    draw_date TEXT,
                    numbers TEXT,
                    special_number INTEGER
                )
            ''')

            # 生成1000期测试数据
            test_data = []
            base_date = time.strptime('2020-01-01', '%Y-%m-%d')
            for i in range(1000):
                date = time.strftime('%Y-%m-%d',
                       time.localtime(time.mktime(base_date) + i * 86400))
                numbers = ','.join(str(x) for x in range(1+i%6, 47, 8))
                special = i % 49 + 1
                test_data.append((date, numbers, special))

            cursor.executemany(
                'INSERT INTO lottery_results VALUES (?, ?, ?)',
                test_data
            )
            conn.commit()

    @classmethod
    def tearDownClass(cls):
        os.unlink(cls.temp_db.name)
        os.unlink(cls.temp_config.name)

    def setUp(self):
        self.filter = TailGroupFilter(
            db_path=self.temp_db.name,
            config_file=self.temp_config.name
        )

    def test_analysis_performance(self):
        """测试分析性能"""
        start_time = time.time()
        self.filter.analyze_history(days=1000)
        duration = time.time() - start_time
        self.assertLess(duration, 2.0)  # 分析1000期数据应在2秒内完成

    def test_hot_groups_performance(self):
        """测试热门组合计算性能"""
        start_time = time.time()
        self.filter.get_hot_groups(top_n=20)
        duration = time.time() - start_time
        self.assertLess(duration, 1.0)  # 获取热门组合应在1秒内完成

    def test_concurrent_queries(self):
        """测试并发查询性能"""
        import threading

        def query_func():
            for _ in range(100):
                self.filter.get_numbers_by_tail({1, 2, 3})

        threads = [threading.Thread(target=query_func) for _ in range(10)]
        start_time = time.time()

        for t in threads:
            t.start()
        for t in threads:
            t.join()

        duration = time.time() - start_time
        self.assertLess(duration, 5.0)  # 1000次并发查询应在5秒内完成

class TestDataIO(unittest.TestCase):
    """数据导入导出测试"""
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_config = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
        self.temp_csv = tempfile.NamedTemporaryFile(suffix='.csv', delete=False)

        # 创建配置文件
        test_config = {
            "groups": [
                {
                    "label": "测试组合1",
                    "tails": [1, 2, 3],
                    "description": "测试用组合1",
                    "type": "basic"
                }
            ]
        }
        with open(self.temp_config.name, 'w', encoding='utf-8') as f:
            json.dump(test_config, f)

        # 创建测试CSV文件
        with open(self.temp_csv.name, 'w', encoding='utf-8') as f:
            f.write('draw_date,numbers,special_number\n')
            f.write('2025-07-01,1;11;21;31;41;2,12\n')
            f.write('2025-07-02,3;13;23;33;43;4,14\n')

        self.filter = TailGroupFilter(
            db_path=self.temp_db.name,
            config_file=self.temp_config.name
        )

    def tearDown(self):
        if self.filter.conn:
            self.filter.conn.close()
        try:
            os.unlink(self.temp_db.name)
            os.unlink(self.temp_config.name)
            os.unlink(self.temp_csv.name)
        except (OSError, IOError):
            pass  # Ignore deletion errors

    def test_csv_import(self):
        """测试CSV数据导入"""
        imported_count = self.filter.import_from_csv(self.temp_csv.name)
        self.assertEqual(imported_count, 2)

        # 验证导入的数据
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM lottery_results')
            count = cursor.fetchone()[0]
            self.assertEqual(count, 2)

    def test_export_analysis(self):
        """测试分析结果导出"""
        export_file = tempfile.NamedTemporaryFile(suffix='.json', delete=False)

        try:
            # 生成并导出分析结果
            analysis = self.filter.analyze_history(days=30)
            self.filter.export_analysis(analysis, export_file.name)

            # 验证导出文件
            with open(export_file.name, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)

            self.assertEqual(exported_data['analysis_date'][:10],
                           time.strftime('%Y-%m-%d'))
            self.assertIn('groups', exported_data)

        finally:
            os.unlink(export_file.name)

    def test_backup_restore(self):
        """测试数据备份和恢复"""
        # 导入一些测试数据
        self.filter.import_from_csv(self.temp_csv.name)

        # 创建备份
        backup_file = self.filter.create_backup()
        self.assertTrue(os.path.exists(backup_file))

        try:
            # 清空数据库
            with sqlite3.connect(self.temp_db.name) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM lottery_results')

            # 从备份恢复
            self.filter.restore_from_backup(backup_file)

            # 验证数据是否恢复
            with sqlite3.connect(self.temp_db.name) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM lottery_results')
                count = cursor.fetchone()[0]
                self.assertEqual(count, 2)

        finally:
            if os.path.exists(backup_file):
                os.unlink(backup_file)

if __name__ == '__main__':
    unittest.main()
