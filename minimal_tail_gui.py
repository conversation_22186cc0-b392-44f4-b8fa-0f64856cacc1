#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
尾数组合筛选器 - 最小化GUI界面
功能：
1. 输入候选号码列表
2. 使用尾数组合进行筛选
3. 显示筛选结果
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from tail_group_filter import TailGroupFilter

class MinimalGUI:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("尾数筛选器")
        self.root.geometry("600x400")

        # 初始化筛选器
        self.filter = TailGroupFilter()

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        """创建界面组件"""
        # 输入框架
        input_frame = ttk.Frame(self.root, padding="5")
        input_frame.pack(fill=tk.X)

        ttk.Label(input_frame, text="输入号码(逗号分隔):").pack(side=tk.LEFT)
        self.numbers_entry = ttk.Entry(input_frame, width=40)
        self.numbers_entry.pack(side=tk.LEFT, padx=5)
        ttk.Button(input_frame, text="筛选", command=self.filter_numbers).pack(side=tk.LEFT)

        # 结果显示区
        result_frame = ttk.Frame(self.root, padding="5")
        result_frame.pack(fill=tk.BOTH, expand=True)

        self.result_text = scrolledtext.ScrolledText(result_frame)
        self.result_text.pack(fill=tk.BOTH, expand=True)

    def filter_numbers(self):
        """执行筛选"""
        # 获取输入
        numbers_str = self.numbers_entry.get().strip()
        if not numbers_str:
            self.show_message("请输入号码")
            return

        try:
            # 解析号码
            numbers = [int(n.strip()) for n in numbers_str.split(',')]
            numbers = [n for n in numbers if 1 <= n <= 48]  # 过滤有效范围

            if not numbers:
                self.show_message("没有有效号码(1-48)")
                return

            # 执行筛选
            results = self.filter.filter_candidates(numbers)

            # 显示结果
            self.show_results(results)

        except ValueError:
            self.show_message("号码格式错误,请使用逗号分隔的数字")
        except Exception as e:
            self.show_message(f"处理出错: {str(e)}")

    def show_results(self, results):
        """显示筛选结果"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "=== 筛选结果 ===\n\n")

        # 显示匹配结果
        for label, matched in results.items():
            if matched:  # 只显示有匹配的组合
                text = f"{label}:\n"
                text += f"匹配号码: {matched}\n"
                text += f"数量: {len(matched)}\n\n"
                self.result_text.insert(tk.END, text)

        # 如果没有任何匹配
        if not any(matched for matched in results.values()):
            self.result_text.insert(tk.END, "没有找到匹配的尾数组合\n")

    def show_message(self, message):
        """显示消息"""
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, message + "\n")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    gui = MinimalGUI()
    gui.run()
