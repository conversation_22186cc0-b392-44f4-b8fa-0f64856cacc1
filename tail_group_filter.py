from typing import List, Dict, Set, Any
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import csv
import os
import shutil

class PerformanceMonitor:
    def __init__(self):
        self.query_times = []
        self.operation_times = {}
        self.cache_hits = 0
        self.cache_misses = 0

    def record_query_time(self, query_time: float):
        self.query_times.append(query_time)

    def record_operation(self, operation: str, time_taken: float):
        if operation not in self.operation_times:
            self.operation_times[operation] = []
        self.operation_times[operation].append(time_taken)

    def record_cache_hit(self):
        self.cache_hits += 1

    def record_cache_miss(self):
        self.cache_misses += 1

    def get_stats(self) -> dict:
        return {
            "avg_query_time": sum(self.query_times) / len(self.query_times) if self.query_times else 0,
            "total_queries": len(self.query_times),
            "operation_stats": {
                op: {
                    "avg_time": sum(times) / len(times),
                    "total_calls": len(times)
                } for op, times in self.operation_times.items()
            },
            "cache_stats": {
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            }
        }

class TailGroupFilter:
    """
    Tail number combination filter engine.
    Generates and analyzes number combination patterns.
    """
    def __init__(self, db_path: str, config_file: str):
        """
        Initialize tail filter with performance monitoring
        Args:
            db_path: Database path
            config_file: Config file path
        """
        self.db_path = db_path
        self.config_file = config_file
        self.conn = sqlite3.connect(self.db_path)
        self._init_database()
        self.config = self._load_config()
        self._init_groups()
        self.cache = {}

        # Performance settings from config
        filter_settings = self.config.get("filter_settings", {})
        enable_monitoring = filter_settings.get("enable_performance_logging", False)
        self.cache_enabled = filter_settings.get("cache_results", False)
        self.performance_monitor = PerformanceMonitor() if enable_monitoring else None

        # Initialize performance tracking if enabled
        if self.performance_monitor:
            self.performance_monitor.record_operation('init', 0.0)

    def _init_database(self):
        """Initialize database tables"""
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_results (
                draw_date TEXT,
                numbers TEXT,
                special_number INTEGER
            )
        ''')
        self.conn.commit()

    def _load_config(self) -> dict:
        """Load configuration file"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def reload_config(self):
        """Reload configuration file"""
        self.config = self._load_config()
        self._init_groups()

    def _init_groups(self):
        """Initialize default tail number groups"""
        if 'groups' not in self.config:
            self.config['groups'] = [
                {
                    'label': '01369',
                    'tails': [0, 1, 3, 6, 9],
                    'description': 'Popular tail combination'
                },
                {
                    'label': '2478',
                    'tails': [2, 4, 7, 8],
                    'description': 'Alternate tail combination'
                },
                {
                    'label': '0369',
                    'tails': [0, 3, 6, 9],
                    'description': 'Three interval tail combination'
                },
                {
                    'label': '1478',
                    'tails': [1, 4, 7, 8],
                    'description': 'Mixed tail combination'
                }
            ]
            self._save_config(self.config)

    def _save_config(self, config: dict):
        """Save configuration to file"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)

    def save_to_database(self):
        """Save current state to database"""
        if self.conn is not None:
            self.conn.commit()

    def get_numbers_by_tail(self, tails: Set[int]) -> Set[int]:
        """Get numbers matching given tail digits"""
        numbers = set()
        for i in range(1, 50):
            if i % 10 in tails:
                numbers.add(i)
        return numbers

    def filter_candidates(self, candidates: List[int]) -> Dict[str, List[int]]:
        """Filter candidate numbers by tail combinations"""
        result = {}
        for group in self.config['groups']:
            group_numbers = []
            group_tails = set(group['tails'])
            for num in candidates:
                if num % 10 in group_tails:
                    group_numbers.append(num)
            result[group['label']] = group_numbers
        return result

    def analyze_history(self, days: int = 30) -> Dict[str, Dict]:
        """Analyze historical data"""
        if self.cache_enabled:
            cache_key = f"analyze_history_{days}"
            if cache_key in self.cache:
                if self.performance_monitor:
                    self.performance_monitor.record_cache_hit()
                return self.cache[cache_key]
            elif self.performance_monitor:
                self.performance_monitor.record_cache_miss()

        start_time = datetime.now()

        if self.conn is None:
            self.conn = sqlite3.connect(self.db_path)

        cursor = self.conn.cursor()
        date_limit = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        query_start = datetime.now()
        cursor.execute('''
            SELECT numbers FROM lottery_results
            WHERE draw_date >= ?
            ORDER BY draw_date DESC
        ''', (date_limit,))

        if self.performance_monitor:
            self.performance_monitor.record_query_time((datetime.now() - query_start).total_seconds())

        results = cursor.fetchall()

        stats = {}
        for group in self.config['groups']:
            hits = 0
            total_matches = 0
            group_tails = set(group['tails'])

            for row in results:
                numbers = [int(x) for x in row[0].split(',')]
                matched = sum(1 for n in numbers if n % 10 in group_tails)
                if matched > 0:
                    hits += 1
                total_matches += matched

            if results:
                hit_rate = hits / len(results)
                stability = total_matches / (len(results) * len(group_tails))
            else:
                hit_rate = stability = 0

            stats[group['label']] = {
                'hit_rate': hit_rate,
                'stability_score': stability
            }

        return stats

    def get_hot_groups(self, min_hit_rate: float = 0.3, days: int = 30) -> List[Dict]:
        """
        Analyze data and get hot combinations
        Args:
            min_hit_rate: Minimum hit rate for hot groups
            days: Days of history to analyze
        Returns:
            List of hot tail groups with their stats
        """
        stats = self.analyze_history(days=days)
        if not stats:
            return []

        hot_groups = []
        for label, group_info in stats.items():
            if group_info.get('hit_rate', 0) >= min_hit_rate:
                group_data = {
                    'label': label,
                    'hit_rate': group_info['hit_rate'],
                    'stability_score': group_info.get('stability_score', 0),
                    'prediction_confidence': group_info.get('hit_rate', 0) * group_info.get('stability_score', 0)
                }
                hot_groups.append(group_data)
        return hot_groups

    def validate_group(self, group: Dict) -> bool:
        """Validate group configuration"""
        if not all(field in group for field in ['label', 'tails']):
            raise ValueError("Missing required fields")

        if not all(isinstance(t, int) and 0 <= t <= 9 for t in group['tails']):
            raise ValueError("Tails must be integers between 0 and 9")

        return True  # If we get here, validation passed

    def close(self):
        """Close database connection and cleanup resources"""
        if self.conn is not None:
            try:
                if self.performance_monitor is not None:
                    self.performance_monitor.record_operation('close_connection', 0.0)
                self.conn.close()
            except Exception as e:
                if self.performance_monitor is not None:
                    self.performance_monitor.record_operation('close_error', 0.0)
            finally:
                self.conn = None

        self.cache.clear()  # Clear cache on close

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()

    def create_backup(self) -> str:
        """Create database backup
        Returns:
            str: Path to the backup file
        """
        # Create backup directory
        backup_dir = "backups"
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create backup path
        backup_path = os.path.join(backup_dir, f"lottery_data_{timestamp}.db")

        if self.conn is not None:
            self.conn.commit()  # Ensure all changes are saved
            with open(backup_path, 'wb') as backup_file:
                for line in self.conn.iterdump():
                    backup_file.write(f'{line}\n'.encode('utf-8'))

        # Save performance stats if enabled
        if self.performance_monitor is not None:
            stats_file = os.path.join(backup_dir, f"performance_stats_{timestamp}.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.performance_monitor.get_stats(), f, indent=2)

        return backup_path

    def restore_from_backup(self, backup_path: str):
        """Restore from backup file"""
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup file not found: {backup_path}")

        if self.conn is not None:
            self.conn.close()
            self.conn = None

        # Restore database
        shutil.copy2(backup_path, self.db_path)  # Preserve metadata
        self.conn = sqlite3.connect(self.db_path)

        return True

    def import_from_csv(self, csv_path: str) -> int:
        """Import data from CSV file"""
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV file not found: {csv_path}")

        imported_count = 0
        start_time = datetime.now()

        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    numbers = row['numbers'].replace(';', ',')

                    if self.conn is not None:
                        cursor = self.conn.cursor()
                        cursor.execute(
                            'INSERT INTO lottery_results VALUES (?, ?, ?)',
                            (row['draw_date'], numbers, int(row['special_number']))
                        )
                        imported_count += 1

            if self.conn is not None:
                self.conn.commit()

            if self.performance_monitor is not None:
                elapsed = (datetime.now() - start_time).total_seconds()
                self.performance_monitor.record_operation('import_csv', elapsed)

        except Exception as e:
            if self.conn is not None:
                self.conn.rollback()
            raise e

        return imported_count

    def export_analysis(self, analysis: Dict[str, Any], output_file: str):
        """Export analysis results"""
        start_time = datetime.now()

        export_data = {
            'analysis_date': datetime.now().isoformat(),
            'groups': analysis
        }

        if self.performance_monitor is not None:
            export_data['performance'] = self.performance_monitor.get_stats()

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        if self.performance_monitor is not None:
            elapsed = (datetime.now() - start_time).total_seconds()
            self.performance_monitor.record_operation('export_analysis', elapsed)

        self.conn.commit()  # Ensure all changes are saved
        shutil.copy2(self.db_path, backup_path)
        return backup_path

    def restore_from_backup(self, backup_path: str):
        """Restore database from backup
        Args:
            backup_path: Path to the backup file to restore from
        """
        self.conn.close()  # Close current connection
        shutil.copy2(backup_path, self.db_path)
        self.conn = sqlite3.connect(self.db_path)  # Reconnect
        backup_dir = Path(self.db_path).parent / 'backups'
        backup_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = backup_dir / f'lottery_data_{timestamp}.db'

        shutil.copy2(self.db_path, backup_path)
        return str(backup_path)

    def restore_from_backup(self, backup_path: str):
        """Restore from backup"""
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup file not found: {backup_path}")

        shutil.copy2(backup_path, self.db_path)

    def __del__(self):
        """Ensure all file handles are properly closed"""
        if hasattr(self, '_config_file') and not self._config_file.closed:
            self._config_file.close()

    def __enter__(self):
        """Support context manager protocol"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Support context manager protocol"""
        self.__del__()
