@echo off
title Real Prediction GUI Launcher
color 0A

echo.
echo ================================================
echo    Real Prediction System GUI v1.0
echo    Using REAL prediction engine (not hardcoded)
echo ================================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check Python environment
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] 'python' command not available, trying 'py'...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Python not found!
        echo.
        echo Please install Python 3.7+ and add to PATH
        echo Download: https://www.python.org/downloads/
        echo.
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo [OK] Python environment found
%PYTHON_CMD% --version

:: Check core files
echo.
echo [INFO] Checking core files...

if not exist "real_prediction_gui.py" (
    echo [ERROR] Missing file: real_prediction_gui.py
    goto :missing_files
)

if not exist "real_prediction_engine.py" (
    echo [ERROR] Missing file: real_prediction_engine.py
    goto :missing_files
)

if not exist "lottery_data_20250717.csv" (
    echo [ERROR] Missing file: lottery_data_20250717.csv
    goto :missing_files
)

echo [OK] All core files found

:: Check Python dependencies
echo.
echo [INFO] Checking Python dependencies...

%PYTHON_CMD% -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] tkinter module not available
    echo Please reinstall Python with tkinter support
    pause
    exit /b 1
)
echo [OK] tkinter available

%PYTHON_CMD% -c "import numpy" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] numpy not installed, installing...
    %PYTHON_CMD% -m pip install numpy
)

echo [OK] Dependencies check complete

:: Test real prediction GUI
echo.
echo [INFO] Testing real prediction GUI...
%PYTHON_CMD% -c "from real_prediction_gui import RealPredictionGUI; print('Real prediction GUI OK')" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Real prediction GUI test failed
    echo Please check real_prediction_gui.py
    pause
    exit /b 1
)
echo [OK] Real prediction GUI test passed

:: Start GUI with real prediction
echo.
echo [INFO] Starting GUI with REAL prediction engine...
echo.
echo ================================================
echo  IMPORTANT: This GUI uses REAL prediction!
echo  - Based on 1940 historical records
echo  - Dynamic predictions (different each time)
echo  - No hardcoded numbers
echo  - Scientific statistical analysis
echo ================================================
echo.

%PYTHON_CMD% real_prediction_gui.py

:: Check startup result
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] GUI failed to start (Error: %errorlevel%)
    echo.
    echo Troubleshooting:
    echo 1. Check if all files are present
    echo 2. Ensure Python dependencies are installed
    echo 3. Try running as administrator
    echo 4. Check error messages above
    echo.
    pause
    exit /b 1
) else (
    echo [OK] GUI program exited normally
)

goto :end

:missing_files
echo.
echo [ERROR] Core files missing!
echo Please ensure all system files are in the same directory:
echo - real_prediction_gui.py (Real prediction GUI interface)
echo - real_prediction_engine.py (Real prediction engine)
echo - lottery_data_20250717.csv (Historical data)
echo.
pause
exit /b 1

:end
echo.
echo [INFO] Real prediction GUI session completed
echo.
echo Note: This version uses REAL prediction engine
echo - Predictions are based on actual historical data
echo - Results will be different each time you run
echo - No hardcoded or fixed numbers
echo.
pause
exit /b 0
