#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速系统响应测试
"""

import time
import threading

def test_core_components():
    """测试核心组件响应"""
    print("⚡ 核心组件响应测试")
    print("="*50)
    
    # 1. 测试数据管理器
    print("📊 测试数据管理器...")
    start_time = time.time()
    try:
        from data_attributes import HistoryDataManager
        manager = HistoryDataManager()
        data = manager.load_data()
        load_time = time.time() - start_time
        print(f"✅ 数据管理器: {load_time:.3f}秒, {len(data)}条记录")
    except Exception as e:
        print(f"❌ 数据管理器失败: {e}")
    
    # 2. 测试策略解析器
    print("📋 测试策略解析器...")
    start_time = time.time()
    try:
        from dsl_strategy_parser import DSLStrategyParser
        parser = DSLStrategyParser()
        summary = parser.get_strategy_summary()
        parse_time = time.time() - start_time
        print(f"✅ 策略解析器: {parse_time:.3f}秒, {summary['total_strategies']}个策略")
    except Exception as e:
        print(f"❌ 策略解析器失败: {e}")
    
    # 3. 测试动态映射
    print("🔄 测试动态映射...")
    start_time = time.time()
    try:
        from dynamic_mapping_system import DynamicMappingSystem
        mapping = DynamicMappingSystem()
        zodiac = mapping.get_zodiac_for_number(1, "2025001")
        map_time = time.time() - start_time
        print(f"✅ 动态映射: {map_time:.3f}秒, 号码1={zodiac}")
    except Exception as e:
        print(f"❌ 动态映射失败: {e}")
    
    # 4. 测试预测引擎（轻量级）
    print("🎯 测试预测引擎...")
    start_time = time.time()
    try:
        from prediction_engine import PredictionEngine
        engine = PredictionEngine()
        init_time = time.time() - start_time
        print(f"✅ 预测引擎初始化: {init_time:.3f}秒")
        
        # 快速预测测试
        start_time = time.time()
        result = engine.run_prediction("2025199")
        predict_time = time.time() - start_time
        print(f"✅ 预测执行: {predict_time:.3f}秒, {len(result.final_numbers)}个号码")
        
    except Exception as e:
        print(f"❌ 预测引擎失败: {e}")

def test_gui_creation():
    """测试GUI创建"""
    print(f"\n🖥️ GUI创建测试")
    print("="*50)
    
    try:
        import tkinter as tk
        from gui_main import LotteryPredictionGUI
        
        start_time = time.time()
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        gui = LotteryPredictionGUI(root)
        gui_time = time.time() - start_time
        
        print(f"✅ GUI创建成功: {gui_time:.3f}秒")
        
        # 检查关键组件
        components = [
            'prediction_engine',
            'dsl_parser', 
            'zodiac_analyzer',
            'reback_engine'
        ]
        
        for component in components:
            if hasattr(gui, component):
                print(f"✅ {component}: 存在")
            else:
                print(f"❌ {component}: 缺失")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")

def test_with_timeout(func, timeout=30):
    """带超时的测试"""
    result = {'completed': False, 'error': None}
    
    def target():
        try:
            func()
            result['completed'] = True
        except Exception as e:
            result['error'] = str(e)
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout)
    
    if thread.is_alive():
        print(f"⚠️ 测试超时 ({timeout}秒)")
        return False
    elif result['error']:
        print(f"❌ 测试失败: {result['error']}")
        return False
    elif result['completed']:
        return True
    else:
        return False

def main():
    """主测试流程"""
    print("🚀 快速系统响应测试")
    print("="*60)
    
    # 测试核心组件
    print("⏱️ 核心组件测试 (30秒超时)...")
    core_ok = test_with_timeout(test_core_components, 30)
    
    if core_ok:
        print("✅ 核心组件测试通过")
    else:
        print("❌ 核心组件测试失败或超时")
    
    # 测试GUI创建
    print(f"\n⏱️ GUI创建测试 (30秒超时)...")
    gui_ok = test_with_timeout(test_gui_creation, 30)
    
    if gui_ok:
        print("✅ GUI创建测试通过")
    else:
        print("❌ GUI创建测试失败或超时")
    
    # 总结
    print(f"\n🎯 测试总结:")
    if core_ok and gui_ok:
        print("✅ 系统响应正常，所有组件工作正常")
        print("💡 如果仍有未响应问题，可能是特定操作导致的")
    else:
        print("❌ 系统存在响应问题")
        
        if not core_ok:
            print("   - 核心组件有问题，需要检查数据文件和缓存")
        if not gui_ok:
            print("   - GUI创建有问题，需要检查界面组件")
    
    print(f"\n💡 建议:")
    print("1. 如果测试通过但GUI仍未响应，尝试重启程序")
    print("2. 如果测试失败，删除缓存文件重新初始化")
    print("3. 检查系统资源使用情况")
    print("4. 确保没有其他程序占用数据文件")

if __name__ == "__main__":
    main()
