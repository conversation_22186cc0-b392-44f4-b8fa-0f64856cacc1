import json
from typing import Dict, Any, Optional
from pathlib import Path
import threading
from datetime import datetime
import logging

class ConfigManager:
    """配置管理类"""

    _instance = None
    _lock = threading.Lock()
    _observers = []

    def __new__(cls, config_path: str = "app_config.json"):
        with cls._lock:
            if cls._instance is None:
                instance = super().__new__(cls)
                instance.initialized = False
                instance.observers = []
                cls._instance = instance
            return cls._instance

    def __init__(self, config_path: str = "app_config.json"):
        """
        初始化配置管理器（单例模式）
        Args:
            config_path: 配置文件路径
        """
        if not self.initialized:
            self.config_path = config_path
            self.config = self._load_config()
            self.initialized = True

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 创建默认配置
            default_config = {
                "app_settings": {
                    "theme": "light",
                    "language": "zh_CN",
                    "auto_save": True,
                    "backup_enabled": True
                },
                "data_settings": {
                    "db_path": "lottery_data.db",
                    "csv_import_path": "data/import",
                    "backup_path": "data/backup",
                    "auto_update": True,
                    "update_interval": 300
                },
                "analysis_settings": {
                    "default_periods": 100,
                    "hot_group_count": 10,
                    "min_stability_score": 0.5,
                    "cache_enabled": True,
                    "cache_duration": 3600
                },
                "gui_settings": {
                    "window_size": "1200x800",
                    "font_size": 12,
                    "show_tooltips": True,
                    "chart_style": "seaborn",
                    "table_page_size": 50
                },
                "notification_settings": {
                    "enabled": True,
                    "check_interval": 300,
                    "notify_new_draws": True,
                    "notify_hot_groups": True
                }
            }
            self._save_config(default_config)
            return default_config

    def _save_config(self, config: Dict) -> None:
        """
        保存配置到文件
        Args:
            config: 配置字典
        """
        # 确保配置文件目录存在
        Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)

        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)

        # 通知观察者
        self._notify_observers()

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        Args:
            key: 配置键名，支持点号分隔的多级键
            default: 默认值
        Returns:
            Any: 配置值
        """
        try:
            value = self.config
            for k in key.split('.'):
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any) -> None:
        """
        设置配置项
        Args:
            key: 配置键名，支持点号分隔的多级键
            value: 配置值
        """
        keys = key.split('.')
        target = self.config

        # 遍历到最后一级的父级
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]

        # 设置最后一级的值
        target[keys[-1]] = value

        # 保存到文件
        self._save_config(self.config)

    def register_observer(self, observer: callable) -> None:
        """
        注册配置变更观察者
        Args:
            observer: 回调函数，接收更新后的配置作为参数
        """
        if observer not in self.observers:
            self.observers.append(observer)

    def unregister_observer(self, observer: callable) -> None:
        """
        注销配置变更观察者
        Args:
            observer: 之前注册的回调函数
        """
        if observer in self.observers:
            self.observers.remove(observer)

    def _notify_observers(self) -> None:
        """通知所有观察者配置已更新"""
        for observer in self.observers:
            try:
                observer(self.config)
            except Exception as e:
                logging.error(f"Error notifying observer: {e}")

    def reset_to_defaults(self, section: Optional[str] = None) -> None:
        """
        重置配置到默认值
        Args:
            section: 要重置的配置段，None表示重置所有配置
        """
        default_config = self._load_config()

        if section:
            if section in self.config:
                self.config[section] = default_config.get(section, {})
        else:
            self.config = default_config

        self._save_config(self.config)

    def backup_config(self) -> str:
        """
        备份当前配置
        Returns:
            str: 备份文件路径
        """
        backup_path = Path(self.config_path).parent / "backups"
        backup_path.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_path / f"config_backup_{timestamp}.json"

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)

        return str(backup_file)

    def restore_from_backup(self, backup_file: str) -> None:
        """
        从备份文件恢复配置
        Args:
            backup_file: 备份文件路径
        """
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self._save_config(self.config)
        except Exception as e:
            raise ValueError(f"Failed to restore from backup: {e}")

    def get_all(self) -> Dict:
        """
        获取完整配置
        Returns:
            Dict: 完整配置字典
        """
        return self.config.copy()

    def validate_config(self) -> bool:
        """
        验证配置有效性
        Returns:
            bool: 配置是否有效
        """
        required_sections = [
            "app_settings",
            "data_settings",
            "analysis_settings",
            "gui_settings"
        ]

        return all(section in self.config for section in required_sections)
