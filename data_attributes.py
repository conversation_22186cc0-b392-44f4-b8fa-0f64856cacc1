#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据属性映射模块
自动计算号码对应的生肖、五行、波色等属性
"""

from datetime import datetime
from typing import Dict, Any, Optional
from dynamic_mapping_system import DynamicMappingSystem

class DataAttributeMapper:
    """数据属性映射器"""
    
    def __init__(self):
        # 初始化动态映射系统
        self.dynamic_mapper = DynamicMappingSystem()

        # 保留原有的静态映射作为备用 (按号码尾数)
        self.zodiac_mapping = {
            1: "猪", 2: "狗", 3: "鸡", 4: "猴", 5: "羊", 6: "马",
            7: "蛇", 8: "龙", 9: "兔", 10: "虎", 11: "牛", 12: "鼠",
            13: "猪", 14: "狗", 15: "鸡", 16: "猴", 17: "羊", 18: "马",
            19: "蛇", 20: "龙", 21: "兔", 22: "虎", 23: "牛", 24: "鼠",
            25: "猪", 26: "狗", 27: "鸡", 28: "猴", 29: "羊", 30: "马",
            31: "蛇", 32: "龙", 33: "兔", 34: "虎", 35: "牛", 36: "鼠",
            37: "猪", 38: "狗", 39: "鸡", 40: "猴", 41: "羊", 42: "马",
            43: "蛇", 44: "龙", 45: "兔", 46: "虎", 47: "牛", 48: "鼠",
            49: "猪"
        }
        
        # 五行映射
        self.five_element_mapping = {
            # 金
            4: "金", 5: "金", 12: "金", 13: "金", 20: "金", 21: "金", 
            28: "金", 29: "金", 36: "金", 37: "金", 44: "金", 45: "金",
            # 木
            1: "木", 2: "木", 9: "木", 10: "木", 17: "木", 18: "木",
            25: "木", 26: "木", 33: "木", 34: "木", 41: "木", 42: "木",
            # 水
            6: "水", 7: "水", 14: "水", 15: "水", 22: "水", 23: "水",
            30: "水", 31: "水", 38: "水", 39: "水", 46: "水", 47: "水",
            # 火
            3: "火", 8: "火", 11: "火", 16: "火", 19: "火", 24: "火",
            27: "火", 32: "火", 35: "火", 40: "火", 43: "火", 48: "火",
            # 土
            49: "土"
        }
        
        # 波色映射
        self.wave_color_mapping = {
            # 红波
            "红波": [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
            # 蓝波  
            "蓝波": [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
            # 绿波
            "绿波": [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        }
        
        # 创建反向映射
        self.number_to_wave = {}
        for color, numbers in self.wave_color_mapping.items():
            for number in numbers:
                self.number_to_wave[number] = color
    
    def get_zodiac(self, number: int) -> str:
        """获取生肖"""
        return self.zodiac_mapping.get(number, "未知")
    
    def get_five_element(self, number: int) -> str:
        """获取五行"""
        return self.five_element_mapping.get(number, "未知")
    
    def get_wave_color(self, number: int) -> str:
        """获取波色"""
        return self.number_to_wave.get(number, "未知")
    
    def get_big_small(self, number: int) -> str:
        """获取大小"""
        return "大" if number >= 25 else "小"
    
    def get_odd_even(self, number: int) -> str:
        """获取单双"""
        return "单" if number % 2 == 1 else "双"
    
    def get_tail_number(self, number: int) -> int:
        """获取尾数"""
        return number % 10
    
    def map_all_attributes(self, number: int, period: str = None) -> Dict[str, Any]:
        """映射所有属性（支持动态映射）"""
        if not (1 <= number <= 49):
            return {
                "zodiac": "无效",
                "five_element": "无效",
                "wave_color": "无效",
                "big_small": "无效",
                "odd_even": "无效",
                "tail_number": 0
            }

        # 如果提供期号，使用动态映射
        if period:
            return self.dynamic_mapper.get_complete_attributes(number, period)

        # 否则使用静态映射（向后兼容）
        return {
            "zodiac": self.get_zodiac(number),
            "five_element": self.get_five_element(number),
            "wave_color": self.get_wave_color(number),
            "big_small": self.get_big_small(number),
            "odd_even": self.get_odd_even(number),
            "tail_number": self.get_tail_number(number)
        }
    
    def create_complete_record(self, period: str, date: str, special_code: int) -> Dict[str, Any]:
        """创建完整的开奖记录（使用动态映射）"""
        attributes = self.map_all_attributes(special_code, period)  # 传入期号使用动态映射

        return {
            "period": period,
            "draw_date": date,
            "special_code": special_code,
            "zodiac": attributes["zodiac"],
            "five_element": attributes["five_element"],
            "wave_color": attributes["wave_color"],
            "big_small": attributes["big_small"],
            "odd_even": attributes["odd_even"],
            "tail_number": attributes["tail_number"]
        }
    
    def validate_period(self, period: str) -> bool:
        """验证期号格式"""
        if not period:
            return False
        
        # 检查期号格式 (例如: 2025001, 2025199等)
        if len(period) == 7 and period.isdigit():
            year = int(period[:4])
            number = int(period[4:])
            return 2020 <= year <= 2030 and 1 <= number <= 365
        
        return False
    
    def validate_date(self, date_str: str) -> bool:
        """验证日期格式"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def validate_special_code(self, code: int) -> bool:
        """验证特码"""
        return isinstance(code, int) and 1 <= code <= 49

class HistoryDataManager:
    """历史数据管理器"""
    
    def __init__(self, data_file: str = "lottery_data_20250717.csv"):
        self.data_file = data_file
        self.mapper = DataAttributeMapper()
        self.data_cache = []
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            import csv
            self.data_cache = []
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 处理BOM字符
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    record = {
                        'period': row[period_key],
                        'draw_date': row['draw_date'],
                        'special_code': int(row['special_code']) if row['special_code'] else None,
                        'zodiac': row.get('zodiac', ''),
                        'five_element': row.get('five_element', ''),
                        'wave_color': self.mapper.get_wave_color(int(row['special_code'])) if row['special_code'] else ''
                    }
                    self.data_cache.append(record)
            
            # 按期号排序 (最新在前)
            self.data_cache.sort(key=lambda x: x['period'], reverse=True)
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            self.data_cache = []

        return self.data_cache
    
    def save_data(self):
        """保存数据到文件"""
        try:
            import csv
            
            # 按期号排序 (最早在前，用于保存)
            sorted_data = sorted(self.data_cache, key=lambda x: x['period'])
            
            with open(self.data_file, 'w', newline='', encoding='utf-8') as f:
                if sorted_data:
                    fieldnames = ['period_number', 'draw_date', 'special_code', 'zodiac', 'five_element']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    
                    for record in sorted_data:
                        writer.writerow({
                            'period_number': record['period'],
                            'draw_date': record['draw_date'],
                            'special_code': record['special_code'],
                            'zodiac': record['zodiac'],
                            'five_element': record['five_element']
                        })
            
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def add_or_update_record(self, period: str, date: str, special_code: int) -> tuple[bool, str]:
        """添加或更新记录"""
        # 验证输入
        if not self.mapper.validate_period(period):
            return False, "期号格式错误 (应为7位数字，如2025001)"
        
        if not self.mapper.validate_date(date):
            return False, "日期格式错误 (应为YYYY-MM-DD格式)"
        
        if not self.mapper.validate_special_code(special_code):
            return False, "特码错误 (应为1-49的整数)"
        
        # 创建完整记录
        new_record = self.mapper.create_complete_record(period, date, special_code)
        
        # 检查是否存在相同期号
        existing_index = None
        for i, record in enumerate(self.data_cache):
            if record['period'] == period:
                existing_index = i
                break
        
        if existing_index is not None:
            # 更新现有记录
            self.data_cache[existing_index] = new_record
            message = f"期号 {period} 的数据已更新"
        else:
            # 添加新记录
            self.data_cache.append(new_record)
            message = f"期号 {period} 的数据已添加"
        
        # 重新排序
        self.data_cache.sort(key=lambda x: x['period'], reverse=True)
        
        return True, message
    
    def delete_record(self, period: str) -> tuple[bool, str]:
        """删除记录"""
        for i, record in enumerate(self.data_cache):
            if record['period'] == period:
                deleted_record = self.data_cache.pop(i)
                return True, f"期号 {period} 的数据已删除"
        
        return False, f"未找到期号 {period} 的数据"
    
    def get_record(self, period: str) -> Optional[Dict[str, Any]]:
        """获取指定期号的记录"""
        for record in self.data_cache:
            if record['period'] == period:
                return record
        return None
    
    def get_all_records(self) -> list:
        """获取所有记录"""
        return self.data_cache.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.data_cache:
            return {}
        
        special_codes = [r['special_code'] for r in self.data_cache if r['special_code']]
        
        if not special_codes:
            return {"total_records": len(self.data_cache), "valid_records": 0}
        
        return {
            "total_records": len(self.data_cache),
            "valid_records": len(special_codes),
            "latest_period": self.data_cache[0]['period'],
            "earliest_period": self.data_cache[-1]['period'],
            "avg_special_code": sum(special_codes) / len(special_codes),
            "max_special_code": max(special_codes),
            "min_special_code": min(special_codes)
        }

if __name__ == "__main__":
    # 测试数据属性映射
    mapper = DataAttributeMapper()
    
    print("=== 数据属性映射测试 ===")
    test_numbers = [1, 13, 25, 37, 49]
    
    for number in test_numbers:
        attrs = mapper.map_all_attributes(number)
        print(f"号码 {number:2d}: {attrs}")
    
    print("\n=== 历史数据管理测试 ===")
    manager = HistoryDataManager()
    
    # 测试添加记录
    success, message = manager.add_or_update_record("2025200", "2025-07-18", 25)
    print(f"添加记录: {success} - {message}")
    
    # 测试更新记录
    success, message = manager.add_or_update_record("2025200", "2025-07-18", 30)
    print(f"更新记录: {success} - {message}")
    
    # 获取统计信息
    stats = manager.get_statistics()
    print(f"统计信息: {stats}")
