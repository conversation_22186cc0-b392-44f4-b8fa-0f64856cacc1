#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断为什么wuxing_2_hot没有参与预测
"""

from dsl_strategy_parser import DSLStrategyParser
from prediction_engine import PredictionEngine
import inspect

def check_strategy_config():
    """检查策略配置"""
    print("⚙️ 检查策略配置")
    print("="*50)
    
    parser = DSLStrategyParser()
    strategy_summary = parser.get_strategy_summary()
    
    print(f"📊 策略总数: {strategy_summary['total_strategies']}")
    print(f"📊 激活策略: {strategy_summary['active_strategies']}")
    print()
    
    wuxing_found = False
    wuxing_active = False
    
    print("📋 所有策略状态:")
    for strategy in strategy_summary['strategies']:
        status = "✅ 启用" if strategy['active'] else "❌ 禁用"
        print(f"   - {strategy['name']} ({strategy['id']}): {status}")
        
        if strategy['id'] == 'wuxing_2_hot':
            wuxing_found = True
            wuxing_active = strategy['active']
            print(f"     权重: {strategy['weight']}")
            print(f"     类型: {strategy['type']}")
    
    print(f"\n🔍 wuxing_2_hot状态:")
    if wuxing_found:
        if wuxing_active:
            print("✅ wuxing_2_hot策略已找到且已启用")
        else:
            print("❌ wuxing_2_hot策略已找到但未启用")
    else:
        print("❌ wuxing_2_hot策略未找到")
    
    return wuxing_active

def check_prediction_engine_logic():
    """检查预测引擎逻辑"""
    print(f"\n🔧 检查预测引擎逻辑")
    print("="*50)
    
    try:
        # 获取预测引擎的源码
        engine = PredictionEngine()
        
        # 检查_run_traditional_strategies方法
        source = inspect.getsource(engine._run_traditional_strategies)
        
        print("🔍 查找wuxing相关逻辑...")
        lines = source.split('\n')
        
        wuxing_logic_found = False
        for i, line in enumerate(lines):
            if 'wuxing' in line.lower():
                print(f"   第{i+1}行: {line.strip()}")
                wuxing_logic_found = True
        
        if not wuxing_logic_found:
            print("❌ 未在预测引擎中找到wuxing相关逻辑")
        else:
            print("✅ 找到wuxing相关逻辑")
        
        # 检查策略ID匹配逻辑
        print(f"\n🔍 检查策略ID匹配逻辑...")
        if 'strategy_id' in source and 'wuxing' in source:
            print("✅ 找到策略ID匹配逻辑")
        else:
            print("❌ 策略ID匹配逻辑可能有问题")
        
    except Exception as e:
        print(f"❌ 检查预测引擎失败: {e}")

def test_prediction_step_by_step():
    """逐步测试预测过程"""
    print(f"\n🧪 逐步测试预测过程")
    print("="*50)
    
    try:
        # 1. 创建预测引擎
        print("📊 1. 创建预测引擎...")
        engine = PredictionEngine()
        print("✅ 预测引擎创建成功")
        
        # 2. 检查策略解析器
        print(f"\n📋 2. 检查策略解析器...")
        parser = engine.dsl_parser
        summary = parser.get_strategy_summary()
        
        active_strategies = [s for s in summary['strategies'] if s['active']]
        print(f"   激活的策略: {len(active_strategies)} 个")
        
        for strategy in active_strategies:
            print(f"     - {strategy['name']} ({strategy['id']})")
        
        # 3. 运行预测并监控
        print(f"\n🎯 3. 运行预测...")
        result = engine.run_prediction("2025199")
        
        print(f"✅ 预测完成")
        print(f"   使用策略: {result.total_strategies_used} 个")
        print(f"   推荐号码: {result.final_numbers}")
        
        # 4. 检查策略详情
        print(f"\n📋 4. 检查策略详情...")
        if result.strategy_details:
            wuxing_found_in_result = False
            
            for strategy in result.strategy_details:
                strategy_id = strategy.get('strategy_id', 'Unknown')
                strategy_name = strategy.get('strategy_name', 'Unknown')
                
                print(f"   - {strategy_name} ({strategy_id})")
                
                if 'wuxing' in strategy_id.lower():
                    wuxing_found_in_result = True
                    print("     ✅ 这是wuxing策略！")
            
            if not wuxing_found_in_result:
                print("❌ 在预测结果中未找到wuxing策略")
            else:
                print("✅ 在预测结果中找到wuxing策略")
        else:
            print("❌ 无策略详情")
        
        return result
        
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_strategy_filtering():
    """检查策略过滤逻辑"""
    print(f"\n🔍 检查策略过滤逻辑")
    print("="*50)
    
    try:
        parser = DSLStrategyParser()
        summary = parser.get_strategy_summary()
        
        print("📊 策略过滤分析:")
        
        for strategy in summary['strategies']:
            strategy_id = strategy['id']
            strategy_name = strategy['name']
            active = strategy['active']
            
            print(f"\n策略: {strategy_name} ({strategy_id})")
            print(f"   启用状态: {'✅ 是' if active else '❌ 否'}")
            
            if strategy_id == 'wuxing_2_hot':
                print(f"   🔍 这是目标策略!")
                
                if active:
                    print(f"   ✅ 策略已启用，应该参与预测")
                else:
                    print(f"   ❌ 策略未启用，不会参与预测")
                
                # 检查过滤条件
                filters = strategy.get('filters', {})
                if filters:
                    print(f"   📋 过滤条件:")
                    for key, value in filters.items():
                        print(f"     {key}: {value}")
                else:
                    print(f"   📋 无过滤条件")
    
    except Exception as e:
        print(f"❌ 策略过滤检查失败: {e}")

def check_mock_strategies():
    """检查模拟策略生成"""
    print(f"\n🎭 检查模拟策略生成")
    print("="*50)
    
    try:
        # 模拟预测引擎中的策略生成逻辑
        parser = DSLStrategyParser()
        strategy_summary = parser.get_strategy_summary()
        
        print("📊 模拟策略生成过程:")
        
        mock_strategies = {}
        
        for strategy in strategy_summary['strategies']:
            if strategy['active']:
                strategy_id = strategy['id']
                print(f"   处理策略: {strategy_id}")
                
                # 根据策略类型生成不同的模拟数据
                if 'wuxing' in strategy_id:
                    mock_strategies[strategy_id] = {"hit_count": 25, "avg_omit_before_hit": 3}
                    print(f"     ✅ 生成wuxing模拟数据")
                elif 'size_odd' in strategy_id:
                    mock_strategies[strategy_id] = {"hit_count": 18, "avg_omit_before_hit": 4}
                    print(f"     ✅ 生成size_odd模拟数据")
                elif 'tail' in strategy_id:
                    mock_strategies[strategy_id] = {"hit_count": 22, "avg_omit_before_hit": 3.5}
                    print(f"     ✅ 生成tail模拟数据")
                else:
                    mock_strategies[strategy_id] = {"hit_count": 15, "avg_omit_before_hit": 5}
                    print(f"     ✅ 生成默认模拟数据")
        
        print(f"\n📋 生成的模拟策略:")
        for strategy_id, data in mock_strategies.items():
            print(f"   {strategy_id}: {data}")
        
        if 'wuxing_2_hot' in mock_strategies:
            print(f"\n✅ wuxing_2_hot已包含在模拟策略中")
        else:
            print(f"\n❌ wuxing_2_hot未包含在模拟策略中")
        
        return mock_strategies
        
    except Exception as e:
        print(f"❌ 模拟策略检查失败: {e}")
        return {}

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案建议")
    print("="*50)
    
    solutions = """
🔧 可能的解决方案:

1. 🔄 重启程序:
   - 配置文件更改后需要重启程序
   - 清除内存中的旧配置缓存

2. 📋 检查配置文件:
   - 确认strategy_config.yaml中wuxing_2_hot的active为true
   - 检查YAML格式是否正确
   - 验证缩进和语法

3. 🔧 检查预测引擎:
   - 确认_run_traditional_strategies方法中包含wuxing逻辑
   - 验证策略ID匹配是否正确
   - 检查策略过滤条件

4. 🧪 调试模式:
   - 在预测过程中添加调试输出
   - 跟踪策略处理流程
   - 确认策略是否被正确识别

5. 📊 数据验证:
   - 检查策略解析器是否正确读取配置
   - 验证策略权重和过滤条件
   - 确认策略类型匹配
"""
    
    print(solutions)

def main():
    """主诊断流程"""
    print("🔍 诊断wuxing_2_hot未参与预测的问题")
    print("="*60)
    
    # 1. 检查策略配置
    wuxing_active = check_strategy_config()
    
    # 2. 检查预测引擎逻辑
    check_prediction_engine_logic()
    
    # 3. 逐步测试预测
    result = test_prediction_step_by_step()
    
    # 4. 检查策略过滤
    check_strategy_filtering()
    
    # 5. 检查模拟策略生成
    mock_strategies = check_mock_strategies()
    
    # 6. 提供解决方案
    provide_solutions()
    
    # 总结
    print(f"\n🎯 诊断总结:")
    if wuxing_active:
        print("✅ wuxing_2_hot策略配置正确")
        if result and result.total_strategies_used >= 3:
            print("✅ 预测中使用了多个策略，wuxing可能已参与")
        else:
            print("❌ 预测中策略数量不足，wuxing可能未参与")
    else:
        print("❌ wuxing_2_hot策略未启用")
    
    print(f"\n💡 建议:")
    print("1. 重启程序重新加载配置")
    print("2. 在GUI中验证策略数量是否恢复到3个")
    print("3. 检查预测结果中是否包含五行相关号码")

if __name__ == "__main__":
    main()
