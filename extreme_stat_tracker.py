import json
from datetime import datetime

class ExtremeStatTracker:
    def __init__(self, db_connection, combo_list, history_draws, strategy_config=None):
        self.db = db_connection
        self.combos = combo_list
        self.history = history_draws
        self.strategy_config = strategy_config or {}

    def run_tracking(self):
        for combo in self.combos:
            key = combo["key"]
            combo_type = combo["type"]
            members = combo["members"]

            # 检查策略是否启用
            if not self._is_strategy_enabled(combo_type):
                continue

            current_miss = 0
            max_miss = 0
            hit_count = 0
            last_hit = None

            # Iterate through history, which is already in reverse chronological order
            for idx, draw in enumerate(self.history):
                is_hit = False
                if combo_type == "生肖4组合" and 'zodiac' in draw:
                    if any(member == draw['zodiac'] for member in members):
                        is_hit = True
                elif combo_type == "五行2组合" and 'five_element' in draw:
                    if any(member == draw['five_element'] for member in members):
                        is_hit = True

                if is_hit:
                    hit_count += 1
                    if current_miss > max_miss:
                        max_miss = current_miss
                    current_miss = 0
                    last_hit = draw.get('period')
                else:
                    current_miss += 1
            
            # After iterating through all history, the final current_miss is the current miss streak
            # If the last draw was a hit, current_miss will be 0.
            # If the history is empty, this loop doesn't run, and values remain 0.

            near_extreme = self.is_near_extreme(current_miss, max_miss)

            self.update_db(combo_type, key, hit_count, current_miss, max_miss, last_hit, near_extreme)

    def is_near_extreme(self, current_miss, max_miss, threshold=0.9):
        if max_miss == 0:
            return False
        return current_miss >= int(max_miss * threshold)

    def _is_strategy_enabled(self, combo_type):
        """检查策略是否启用"""
        if not self.strategy_config:
            return True  # 如果没有配置，默认启用

        # 策略类型映射
        strategy_mapping = {
            "五行2组合": "wuxing_2_hot",
            "色波组合": "wave_balance"
        }

        strategy_id = strategy_mapping.get(combo_type)
        if not strategy_id:
            return True  # 未知类型默认启用

        # 检查策略配置
        for strategy in self.strategy_config.get('strategies', []):
            if strategy.get('id') == strategy_id:
                is_active = strategy.get('active', True)
                if not is_active:
                    print(f"⏭️ 跳过已禁用的策略: {combo_type}")
                return is_active

        return True  # 如果找不到配置，默认启用

    def update_db(self, combo_type, key, hit_count, current_miss, max_miss, last_hit, near_extreme):
        # This is a pseudo-code for database interaction.
        # In a real application, this would be an actual database call.
        print(f"Updating DB for {combo_type} - {key}:")
        print(f"  Hit Count: {hit_count}")
        print(f"  Current Miss: {current_miss}")
        print(f"  Max Miss: {max_miss}")
        print(f"  Last Hit Period: {last_hit}")
        print(f"  Is Near Extreme: {near_extreme}")
        
        # Example of how it might work with a real DB connection
        # self.db.insert_or_update("combo_extremes", {
        #     "combo_type": combo_type,
        #     "combo_key": key,
        #     "hit_count": hit_count,
        #     "current_miss": current_miss,
        #     "max_miss": max_miss,
        #     "last_hit_period": last_hit,
        #     "is_near_extreme": near_extreme,
        #     "updated_at": datetime.now()
        # })

if __name__ == '__main__':
    # Example Usage
    # This part requires a mock database connection, a list of combos, and historical data.

    # 1. Load combos from the generated JSON file
    with open('combos.json', 'r', encoding='utf-8') as f:
        all_combos = json.load(f)
    
    # We'll use wuxing_2 for this example
    wuxing_2_combos = all_combos.get('wuxing_2', [])

    # This example is now handled by main.py with real data.
    # Keeping the structure for potential standalone testing.
    history_draws = []

    # 3. Mock DB connection
    class MockDB:
        def insert_or_update(self, table, data):
            print(f"DB Operation on table '{table}': {data}")

    db_conn = MockDB()

    # 4. Initialize and run the tracker
    if history_draws and wuxing_2_combos:
        tracker = ExtremeStatTracker(db_conn, wuxing_2_combos, history_draws)
        tracker.run_tracking()
    else:
        print("Skipping tracker run due to empty history or combos.")
