#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3
import sys

print("🔧 快速系统状态检查")
print("=" * 40)

# 1. 检查Python版本
print(f"🐍 Python版本: {sys.version.split()[0]}")

# 2. 检查核心文件
print("\n📁 核心文件检查:")
files = ['main.py', 'combo_generator.py', 'extreme_stat_tracker.py', 'strategy_scorer.py', 'lottery_data.db']
for f in files:
    if os.path.exists(f):
        size = os.path.getsize(f)
        print(f"  ✅ {f} ({size} bytes)")
    else:
        print(f"  ❌ {f} (不存在)")

# 3. 检查数据库
print("\n🗄️ 数据库检查:")
try:
    conn = sqlite3.connect('lottery_data.db')
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"  ✅ 数据库连接成功，{len(tables)} 个表")
    
    if 'lottery_records' in tables:
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        count = cursor.fetchone()[0]
        print(f"  📊 lottery_records: {count} 条记录")
    
    conn.close()
except Exception as e:
    print(f"  ❌ 数据库错误: {e}")

# 4. 测试模块导入
print("\n📦 模块导入测试:")
modules = ['combo_generator', 'extreme_stat_tracker', 'strategy_scorer']
for mod in modules:
    try:
        __import__(mod)
        print(f"  ✅ {mod}")
    except Exception as e:
        print(f"  ❌ {mod}: {e}")

# 5. 快速功能测试
print("\n🧪 功能测试:")
try:
    from combo_generator import ComboGenerator
    gen = ComboGenerator()
    combos = gen.generate_shengxiao_4()
    print(f"  ✅ 组合生成: {len(combos)} 个生肖4组合")
except Exception as e:
    print(f"  ❌ 组合生成失败: {e}")

print("\n✅ 检查完成!")
