#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维生肖预测数据库结构设计
"""

import sqlite3
import json
from datetime import datetime, date
from typing import Dict, List, Any, Optional

class MultiDimensionalZodiacDB:
    """多维生肖数据库管理器"""
    
    def __init__(self, db_path: str = "multi_zodiac.db"):
        self.db_path = db_path
        self.init_database()
        self.init_zodiac_dimensions()
    
    def init_database(self):
        """初始化数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 1. 主表：lottery_draws
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_draws (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                draw_date DATE NOT NULL,
                year INTEGER NOT NULL,
                draw_number VARCHAR(20) NOT NULL UNIQUE,
                special_number INTEGER NOT NULL,
                all_numbers VARCHAR(200),
                zodiac VARCHAR(10),
                zodiac_number_map JSON,
                multi_dim_features JSO<PERSON>,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 2. 多维生肖表：zodiac_dimensions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS zodiac_dimensions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                zodiac VARCHAR(10) NOT NULL,
                dim_category VARCHAR(50) NOT NULL,
                dim_group VARCHAR(50) NOT NULL,
                valid_from DATE NOT NULL,
                valid_to DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 3. 春节日期表：spring_festival_dates
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS spring_festival_dates (
                year INTEGER PRIMARY KEY,
                festival_date DATE NOT NULL,
                zodiac_year VARCHAR(10) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 4. 极限遗漏统计表：extreme_miss_stats
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extreme_miss_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dim_category VARCHAR(50) NOT NULL,
                dim_group VARCHAR(50) NOT NULL,
                max_miss_periods INTEGER NOT NULL,
                avg_miss_periods REAL,
                last_updated DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 5. 回补记录表：reback_records
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reback_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dim_category VARCHAR(50) NOT NULL,
                dim_group VARCHAR(50) NOT NULL,
                extreme_date DATE NOT NULL,
                reback_start_date DATE NOT NULL,
                reback_periods INTEGER NOT NULL,
                reback_intensity REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ 多维生肖数据库结构初始化完成")
    
    def init_zodiac_dimensions(self):
        """初始化多维生肖分类数据"""
        
        # 多维生肖定义
        zodiac_dimensions = {
            # 时间维度
            "日肖": ["兔", "龙", "蛇", "马", "羊", "猴"],
            "夜肖": ["鼠", "牛", "虎", "鸡", "狗", "猪"],
            
            # 空间维度
            "左肖": ["鼠", "牛", "龙", "蛇", "猴", "鸡"],
            "右肖": ["虎", "兔", "马", "羊", "狗", "猪"],
            
            # 阴阳维度
            "阴肖": ["鼠", "龙", "蛇", "马", "狗", "猪"],
            "阳肖": ["牛", "虎", "兔", "羊", "猴", "鸡"],
            
            # 独合维度
            "独肖": ["鼠", "牛", "虎", "兔", "马", "羊"],
            "合肖": ["龙", "蛇", "猴", "鸡", "狗", "猪"],
            
            # 家野维度
            "家肖": ["牛", "马", "羊", "狗", "鸡", "猪"],
            "野肖": ["鼠", "虎", "兔", "龙", "蛇", "猴"],
            
            # 天地维度
            "天肖": ["牛", "猴", "兔", "猪", "马", "龙"],
            "地肖": ["蛇", "虎", "羊", "鸡", "狗", "鼠"],
            
            # 性别维度
            "男肖": ["鼠", "牛", "虎", "龙", "马", "猴", "狗"],
            "女肖": ["兔", "蛇", "羊", "鸡", "猪"],
            
            # 吉凶维度
            "吉肖": ["兔", "龙", "蛇", "马", "羊", "鸡"],
            "凶肖": ["鼠", "牛", "虎", "猴", "狗", "猪"],
            
            # 前后维度
            "前肖": ["鼠", "牛", "虎", "兔", "龙", "蛇"],
            "后肖": ["马", "羊", "猴", "鸡", "狗", "猪"],
            
            # 笔画维度
            "单笔": ["鼠", "龙", "马", "蛇", "鸡", "猪"],
            "双笔": ["虎", "猴", "狗", "兔", "羊", "牛"],
            
            # 性格维度
            "胆大": ["牛", "虎", "马", "猴", "狗", "猪"],
            "胆小": ["鼠", "兔", "龙", "蛇", "羊", "鸡"],
            
            # 颜色维度
            "红肖": ["马", "兔", "鼠", "鸡"],
            "蓝肖": ["蛇", "虎", "猪", "猴"],
            "绿肖": ["羊", "龙", "牛", "狗"],
            
            # 琴棋书画
            "琴": ["兔", "蛇", "鸡"],
            "棋": ["鼠", "牛", "狗"],
            "书": ["虎", "龙", "马"],
            "画": ["羊", "猴", "猪"],
            
            # 四季
            "春": ["虎", "兔", "龙"],
            "夏": ["蛇", "马", "羊"],
            "秋": ["猴", "狗", "鸡"],
            "冬": ["鼠", "牛", "猪"],
            
            # 梅兰菊竹
            "梅": ["龙", "牛", "狗"],
            "兰": ["兔", "羊", "蛇"],
            "菊": ["鼠", "马", "猪"],
            "竹": ["虎", "鸡", "猴"],
            
            # 军队阵营
            "元帅": ["鼠", "虎", "狗"],
            "大将": ["牛", "蛇", "猴"],
            "先锋": ["马", "羊", "鸡"],
            "小兵": ["兔", "龙", "猪"],
            
            # 特殊组合
            "五福肖": ["鼠", "虎", "兔", "蛇", "猴"],
            "白边": ["鼠", "牛", "虎", "鸡", "狗", "猪"],
            "黑中": ["兔", "龙", "蛇", "马", "羊", "猴"]
        }
        
        # 六合组合
        six_combinations = {
            "鼠牛合": ["鼠", "牛"],
            "龙鸡合": ["龙", "鸡"],
            "虎猪合": ["虎", "猪"],
            "蛇猴合": ["蛇", "猴"],
            "兔狗合": ["兔", "狗"],
            "马羊合": ["马", "羊"]
        }
        
        # 三合组合
        three_combinations = {
            "鼠龙猴合": ["鼠", "龙", "猴"],
            "牛蛇鸡合": ["牛", "蛇", "鸡"],
            "虎马狗合": ["虎", "马", "狗"],
            "兔羊猪合": ["兔", "羊", "猪"]
        }
        
        # 合并所有维度
        all_dimensions = {**zodiac_dimensions, **six_combinations, **three_combinations}
        
        # 插入数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM zodiac_dimensions")
        
        # 插入新数据
        for dim_group, zodiacs in all_dimensions.items():
            # 确定分类
            if dim_group in ["日肖", "夜肖"]:
                dim_category = "时间维度"
            elif dim_group in ["左肖", "右肖"]:
                dim_category = "空间维度"
            elif dim_group in ["阴肖", "阳肖"]:
                dim_category = "阴阳维度"
            elif dim_group in ["独肖", "合肖"]:
                dim_category = "独合维度"
            elif dim_group in ["家肖", "野肖"]:
                dim_category = "家野维度"
            elif dim_group in ["天肖", "地肖"]:
                dim_category = "天地维度"
            elif dim_group in ["男肖", "女肖"]:
                dim_category = "性别维度"
            elif dim_group in ["吉肖", "凶肖"]:
                dim_category = "吉凶维度"
            elif dim_group in ["前肖", "后肖"]:
                dim_category = "前后维度"
            elif dim_group in ["单笔", "双笔"]:
                dim_category = "笔画维度"
            elif dim_group in ["胆大", "胆小"]:
                dim_category = "性格维度"
            elif dim_group in ["红肖", "蓝肖", "绿肖"]:
                dim_category = "颜色维度"
            elif dim_group in ["琴", "棋", "书", "画"]:
                dim_category = "琴棋书画"
            elif dim_group in ["春", "夏", "秋", "冬"]:
                dim_category = "四季维度"
            elif dim_group in ["梅", "兰", "菊", "竹"]:
                dim_category = "梅兰菊竹"
            elif dim_group in ["元帅", "大将", "先锋", "小兵"]:
                dim_category = "军队阵营"
            elif dim_group in ["五福肖", "白边", "黑中"]:
                dim_category = "特殊组合"
            elif "合" in dim_group:
                if len(zodiacs) == 2:
                    dim_category = "六合组合"
                else:
                    dim_category = "三合组合"
            else:
                dim_category = "其他维度"
            
            # 为每个生肖插入记录
            for zodiac in zodiacs:
                cursor.execute('''
                    INSERT INTO zodiac_dimensions 
                    (zodiac, dim_category, dim_group, valid_from, valid_to)
                    VALUES (?, ?, ?, ?, ?)
                ''', (zodiac, dim_category, dim_group, '2020-01-01', None))
        
        conn.commit()
        conn.close()
        print("✅ 多维生肖分类数据初始化完成")
    
    def init_spring_festival_dates(self):
        """初始化春节日期数据"""
        spring_festivals = [
            (2020, '2020-01-25', '鼠'),
            (2021, '2021-02-12', '牛'),
            (2022, '2022-02-01', '虎'),
            (2023, '2023-01-22', '兔'),
            (2024, '2024-02-10', '龙'),
            (2025, '2025-01-29', '蛇'),
            (2026, '2026-02-17', '马'),
            (2027, '2027-02-06', '羊'),
            (2028, '2028-01-26', '猴'),
            (2029, '2029-02-13', '鸡'),
            (2030, '2030-02-03', '狗')
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for year, festival_date, zodiac_year in spring_festivals:
            cursor.execute('''
                INSERT OR REPLACE INTO spring_festival_dates 
                (year, festival_date, zodiac_year)
                VALUES (?, ?, ?)
            ''', (year, festival_date, zodiac_year))
        
        conn.commit()
        conn.close()
        print("✅ 春节日期数据初始化完成")
    
    def get_zodiac_dimensions(self) -> Dict[str, List[str]]:
        """获取所有多维生肖分类"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT dim_category, dim_group, GROUP_CONCAT(zodiac) as zodiacs
            FROM zodiac_dimensions
            GROUP BY dim_category, dim_group
            ORDER BY dim_category, dim_group
        ''')
        
        dimensions = {}
        for row in cursor.fetchall():
            dim_category, dim_group, zodiacs = row
            dimensions[f"{dim_category}-{dim_group}"] = zodiacs.split(',')
        
        conn.close()
        return dimensions
    
    def get_dimensions_by_category(self, category: str) -> Dict[str, List[str]]:
        """获取指定分类的维度"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT dim_group, GROUP_CONCAT(zodiac) as zodiacs
            FROM zodiac_dimensions
            WHERE dim_category = ?
            GROUP BY dim_group
            ORDER BY dim_group
        ''', (category,))
        
        dimensions = {}
        for row in cursor.fetchall():
            dim_group, zodiacs = row
            dimensions[dim_group] = zodiacs.split(',')
        
        conn.close()
        return dimensions

def test_multi_dimensional_zodiac_db():
    """测试多维生肖数据库"""
    print("🧪 测试多维生肖数据库")
    print("="*50)
    
    # 创建数据库
    db = MultiDimensionalZodiacDB()
    
    # 初始化春节数据
    db.init_spring_festival_dates()
    
    # 获取所有维度
    dimensions = db.get_zodiac_dimensions()
    
    print(f"📊 总维度数: {len(dimensions)}")
    
    # 显示部分维度
    categories = ["时间维度", "颜色维度", "琴棋书画", "六合组合"]
    
    for category in categories:
        print(f"\n📋 {category}:")
        cat_dims = db.get_dimensions_by_category(category)
        for dim_group, zodiacs in cat_dims.items():
            print(f"   {dim_group}: {', '.join(zodiacs)}")
    
    print(f"\n✅ 多维生肖数据库测试完成")

if __name__ == "__main__":
    test_multi_dimensional_zodiac_db()
