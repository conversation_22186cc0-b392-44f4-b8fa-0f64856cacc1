#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库结构和内容
"""

import sqlite3
import os

def check_database():
    db_path = 'lottery_data.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件 {db_path} 不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 数据库包含 {len(tables)} 个表:")
        for table in tables:
            table_name = table[0]
            print(f"\n🔍 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("  字段结构:")
            for col in columns:
                print(f"    {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count}")
            
            # 显示前几条记录
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                rows = cursor.fetchall()
                print("  示例数据:")
                for i, row in enumerate(rows, 1):
                    print(f"    {i}: {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
