#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全重建高级分析系统
从头处理所有历史数据
"""

import os
import time
from optimized_zodiac_engine import OptimizedZodiacEngine
from prediction_engine import PredictionEngine

def complete_rebuild():
    """完全重建系统"""
    print("🔄 完全重建高级分析系统")
    print("="*60)
    
    # 1. 删除所有缓存
    cache_files = ["zodiac_cache.pkl"]
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            print(f"🗑️ 删除缓存文件: {cache_file}")
            os.remove(cache_file)
    
    # 2. 创建新的引擎实例
    print("📊 创建新的引擎实例...")
    engine = OptimizedZodiacEngine()
    
    # 3. 加载历史数据
    print("📈 加载历史数据...")
    pred_engine = PredictionEngine()
    history_data = pred_engine.load_historical_data()
    
    print(f"✅ 加载了 {len(history_data)} 期历史数据")
    print(f"   期号范围: {history_data[-1]['period']} - {history_data[0]['period']}")
    
    # 4. 手动处理所有历史数据（绕过增量更新逻辑）
    print("🔄 手动处理所有历史数据...")
    start_time = time.time()
    
    # 重置引擎状态
    engine.last_processed_period = ""
    
    # 按时间顺序处理（从最早到最新）
    sorted_data = sorted(history_data, key=lambda x: x.get('period', ''))
    
    processed_count = 0
    error_count = 0
    
    for i, period_data in enumerate(sorted_data):
        period = period_data.get('period', '')
        special_code = period_data.get('special_code')
        
        if not period or not special_code:
            error_count += 1
            continue
        
        # 使用动态映射获取生肖
        zodiac = engine.dynamic_mapper.get_zodiac_for_number(special_code, period)
        if not zodiac or zodiac == "未知":
            error_count += 1
            continue
        
        # 手动更新所有小组
        for group in engine.all_groups.values():
            if zodiac in group.members:
                # 命中：记录遗漏历史，清零当前遗漏
                if group.current_miss > 0:
                    group.miss_history.append(group.current_miss)
                    group.max_miss = max(group.max_miss, group.current_miss)
                
                group.current_miss = 0
                group.internal_misses[zodiac] = 0
                group.hit_count += 1
            else:
                # 未命中：遗漏+1
                group.current_miss += 1
            
            # 更新组内其他生肖遗漏
            for member in group.members:
                if member != zodiac:
                    group.internal_misses[member] += 1
            
            group.last_updated_period = period
        
        processed_count += 1
        
        # 显示进度
        if processed_count % 100 == 0:
            progress = processed_count / len(sorted_data) * 100
            print(f"   进度: {progress:.1f}% ({processed_count}/{len(sorted_data)})")
    
    # 更新最后处理期号
    engine.last_processed_period = sorted_data[-1]['period'] if sorted_data else ""
    
    processing_time = time.time() - start_time
    print(f"✅ 数据处理完成:")
    print(f"   处理期数: {processed_count}")
    print(f"   错误期数: {error_count}")
    print(f"   处理时间: {processing_time:.2f} 秒")
    print(f"   最后期号: {engine.last_processed_period}")
    
    # 5. 重新计算统计指标
    print("📊 重新计算统计指标...")
    engine._recalculate_statistics()
    
    # 6. 验证结果
    print("🔍 验证处理结果...")
    
    groups_with_history = 0
    groups_with_zscore = 0
    max_zscore = 0
    total_hits = 0
    
    for group in engine.all_groups.values():
        if len(group.miss_history) > 0:
            groups_with_history += 1
            total_hits += group.hit_count
        
        if group.z_score > 0:
            groups_with_zscore += 1
            max_zscore = max(max_zscore, group.z_score)
    
    print(f"   有历史数据的小组: {groups_with_history:,} / {len(engine.all_groups):,}")
    print(f"   有Z-Score的小组: {groups_with_zscore:,}")
    print(f"   最大Z-Score: {max_zscore:.3f}")
    print(f"   总命中次数: {total_hits:,}")
    
    # 7. 查找候选小组
    print("🎯 查找候选小组...")
    candidates = engine.find_candidates_fast(1.0)
    print(f"   阈值1.0的候选: {len(candidates)}")
    
    candidates_2 = engine.find_candidates_fast(2.0)
    print(f"   阈值2.0的候选: {len(candidates_2)}")
    
    if candidates:
        print(f"   前5个候选 (阈值1.0):")
        for i, candidate in enumerate(candidates[:5], 1):
            members_str = ", ".join(candidate['members'])
            print(f"     {i}. {members_str} - Z-Score: {candidate['z_score']:.3f}")
    
    # 8. 保存缓存
    print("💾 保存缓存...")
    engine.save_cache()
    
    # 9. 测试预测引擎集成
    print("🧠 测试预测引擎集成...")
    pred_engine.advanced_zodiac_engine = engine
    
    analysis_result = pred_engine.get_advanced_zodiac_analysis()
    
    if 'error' in analysis_result:
        print(f"❌ 集成测试失败: {analysis_result['error']}")
    else:
        candidates_count = len(analysis_result.get('candidates', []))
        recommendations_count = len(analysis_result.get('top_recommendations', []))
        energy_count = len(analysis_result.get('energy_analysis', {}))
        
        print(f"✅ 集成测试成功:")
        print(f"   候选小组: {candidates_count}")
        print(f"   推荐号码: {recommendations_count}")
        print(f"   能量分析: {energy_count} 个生肖")
        
        # 显示推荐号码
        if analysis_result.get('top_recommendations'):
            numbers_str = ', '.join(f"{n:02d}" for n in analysis_result['top_recommendations'])
            print(f"   推荐号码: {numbers_str}")
    
    print(f"\n🎉 完全重建完成!")
    return engine

if __name__ == "__main__":
    complete_rebuild()
