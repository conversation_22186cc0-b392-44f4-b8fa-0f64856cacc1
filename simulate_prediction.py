#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟预测运行 - 直接执行main.py的逻辑
"""

import csv
import json
import sqlite3
import os
from datetime import datetime

def simulate_main_execution():
    """模拟main.py的执行过程"""
    print("🚀 开始模拟预测系统执行...")
    print("=" * 50)
    
    try:
        # 1. 检查并导入模块
        print("📦 导入核心模块...")
        from combo_generator import ComboGenerator
        from extreme_stat_tracker import ExtremeStatTracker
        from strategy_scorer import StrategyScorerAndFusionEngine
        print("✅ 所有模块导入成功")
        
        # 2. 生成组合
        print("\n🎲 生成预测组合...")
        combo_gen = ComboGenerator()
        shengxiao_4_combos = combo_gen.generate_shengxiao_4()
        wuxing_2_combos = combo_gen.generate_wuxing_2()
        all_combos = shengxiao_4_combos + wuxing_2_combos
        
        print(f"✅ 组合生成完成:")
        print(f"   - 生肖4组合: {len(shengxiao_4_combos)} 个")
        print(f"   - 五行2组合: {len(wuxing_2_combos)} 个")
        print(f"   - 总计: {len(all_combos)} 个")
        
        # 3. 加载历史数据 (模拟main.py中的load_historical_data函数)
        print("\n📊 加载历史数据...")
        history = []
        
        try:
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 处理BOM字符
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'

                    history.append({
                        'period': row[period_key],
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
        except FileNotFoundError:
            print("❌ CSV文件未找到")
            return False
        
        # 反向排序 (最新在前)
        history_draws = list(reversed(history))
        print(f"✅ 历史数据加载成功: {len(history_draws)} 条记录")
        print(f"   数据范围: {history[0]['period']} 到 {history[-1]['period']}")
        
        # 4. 运行统计追踪
        print("\n📈 运行统计追踪...")
        print("   (为节省时间，只处理前20个组合)")
        
        tracker = ExtremeStatTracker(
            db_connection=None, 
            combo_list=all_combos[:20],  # 只处理前20个组合
            history_draws=history_draws
        )
        tracker.run_tracking()
        print("✅ 统计追踪完成")
        
        # 5. 定义策略 (模拟main.py中的策略定义)
        print("\n🎯 定义预测策略...")
        strategies = {
            "shengxiao_4_extreme": {
                "hit_count": 5,
                "avg_omit_before_hit": 8,
                "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
            },
            "wuxing_2_hot": {
                "hit_count": 10,
                "avg_omit_before_hit": 4,
                "predicted_numbers": [5, 10, 15, 20, 25, 30, 35]
            }
        }
        print(f"✅ 策略定义完成: {len(strategies)} 个策略")
        
        # 6. 策略评分
        print("\n⚖️ 计算策略评分...")
        scorer = StrategyScorerAndFusionEngine(strategies, history_draws)
        strategy_scores = scorer.calculate_scores()
        
        print("✅ 策略评分完成:")
        for strat_id, score_data in strategy_scores.items():
            print(f"   - {strat_id}:")
            print(f"     评分: {score_data['score']:.4f}")
            print(f"     命中率: {score_data['hit_rate']:.4f}")
            print(f"     平均遗漏: {score_data['avg_omit_before_hit']:.1f}")
        
        # 7. 预测融合
        print("\n🔮 执行预测融合...")
        predictions_to_fuse = [
            {
                "strategy_id": strat_id,
                "weight": strategy_scores[strat_id]["score"],
                "numbers": strat_data["predicted_numbers"]
            }
            for strat_id, strat_data in strategies.items()
        ]
        
        final_prediction = scorer.fuse_predictions(
            predictions_to_fuse, 
            method='weighted_union', 
            top_n=12
        )
        
        print("✅ 预测融合完成")
        print(f"🎯 最终预测号码 (Top 12): {sorted(final_prediction)}")
        
        # 8. 保存结果
        result = {
            "timestamp": datetime.now().isoformat(),
            "combos_generated": len(all_combos),
            "history_records": len(history_draws),
            "strategies": list(strategies.keys()),
            "strategy_scores": strategy_scores,
            "final_prediction": sorted(final_prediction)
        }
        
        with open('simulation_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: simulation_result.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print("\n🗄️ 检查数据库连接...")
    
    try:
        if not os.path.exists('lottery_data.db'):
            print("❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect('lottery_data.db')
        cursor = conn.cursor()
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✅ 数据库连接成功，发现 {len(tables)} 个表")
        
        # 检查主要数据
        if 'lottery_records' in tables:
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            count = cursor.fetchone()[0]
            print(f"📊 lottery_records 表: {count} 条记录")
            
            if count > 0:
                cursor.execute("""
                    SELECT period_number, draw_date, zodiac, five_element 
                    FROM lottery_records 
                    ORDER BY period_number DESC 
                    LIMIT 3
                """)
                recent = cursor.fetchall()
                print("📅 最近3条数据库记录:")
                for record in recent:
                    print(f"   期号:{record[0]} 日期:{record[1]} 生肖:{record[2]} 五行:{record[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 六合彩预测系统后台监测")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 检查数据库连接
    db_status = check_database_connection()
    
    # 2. 模拟预测执行
    prediction_status = simulate_main_execution()
    
    # 3. 总结
    print("\n📋 监测总结")
    print("=" * 30)
    print(f"🗄️ 数据库状态: {'✅ 正常' if db_status else '❌ 异常'}")
    print(f"🎯 预测系统: {'✅ 正常' if prediction_status else '❌ 异常'}")
    
    if prediction_status:
        print("\n🎉 系统运行完全正常！")
        print("✅ 数据连接正常")
        print("✅ 预测流程正常")
        print("✅ 结果生成正常")
    else:
        print("\n⚠️ 系统存在问题，需要检查")
    
    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return prediction_status

if __name__ == "__main__":
    main()
