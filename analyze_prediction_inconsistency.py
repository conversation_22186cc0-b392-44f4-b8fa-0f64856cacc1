#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析预测结果不一致的原因
"""

from wuxing_2_hot_optimizer import WuXing2HotOptimizer
import random

def analyze_randomness_sources():
    """分析随机性来源"""
    print("🔍 分析预测结果不一致的原因")
    print("="*60)
    
    # 1. 检查优化器中的随机性
    print("📊 1. 检查优化器中的随机性...")
    
    optimizer = WuXing2HotOptimizer()
    
    # 多次运行相同分析
    results = []
    for i in range(5):
        print(f"   第{i+1}次分析...")
        result = optimizer.analyze_wuxing_combinations("2025199")
        results.append({
            'predicted_numbers': result['predicted_numbers'],
            'hot_combos_count': len(result['hot_combos']),
            'analysis_summary': result['analysis_summary']
        })
        print(f"     预测号码: {result['predicted_numbers']}")
    
    # 分析结果差异
    print(f"\n📋 结果差异分析:")
    
    all_numbers = set()
    for result in results:
        all_numbers.update(result['predicted_numbers'])
    
    print(f"   所有出现的号码: {sorted(all_numbers)} (共{len(all_numbers)}个)")
    
    # 检查是否完全相同
    first_result = results[0]['predicted_numbers']
    all_same = all(r['predicted_numbers'] == first_result for r in results)
    
    if all_same:
        print(f"   ✅ 所有预测结果完全相同")
    else:
        print(f"   ❌ 预测结果存在差异")
        
        # 分析差异程度
        for i, result in enumerate(results, 1):
            common = set(first_result) & set(result['predicted_numbers'])
            similarity = len(common) / len(first_result) * 100
            print(f"     第{i}次与第1次相似度: {similarity:.1f}%")

def find_randomness_in_code():
    """查找代码中的随机性"""
    print(f"\n🔍 查找代码中的随机性来源")
    print("="*60)
    
    import inspect
    from wuxing_2_hot_optimizer import WuXing2HotOptimizer
    
    # 获取优化器的源码
    source = inspect.getsource(WuXing2HotOptimizer)
    
    # 查找随机性相关的代码
    random_keywords = ['random', 'shuffle', 'choice', 'sample', 'randint', 'uniform']
    
    print("🔍 搜索随机性关键词:")
    lines = source.split('\n')
    
    found_random = False
    for i, line in enumerate(lines, 1):
        for keyword in random_keywords:
            if keyword in line.lower() and not line.strip().startswith('#'):
                print(f"   第{i}行: {line.strip()}")
                found_random = True
    
    if not found_random:
        print("   ❌ 未找到明显的随机性代码")
    else:
        print("   ✅ 找到随机性代码")

def test_specific_methods():
    """测试具体方法的一致性"""
    print(f"\n🧪 测试具体方法的一致性")
    print("="*60)
    
    optimizer = WuXing2HotOptimizer()
    
    # 1. 测试五行组合生成
    print("📊 1. 测试五行组合生成...")
    combos1 = optimizer.combo_generator.generate_wuxing_2()
    combos2 = optimizer.combo_generator.generate_wuxing_2()
    
    if combos1 == combos2:
        print("   ✅ 五行组合生成一致")
    else:
        print("   ❌ 五行组合生成不一致")
    
    # 2. 测试数据加载
    print("📊 2. 测试数据加载...")
    data1 = optimizer.data_manager.load_data()
    data2 = optimizer.data_manager.load_data()
    
    if data1 == data2:
        print("   ✅ 数据加载一致")
    else:
        print("   ❌ 数据加载不一致")
    
    # 3. 测试号码生成方法
    print("📊 3. 测试号码生成方法...")
    
    # 模拟相同的热门组合
    mock_hot_combos = [
        ('五行2组合 - 金+木', {'hot_score': 0.8, 'hit_rate': 0.6}),
        ('五行2组合 - 水+火', {'hot_score': 0.7, 'hit_rate': 0.5})
    ]
    
    numbers1 = optimizer._generate_numbers_from_hot_combos(mock_hot_combos, "2025199")
    numbers2 = optimizer._generate_numbers_from_hot_combos(mock_hot_combos, "2025199")
    
    print(f"   第1次生成: {numbers1}")
    print(f"   第2次生成: {numbers2}")
    
    if numbers1 == numbers2:
        print("   ✅ 号码生成一致")
    else:
        print("   ❌ 号码生成不一致 (这里是随机性的主要来源)")

def analyze_random_seed():
    """分析随机种子问题"""
    print(f"\n🎲 分析随机种子问题")
    print("="*60)
    
    print("📊 测试设置随机种子的效果...")
    
    # 测试1: 不设置种子
    print("   不设置种子:")
    for i in range(3):
        numbers = random.sample(range(1, 50), 8)
        print(f"     第{i+1}次: {sorted(numbers)}")
    
    # 测试2: 设置固定种子
    print("   设置固定种子:")
    for i in range(3):
        random.seed(42)  # 固定种子
        numbers = random.sample(range(1, 50), 8)
        print(f"     第{i+1}次: {sorted(numbers)}")

def test_deterministic_version():
    """测试确定性版本"""
    print(f"\n🔧 测试确定性版本")
    print("="*60)
    
    print("📊 创建确定性优化器...")
    
    # 临时修改优化器，移除随机性
    class DeterministicOptimizer(WuXing2HotOptimizer):
        def _get_numbers_for_wuxing(self, wuxing: str, period_number: str):
            """确定性的五行号码获取"""
            wuxing_numbers = {
                '金': [2, 3, 10, 11],  # 固定选择前4个
                '木': [1, 8, 9, 16],
                '水': [6, 7, 14, 15],
                '火': [4, 5, 12, 13],
                '土': [49, 48, 47, 46]
            }
            return wuxing_numbers.get(wuxing, [1, 2, 3, 4])
        
        def _generate_balanced_numbers(self):
            """确定性的平衡号码生成"""
            return [1, 2, 25, 26, 3, 4, 27, 28]  # 固定组合
        
        def _generate_supplement_numbers(self, existing):
            """确定性的补充号码"""
            all_numbers = list(range(1, 50))
            available = [n for n in all_numbers if n not in existing]
            return available[:2] if available else []
    
    # 测试确定性版本
    det_optimizer = DeterministicOptimizer()
    
    print("   测试确定性预测...")
    for i in range(3):
        result = det_optimizer.analyze_wuxing_combinations("2025199")
        print(f"     第{i+1}次: {result['predicted_numbers']}")

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案")
    print("="*60)
    
    solutions = """
🎯 预测结果不一致的原因:

1. 🎲 随机性来源:
   ❌ random.sample() - 从五行号码中随机选择
   ❌ random.choice() - 随机选择补充号码
   ❌ 无固定种子 - 每次运行都产生不同随机数

2. 🔧 解决方案选择:

   方案A: 完全确定性 (推荐)
   ✅ 移除所有随机性
   ✅ 基于固定规则选择号码
   ✅ 每次预测结果完全一致
   ✅ 保持算法的科学性

   方案B: 可控随机性
   ✅ 设置固定随机种子
   ✅ 基于期号生成种子
   ✅ 相同期号结果一致
   ✅ 不同期号有所变化

   方案C: 混合模式
   ✅ 核心逻辑确定性
   ✅ 细节选择有随机性
   ✅ 平衡一致性和多样性

3. 🎯 推荐实施:
   建议采用方案A (完全确定性)
   - 预测系统应该稳定可靠
   - 相同输入产生相同输出
   - 便于验证和调试
   - 用户体验更好
"""
    
    print(solutions)

def main():
    """主分析流程"""
    print("🔍 预测结果不一致原因分析")
    print("="*70)
    
    # 分析随机性来源
    analyze_randomness_sources()
    
    # 查找代码中的随机性
    find_randomness_in_code()
    
    # 测试具体方法
    test_specific_methods()
    
    # 分析随机种子
    analyze_random_seed()
    
    # 测试确定性版本
    test_deterministic_version()
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n🎯 分析结论:")
    print("预测结果不一致主要由于代码中使用了random.sample()等随机函数")
    print("建议修改为确定性算法，确保相同输入产生相同输出")

if __name__ == "__main__":
    main()
