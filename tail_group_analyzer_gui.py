import tkinter as tk
from tkinter import ttk, messagebox
from tail_group_filter import TailGroupFilter
import sqlite3
from typing import List, Dict
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import seaborn as sns
from pathlib import Path

class TailGroupAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("尾数组合分析系统")
        self.root.geometry("1200x800")

        # 初始化分析器
        self.filter = TailGroupFilter()

        # 创建主框架和标签页
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建各个标签页
        self.overview_tab = self._create_overview_tab()
        self.analysis_tab = self._create_analysis_tab()
        self.prediction_tab = self._create_prediction_tab()
        self.charts_tab = self._create_charts_tab()

        # 添加标签页到notebook
        self.notebook.add(self.overview_tab, text="组合概览")
        self.notebook.add(self.analysis_tab, text="统计分析")
        self.notebook.add(self.prediction_tab, text="预测系统")
        self.notebook.add(self.charts_tab, text="图表分析")

        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self._on_tab_changed)

        # 初始化数据
        self._load_initial_data()

    def _create_overview_tab(self) -> ttk.Frame:
        """创建组合概览标签页"""
        frame = ttk.Frame(self.notebook)

        # 创建组合类型过滤器
        filter_frame = ttk.LabelFrame(frame, text="组合类型筛选")
        filter_frame.pack(fill='x', padx=5, pady=5)

        self.type_vars = {}
        for group_type in ["basic", "parity", "size", "adjacent", "span"]:
            var = tk.BooleanVar(value=True)
            self.type_vars[group_type] = var
            cb = ttk.Checkbutton(filter_frame, text=self._get_type_label(group_type),
                               variable=var, command=self._filter_groups)
            cb.pack(side='left', padx=5)

        # 创建组合列表
        list_frame = ttk.LabelFrame(frame, text="组合列表")
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建表格
        columns = ("标签", "尾数", "类型", "匹配数量", "命中率")
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        return frame

    def _create_analysis_tab(self) -> ttk.Frame:
        """创建统计分析标签页"""
        frame = ttk.Frame(self.notebook)

        # 创建左侧组合选择面板
        left_frame = ttk.LabelFrame(frame, text="选择组合")
        left_frame.pack(side='left', fill='y', padx=5, pady=5)

        self.group_listbox = tk.Listbox(left_frame, width=30)
        self.group_listbox.pack(fill='both', expand=True)
        self.group_listbox.bind('<<ListboxSelect>>', self._on_group_selected)

        # 创建右侧统计信息面板
        right_frame = ttk.LabelFrame(frame, text="统计信息")
        right_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        # 创建统计信息显示区域
        self.stats_text = tk.Text(right_frame, wrap=tk.WORD, width=50)
        self.stats_text.pack(fill='both', expand=True)

        return frame

    def _create_prediction_tab(self) -> ttk.Frame:
        """创建预测系统标签页"""
        frame = ttk.Frame(self.notebook)

        # 创建预测设置面板
        settings_frame = ttk.LabelFrame(frame, text="预测设置")
        settings_frame.pack(fill='x', padx=5, pady=5)

        # 添加预测参数设置
        ttk.Label(settings_frame, text="分析期数:").pack(side='left', padx=5)
        self.period_entry = ttk.Entry(settings_frame, width=10)
        self.period_entry.insert(0, "30")
        self.period_entry.pack(side='left', padx=5)

        ttk.Label(settings_frame, text="热门组合数:").pack(side='left', padx=5)
        self.top_n_entry = ttk.Entry(settings_frame, width=10)
        self.top_n_entry.insert(0, "5")
        self.top_n_entry.pack(side='left', padx=5)

        ttk.Button(settings_frame, text="生成预测",
                  command=self._generate_prediction).pack(side='left', padx=5)

        # 创建预测结果显示面板
        results_frame = ttk.LabelFrame(frame, text="预测结果")
        results_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 添加预测结果表格
        columns = ("组合", "匹配号码", "热度指数", "稳定性", "趋势")
        self.prediction_tree = ttk.Treeview(results_frame, columns=columns, show='headings')

        for col in columns:
            self.prediction_tree.heading(col, text=col)
            self.prediction_tree.column(col, width=100)

        self.prediction_tree.pack(fill='both', expand=True)

        return frame

    def _create_charts_tab(self) -> ttk.Frame:
        """创建图表分析标签页"""
        frame = ttk.Frame(self.notebook)

        # 创建图表类型选择面板
        controls_frame = ttk.LabelFrame(frame, text="图表控制")
        controls_frame.pack(fill='x', padx=5, pady=5)

        # 添加图表类型选择
        ttk.Label(controls_frame, text="图表类型:").pack(side='left', padx=5)
        self.chart_type = ttk.Combobox(controls_frame, values=[
            "命中率分布",
            "热度趋势",
            "相关性矩阵",
            "稳定性对比"
        ])
        self.chart_type.set("命中率分布")
        self.chart_type.pack(side='left', padx=5)

        ttk.Button(controls_frame, text="更新图表",
                  command=self._update_chart).pack(side='left', padx=5)

        # 创建图表显示区域
        self.chart_frame = ttk.LabelFrame(frame, text="图表显示")
        self.chart_frame.pack(fill='both', expand=True, padx=5, pady=5)

        return frame

    def _load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载历史数据
            self.history_data = self._load_history_data()

            # 更新组合列表
            self._update_group_list()

            # 更新统计信息
            self._update_stats()

        except Exception as e:
            messagebox.showerror("错误", f"加载数据时出错: {str(e)}")

    def _load_history_data(self) -> List[int]:
        """从数据库加载历史数据"""
        # 这里应该从您的数据库中读取实际的历史数据
        # 现在使用示例数据
        return list(range(1, 50))  # 示例数据

    def _update_group_list(self):
        """更新组合列表显示"""
        self.tree.delete(*self.tree.get_children())
        self.group_listbox.delete(0, tk.END)

        for group in self.filter.groups:
            # 检查组合类型是否被选中
            if self.type_vars.get(group.get('type', 'basic'), tk.BooleanVar(value=True)).get():
                # 更新树形视图
                self.tree.insert('', 'end', values=(
                    group['label'],
                    ','.join(map(str, group['tails'])),
                    group.get('type', 'basic'),
                    len(self.filter.get_numbers_by_tail(group['tails'])),
                    f"{self._get_hit_rate(group['label']):.2%}"
                ))

                # 更新列表框
                self.group_listbox.insert(tk.END, group['label'])

    def _get_hit_rate(self, group_label: str) -> float:
        """获取组合的命中率"""
        stats = self.filter.analyze_history(self.history_data)
        return stats.get(group_label, {}).get('hit_rate', 0)

    def _on_group_selected(self, event):
        """当选择组合时更新统计信息"""
        selection = self.group_listbox.curselection()
        if selection:
            group_label = self.group_listbox.get(selection[0])
            stats = self.filter.analyze_history(self.history_data)
            group_stats = stats.get(group_label, {})

            # 更新统计信息显示
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, f"组合: {group_label}\n\n")
            self.stats_text.insert(tk.END, f"命中统计:\n")
            self.stats_text.insert(tk.END, f"- 命中次数: {group_stats.get('hits', 0)}\n")
            self.stats_text.insert(tk.END, f"- 命中率: {group_stats.get('hit_rate', 0):.2%}\n")
            self.stats_text.insert(tk.END, f"- 最近命中: {group_stats.get('last_n_hits', 0)}次\n\n")

            self.stats_text.insert(tk.END, f"遗漏分析:\n")
            self.stats_text.insert(tk.END, f"- 当前遗漏: {group_stats.get('miss_count', 0)}\n")
            self.stats_text.insert(tk.END, f"- 最大遗漏: {group_stats.get('max_miss', 0)}\n")
            self.stats_text.insert(tk.END, f"- 平均遗漏: {group_stats.get('avg_miss', 0):.2f}\n\n")

            self.stats_text.insert(tk.END, f"走势分析:\n")
            self.stats_text.insert(tk.END, f"- 连续命中: {group_stats.get('consecutive_hits', 0)}\n")
            self.stats_text.insert(tk.END, f"- 最大连续: {group_stats.get('hit_streak', 0)}\n")
            self.stats_text.insert(tk.END, f"- 热温比: {group_stats.get('hot_cold_ratio', 0):.2f}\n")
            self.stats_text.insert(tk.END, f"- 走势指标: {group_stats.get('trend_indicator', 0):.2f}\n\n")

            self.stats_text.insert(tk.END, f"综合评分:\n")
            self.stats_text.insert(tk.END, f"- 稳定性: {group_stats.get('stability_score', 0):.2f}\n")
            self.stats_text.insert(tk.END, f"- 关联度: {group_stats.get('correlation_score', 0):.2f}\n")

    def _generate_prediction(self):
        """生成预测结果"""
        try:
            periods = int(self.period_entry.get())
            top_n = int(self.top_n_entry.get())

            # 获取热门组合
            hot_groups = self.filter.get_hot_groups(self.history_data[-periods:], top_n)

            # 清空预测结果表格
            self.prediction_tree.delete(*self.prediction_tree.get_children())

            # 更新预测结果
            for group_label, stats in hot_groups:
                matched_numbers = self.filter.get_numbers_by_tail(
                    next(g['tails'] for g in self.filter.groups if g['label'] == group_label)
                )

                self.prediction_tree.insert('', 'end', values=(
                    group_label,
                    ','.join(map(str, sorted(matched_numbers))),
                    f"{stats['hot_cold_ratio']:.2f}",
                    f"{stats['stability_score']:.2f}",
                    f"{stats['trend_indicator']:.2f}"
                ))

        except ValueError as e:
            messagebox.showerror("错误", "请输入有效的数字")

    def _update_chart(self):
        """更新图表显示"""
        # 清空当前图表
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        # 创建新图表
        fig, ax = plt.subplots(figsize=(10, 6))

        if self.chart_type.get() == "命中率分布":
            self._plot_hit_rate_distribution(ax)
        elif self.chart_type.get() == "热度趋势":
            self._plot_hot_trend(ax)
        elif self.chart_type.get() == "相关性矩阵":
            self._plot_correlation_matrix(ax)
        elif self.chart_type.get() == "稳定性对比":
            self._plot_stability_comparison(ax)

        # 添加图表到界面
        canvas = FigureCanvasTkAgg(fig, master=self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)

    def _plot_hit_rate_distribution(self, ax):
        """绘制命中率分布图"""
        stats = self.filter.analyze_history(self.history_data)
        hit_rates = [s['hit_rate'] for s in stats.values()]

        sns.histplot(hit_rates, ax=ax, bins=20)
        ax.set_title("组合命中率分布")
        ax.set_xlabel("命中率")
        ax.set_ylabel("组合数量")

    def _plot_hot_trend(self, ax):
        """绘制热度趋势图"""
        stats = self.filter.analyze_history(self.history_data)
        top_groups = sorted(stats.items(), key=lambda x: x[1]['hot_cold_ratio'], reverse=True)[:5]

        for label, stat in top_groups:
            ax.plot([stat['hits'], stat['last_n_hits']], label=label)

        ax.set_title("热门组合趋势")
        ax.set_xlabel("时期")
        ax.set_ylabel("命中次数")
        ax.legend()

    def _plot_correlation_matrix(self, ax):
        """绘制相关性矩阵"""
        stats = self.filter.analyze_history(self.history_data)
        correlation_data = []

        for group in self.filter.groups:
            correlations = []
            for other_group in self.filter.groups:
                if group != other_group:
                    matched = self.filter.get_numbers_by_tail(group['tails'])
                    other_matched = self.filter.get_numbers_by_tail(other_group['tails'])
                    correlation = len(matched & other_matched) / len(matched | other_matched)
                    correlations.append(correlation)

            correlation_data.append(correlations)

        sns.heatmap(correlation_data, ax=ax, cmap='coolwarm')
        ax.set_title("组合间相关性矩阵")

    def _plot_stability_comparison(self, ax):
        """绘制稳定性对比图"""
        stats = self.filter.analyze_history(self.history_data)
        groups = sorted(stats.items(), key=lambda x: x[1]['stability_score'], reverse=True)[:10]

        labels = [g[0] for g in groups]
        scores = [g[1]['stability_score'] for g in groups]

        ax.bar(labels, scores)
        ax.set_title("组合稳定性对比")
        ax.set_xlabel("组合")
        ax.set_ylabel("稳定性得分")
        plt.xticks(rotation=45)

    def _filter_groups(self):
        """根据选中的组合类型筛选显示"""
        self._update_group_list()

    def _on_tab_changed(self, event):
        """标签页切换事件处理"""
        current_tab = self.notebook.select()
        current_tab_name = self.notebook.tab(current_tab, "text")

        if current_tab_name == "图表分析":
            self._update_chart()

    @staticmethod
    def _get_type_label(group_type: str) -> str:
        """获取组合类型的中文标签"""
        type_labels = {
            "basic": "基础组合",
            "parity": "奇偶组合",
            "size": "大小组合",
            "adjacent": "邻位组合",
            "span": "跨度组合"
        }
        return type_labels.get(group_type, group_type)

def main():
    root = tk.Tk()
    app = TailGroupAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
