#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测验证系统 - 验证预测引擎的真实可信度
通过历史数据回测来验证预测准确性
"""

import csv
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import matplotlib.pyplot as plt
import pandas as pd

class BacktestVerification:
    """回测验证系统"""
    
    def __init__(self):
        self.history_data = []
        self.backtest_results = []
        self.load_data()
    
    def load_data(self):
        """加载历史数据"""
        try:
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    self.history_data.append({
                        'period': row[period_key],
                        'date': row['draw_date'],
                        'special_code': int(row['special_code']),
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
            
            # 按期号排序，最旧的在前
            self.history_data.sort(key=lambda x: int(x['period']))
            print(f"✅ 加载历史数据: {len(self.history_data)} 条记录")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            self.history_data = []
    
    def simulate_zodiac_prediction(self, train_data: List[Dict], periods: int = 50) -> List[int]:
        """模拟生肖预测策略"""
        zodiac_mapping = {
            '鼠': [6, 18, 30, 42], '牛': [5, 17, 29, 41], '虎': [4, 16, 28, 40],
            '兔': [3, 15, 27, 39], '龙': [2, 14, 26, 38], '蛇': [1, 13, 25, 37, 49],
            '马': [12, 24, 36, 48], '羊': [11, 23, 35, 47], '猴': [10, 22, 34, 46],
            '鸡': [9, 21, 33, 45], '狗': [8, 20, 32, 44], '猪': [7, 19, 31, 43]
        }
        
        if len(train_data) < periods:
            periods = len(train_data)
        
        recent_data = train_data[-periods:]
        zodiac_stats = defaultdict(list)
        
        for i, record in enumerate(recent_data):
            zodiac = record['zodiac']
            zodiac_stats[zodiac].append(periods - i - 1)  # 距离现在的期数
        
        # 选择最久未出现的生肖
        zodiac_scores = []
        for zodiac, positions in zodiac_stats.items():
            if positions:
                current_miss = positions[0]  # 最近一次出现距离现在的期数
                avg_interval = np.mean(np.diff([0] + sorted(positions, reverse=True) + [periods]))
                score = current_miss / avg_interval if avg_interval > 0 else current_miss
            else:
                score = periods  # 从未出现
            
            zodiac_scores.append((zodiac, score))
        
        # 选择评分最高的2个生肖
        zodiac_scores.sort(key=lambda x: x[1], reverse=True)
        selected_zodiacs = zodiac_scores[:2]
        
        predicted_numbers = []
        for zodiac, _ in selected_zodiacs:
            predicted_numbers.extend(zodiac_mapping.get(zodiac, [])[:3])
        
        return predicted_numbers[:8]
    
    def simulate_wuxing_prediction(self, train_data: List[Dict], periods: int = 50) -> List[int]:
        """模拟五行预测策略"""
        wuxing_mapping = {
            '金': [4, 5, 12, 13, 20, 21, 28, 29, 36, 37, 44, 45],
            '木': [1, 2, 9, 10, 17, 18, 25, 26, 33, 34, 41, 42, 49],
            '水': [7, 8, 15, 16, 23, 24, 31, 32, 39, 40, 47, 48],
            '火': [6, 14, 22, 30, 38, 46],
            '土': [3, 11, 19, 27, 35, 43]
        }
        
        if len(train_data) < periods:
            periods = len(train_data)
        
        recent_data = train_data[-periods:]
        wuxing_stats = defaultdict(list)
        
        for i, record in enumerate(recent_data):
            wuxing = record['five_element']
            wuxing_stats[wuxing].append(periods - i - 1)
        
        # 选择最有潜力的五行
        wuxing_scores = []
        for wuxing, positions in wuxing_stats.items():
            if positions:
                current_miss = positions[0]
                frequency = len(positions) / periods
                score = current_miss * frequency
            else:
                score = periods
            
            wuxing_scores.append((wuxing, score))
        
        wuxing_scores.sort(key=lambda x: x[1], reverse=True)
        selected_wuxing = wuxing_scores[:2]
        
        predicted_numbers = []
        for wuxing, _ in selected_wuxing:
            predicted_numbers.extend(wuxing_mapping.get(wuxing, [])[:4])
        
        return predicted_numbers[:8]
    
    def simulate_number_prediction(self, train_data: List[Dict], periods: int = 50) -> List[int]:
        """模拟号码统计预测策略"""
        if len(train_data) < periods:
            periods = len(train_data)
        
        recent_data = train_data[-periods:]
        number_stats = defaultdict(list)
        
        for i, record in enumerate(recent_data):
            number = record['special_code']
            number_stats[number].append(periods - i - 1)
        
        # 分析所有号码
        number_scores = []
        for number in range(1, 50):
            positions = number_stats.get(number, [])
            if positions:
                current_miss = positions[0]
                frequency = len(positions) / periods
                # 综合评分：遗漏期数 + 频率权重
                score = current_miss + (1 - frequency) * 10
            else:
                score = periods + 10  # 从未出现的号码
            
            number_scores.append((number, score))
        
        # 选择评分最高的号码
        number_scores.sort(key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, _ in number_scores[:8]]
        
        return predicted_numbers
    
    def run_single_prediction(self, train_data: List[Dict]) -> List[int]:
        """运行单次预测"""
        # 三种策略预测
        zodiac_numbers = self.simulate_zodiac_prediction(train_data)
        wuxing_numbers = self.simulate_wuxing_prediction(train_data)
        number_numbers = self.simulate_number_prediction(train_data)
        
        # 融合预测
        all_numbers = zodiac_numbers + wuxing_numbers + number_numbers
        number_counts = defaultdict(int)
        
        for num in all_numbers:
            number_counts[num] += 1
        
        # 按出现次数排序，选择前12个
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, count in sorted_numbers[:12]]
        
        # 如果不足12个，补充高频号码
        if len(final_numbers) < 12:
            all_unique = list(set(all_numbers))
            for num in all_unique:
                if num not in final_numbers:
                    final_numbers.append(num)
                if len(final_numbers) >= 12:
                    break
        
        return final_numbers[:12]
    
    def run_backtest(self, start_index: int = 100, test_periods: int = 100) -> Dict[str, Any]:
        """运行回测"""
        print(f"🔄 开始回测验证...")
        print(f"📊 训练数据: 前{start_index}期")
        print(f"🎯 测试期数: {test_periods}期")
        
        if len(self.history_data) < start_index + test_periods:
            test_periods = len(self.history_data) - start_index
            print(f"⚠️ 调整测试期数为: {test_periods}期")
        
        results = []
        hit_counts = []
        hit_rates = []
        
        for i in range(test_periods):
            test_index = start_index + i
            if test_index >= len(self.history_data):
                break
            
            # 训练数据：从开始到当前测试期之前
            train_data = self.history_data[:test_index]
            
            # 实际结果
            actual_record = self.history_data[test_index]
            actual_number = actual_record['special_code']
            actual_period = actual_record['period']
            
            # 预测
            predicted_numbers = self.run_single_prediction(train_data)
            
            # 检查命中
            hit = actual_number in predicted_numbers
            hit_count = 1 if hit else 0
            
            result = {
                'period': actual_period,
                'predicted_numbers': predicted_numbers,
                'actual_number': actual_number,
                'hit': hit,
                'hit_count': hit_count
            }
            
            results.append(result)
            hit_counts.append(hit_count)
            
            # 计算累计命中率
            cumulative_hit_rate = sum(hit_counts) / len(hit_counts)
            hit_rates.append(cumulative_hit_rate)
            
            if (i + 1) % 20 == 0:
                print(f"  进度: {i+1}/{test_periods}, 当前命中率: {cumulative_hit_rate:.2%}")
        
        # 统计结果
        total_predictions = len(results)
        total_hits = sum(hit_counts)
        overall_hit_rate = total_hits / total_predictions if total_predictions > 0 else 0
        
        # 计算其他统计指标
        consecutive_hits = self.calculate_consecutive_hits(results)
        max_consecutive_hits = max(consecutive_hits) if consecutive_hits else 0
        max_consecutive_misses = self.calculate_max_consecutive_misses(results)
        
        backtest_summary = {
            'total_predictions': total_predictions,
            'total_hits': total_hits,
            'overall_hit_rate': overall_hit_rate,
            'max_consecutive_hits': max_consecutive_hits,
            'max_consecutive_misses': max_consecutive_misses,
            'hit_rates_over_time': hit_rates,
            'detailed_results': results
        }
        
        self.backtest_results = results
        
        print(f"\n📊 回测完成!")
        print(f"总预测次数: {total_predictions}")
        print(f"命中次数: {total_hits}")
        print(f"整体命中率: {overall_hit_rate:.2%}")
        print(f"最大连续命中: {max_consecutive_hits}")
        print(f"最大连续失误: {max_consecutive_misses}")
        
        return backtest_summary
    
    def calculate_consecutive_hits(self, results: List[Dict]) -> List[int]:
        """计算连续命中次数"""
        consecutive_counts = []
        current_count = 0
        
        for result in results:
            if result['hit']:
                current_count += 1
            else:
                if current_count > 0:
                    consecutive_counts.append(current_count)
                current_count = 0
        
        if current_count > 0:
            consecutive_counts.append(current_count)
        
        return consecutive_counts
    
    def calculate_max_consecutive_misses(self, results: List[Dict]) -> int:
        """计算最大连续失误次数"""
        max_misses = 0
        current_misses = 0
        
        for result in results:
            if not result['hit']:
                current_misses += 1
                max_misses = max(max_misses, current_misses)
            else:
                current_misses = 0
        
        return max_misses
    
    def analyze_prediction_quality(self, results: List[Dict]) -> Dict[str, Any]:
        """分析预测质量"""
        if not results:
            return {}
        
        # 按期数分组分析
        period_analysis = {}
        recent_50 = results[-50:] if len(results) >= 50 else results
        recent_20 = results[-20:] if len(results) >= 20 else results
        
        period_analysis['recent_50_hit_rate'] = sum(r['hit'] for r in recent_50) / len(recent_50)
        period_analysis['recent_20_hit_rate'] = sum(r['hit'] for r in recent_20) / len(recent_20)
        
        # 预测号码分布分析
        all_predicted = []
        for result in results:
            all_predicted.extend(result['predicted_numbers'])
        
        from collections import Counter
        number_frequency = Counter(all_predicted)
        most_predicted = number_frequency.most_common(10)
        
        # 实际命中号码分析
        hit_numbers = [r['actual_number'] for r in results if r['hit']]
        hit_number_frequency = Counter(hit_numbers)
        
        quality_analysis = {
            'period_analysis': period_analysis,
            'most_predicted_numbers': most_predicted,
            'hit_number_frequency': dict(hit_number_frequency),
            'prediction_diversity': len(set(all_predicted)),
            'avg_predicted_per_round': len(all_predicted) / len(results)
        }
        
        return quality_analysis
    
    def generate_backtest_report(self, backtest_summary: Dict[str, Any]) -> str:
        """生成回测报告"""
        quality_analysis = self.analyze_prediction_quality(backtest_summary['detailed_results'])
        
        report = f"""
# 六合彩预测系统回测验证报告

## 📊 回测概况
- **测试期数**: {backtest_summary['total_predictions']} 期
- **命中次数**: {backtest_summary['total_hits']} 次
- **整体命中率**: {backtest_summary['overall_hit_rate']:.2%}
- **最大连续命中**: {backtest_summary['max_consecutive_hits']} 次
- **最大连续失误**: {backtest_summary['max_consecutive_misses']} 次

## 🎯 预测质量分析
- **近50期命中率**: {quality_analysis.get('period_analysis', {}).get('recent_50_hit_rate', 0):.2%}
- **近20期命中率**: {quality_analysis.get('period_analysis', {}).get('recent_20_hit_rate', 0):.2%}
- **预测号码多样性**: {quality_analysis.get('prediction_diversity', 0)} 个不同号码
- **平均每轮预测**: {quality_analysis.get('avg_predicted_per_round', 0):.1f} 个号码

## 📈 可信度评估
"""
        
        hit_rate = backtest_summary['overall_hit_rate']
        if hit_rate >= 0.15:
            credibility = "高"
            assessment = "预测系统表现优秀，具有较高的实用价值"
        elif hit_rate >= 0.10:
            credibility = "中等"
            assessment = "预测系统表现良好，有一定的参考价值"
        elif hit_rate >= 0.05:
            credibility = "较低"
            assessment = "预测系统表现一般，需要进一步优化"
        else:
            credibility = "低"
            assessment = "预测系统表现较差，建议重新设计算法"
        
        report += f"""
- **可信度等级**: {credibility}
- **评估结论**: {assessment}
- **理论基准**: 随机预测12个号码的命中率约为24.5%
- **实际表现**: {'优于' if hit_rate > 0.245 else '低于'}理论基准

## ⚠️ 重要说明
1. 本回测基于历史数据，不能保证未来预测的准确性
2. 彩票具有随机性，任何预测都存在不确定性
3. 回测结果仅供算法评估，不构成投注建议
4. 请理性对待预测结果，合理控制风险

## 📋 详细数据
回测详细数据已保存到 backtest_detailed_results.json
        """
        
        return report
    
    def save_results(self, backtest_summary: Dict[str, Any]):
        """保存回测结果"""
        # 保存详细结果
        with open('backtest_detailed_results.json', 'w', encoding='utf-8') as f:
            json.dump(backtest_summary, f, ensure_ascii=False, indent=2)
        
        # 生成并保存报告
        report = self.generate_backtest_report(backtest_summary)
        with open('backtest_verification_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n💾 回测结果已保存:")
        print(f"   - 详细数据: backtest_detailed_results.json")
        print(f"   - 分析报告: backtest_verification_report.md")


def main():
    """主函数"""
    print("🔧 启动回测验证系统...")
    
    verifier = BacktestVerification()
    
    if not verifier.history_data:
        print("❌ 无法加载历史数据，退出验证")
        return
    
    # 运行回测
    backtest_summary = verifier.run_backtest(start_index=200, test_periods=100)
    
    # 保存结果
    verifier.save_results(backtest_summary)
    
    # 输出结论
    hit_rate = backtest_summary['overall_hit_rate']
    print(f"\n🎯 验证结论:")
    
    if hit_rate >= 0.15:
        print(f"✅ 预测系统可信度: 高")
        print(f"📈 命中率 {hit_rate:.2%} 表现优秀")
    elif hit_rate >= 0.10:
        print(f"✅ 预测系统可信度: 中等")
        print(f"📊 命中率 {hit_rate:.2%} 表现良好")
    elif hit_rate >= 0.05:
        print(f"⚠️ 预测系统可信度: 较低")
        print(f"📉 命中率 {hit_rate:.2%} 需要优化")
    else:
        print(f"❌ 预测系统可信度: 低")
        print(f"📉 命中率 {hit_rate:.2%} 表现较差")
    
    print(f"\n💡 参考基准: 随机预测12个号码的理论命中率约为24.5%")
    print(f"🔍 实际表现: {'优于' if hit_rate > 0.245 else '低于'}理论基准")


if __name__ == "__main__":
    main()
