 一、数据库表结构设计（主表 + 维度表）
1. lottery_draws（开奖记录主表）
字段名	类型	说明
id	INT	主键
draw_date	DATE	开奖日期
year	INT	年份
draw_number	VARCHAR	开奖期号
special_number	INT	特码
all_numbers	VARCHAR	全部开奖号码（逗号分隔）
zodiac	VARCHAR	特码生肖
element	VARCHAR	特码五行
color_wave	VARCHAR	色波
metadata	JSON	扩展数据（如：生肖组合、五行组合等）

2. zodiac_map（生肖与号码映射表）
字段名	类型	说明
year	INT	公历年（用于春节前后切换）
zodiac	VARCHAR	生肖
numbers	TEXT	该年对应的号码（列表，如：08,20,32...）
position_type	VARCHAR	日肖/夜肖/左肖/右肖等多维属性
gender_type	VARCHAR	男肖/女肖
yin_yang_type	VARCHAR	阴/阳
combo_tags	TEXT	组合标签（如：三合、六合等）

3. element_map（五行与号码映射表）
字段名	类型	说明
year	INT	年份（如五行切换受年份影响）
element	VARCHAR	五行（如：金）
numbers	TEXT	对应号码（逗号分隔）
element_combo	TEXT	所属五行组合标签（金水、木火等）

4. number_meta（号码多维属性表）
| number | INT | 号码 |
| size_type | VARCHAR | 大/小 |
| parity_type | VARCHAR | 单/双 |
| sum_type | VARCHAR | 合数大/合数小 |
| sum_parity | VARCHAR | 合数单/合数双 |
| wave_color | VARCHAR | 红/蓝/绿波 |
| seq_type | VARCHAR | 顺向码/逆反码 |
| zodiac | VARCHAR | 所属生肖（当年） |
| element | VARCHAR | 所属五行（当年） |
| tags | TEXT | 多维组合标签（如：大单红金） |

5. combo_extremes（组合极限统计表）
| combo_type | VARCHAR | 组合类型（如：4生肖组合） |
| combo_id | VARCHAR | 组合唯一ID（如：鼠牛虎兔） |
| total_hits | INT | 总命中次数 |
| max_miss | INT | 最大遗漏 |
| current_miss | INT | 当前遗漏 |
| last_hit_date | DATE | 最近命中时间 |
| is_extreme | BOOLEAN | 是否处于极限状态 |

🧠 二、多维组合结构建模（支持 DSL 与动态组合）
所有组合生成逻辑支持自动映射与标签驱动（如组合含：1大+1小+1金+1水+1男肖+1阴肖）

每种组合对应唯一 hash 或 ID，用于建模、统计、触发预测

多维组合类型支持：

多维生肖组合（4肖组、六合配、三合配、五福肖等）

多维五行组合（金水、火土、水火等）

多维色波组合（红蓝波、蓝绿波等）

多维号码组合（满足条件的号码分组，如策略选号系统）

📦 三、配置数据支持模块（每年初始化）
bash
复制
编辑
/init/zodiac_2025.yaml
/init/element_2025.yaml
/init/color_wave_2025.yaml
/init/number_meta.csv
通过年份初始化生肖、五行、色波映射关系

支持切换年份后重新生成映射

🔁 四、系统流程对接图（部分）
bash
复制
编辑
数据导入（csv/db）→ 映射更新（生肖/五行）→ number_meta 构建 →
→ 多维组合生成（生肖组 / 五行组 / 色波组）→
→ 极限统计计算 → 更新 combo_extremes 表 →
→ DSL策略匹配器触发策略组合 → 回测 / 预测模块调用
📘 建议：关键点优化
动态映射机制：

每年2月（春节）重新切换生肖 → 动态生成生肖号码映射

五行与号码变动，支持版本化

DSL配置映射：

建议为组合策略使用 DSL 规则配置，如：

dsl
复制
编辑
combo("生肖4联组", "鼠,牛,虎,兔") if max_miss >= 85
combo("金木组合", ["金", "木"]) if current_miss >= 20 and total_hits > 30
预测触发器与评分机制：

极限组合命中触发器（当前遗漏 = 最大遗漏）

冷临界权重预测（遗漏 ≥ 80% max_miss）

加入组合打分模型 → 策略融合模块使用

 模块名称：ExtremeStatTracker（极限追踪器）
🎯 模块目标：
对所有组合（如：生肖组合、五行组合、色波组合等）进行：

命中统计

当前遗漏期数统计

最大历史遗漏计算

是否逼近“极限”条件判断（例如：90%历史最大遗漏）

📦 数据结构设计
表一：combo_extremes（组合极限统计表）
字段名	类型	说明
id	INT	主键
combo_type	TEXT	组合类型（如 生肖4组合）
combo_key	TEXT	唯一组合名（如 兔龙蛇马）
hit_count	INT	命中总次数
max_miss	INT	最大遗漏值
current_miss	INT	当前遗漏期数
is_near_extreme	BOOLEAN	是否逼近极限（如 80%以上）
last_hit_period	INT	最近一次命中期号
updated_at	DATETIME	更新时间

🔁 流程图说明
mermaid
复制
编辑
flowchart TD
    A[读取历史开奖记录] --> B[遍历每一期数据]
    B --> C[对每个组合判断是否命中]
    C --> D[更新命中次数 / 当前遗漏]
    D --> E[比较最大遗漏，更新]
    E --> F{是否逼近极限阈值}
    F -->|是| G[标记为 is_near_extreme=True]
    F -->|否| H[is_near_extreme=False]
    G --> I[写入 combo_extremes 表]
    H --> I
🧠 逼近极限的判断逻辑（可配置）
python
复制
编辑
def is_near_extreme(current_miss, max_miss, threshold=0.9):
    return current_miss >= int(max_miss * threshold)
🧱 初版实现结构（Python）
python
复制
编辑
class ExtremeStatTracker:
    def __init__(self, db_connection, combo_list, history_draws):
        self.db = db_connection
        self.combos = combo_list        # List of dicts: {"type": "生肖4组合", "key": "兔龙蛇马", "members": ["兔", "龙", "蛇", "马"]}
        self.history = history_draws    # List of past draws

    def run_tracking(self):
        for combo in self.combos:
            key = combo["key"]
            combo_type = combo["type"]
            members = combo["members"]

            current_miss = 0
            max_miss = 0
            hit_count = 0
            last_hit = None

            for idx, draw in enumerate(reversed(self.history)):
                if any(member in draw for member in members):
                    hit_count += 1
                    if current_miss > max_miss:
                        max_miss = current_miss
                    current_miss = 0
                    last_hit = len(self.history) - idx
                else:
                    current_miss += 1

            near_extreme = self.is_near_extreme(current_miss, max_miss)

            # 写入或更新数据库
            self.update_db(combo_type, key, hit_count, current_miss, max_miss, last_hit, near_extreme)

    def is_near_extreme(self, current_miss, max_miss, threshold=0.9):
        if max_miss == 0:
            return False
        return current_miss >= int(max_miss * threshold)

    def update_db(self, combo_type, key, hit_count, current_miss, max_miss, last_hit, near_extreme):
        # 示例伪代码：数据库写入逻辑
        self.db.insert_or_update("combo_extremes", {
            "combo_type": combo_type,
            "combo_key": key,
            "hit_count": hit_count,
            "current_miss": current_miss,
            "max_miss": max_miss,
            "last_hit_period": last_hit,
            "is_near_extreme": near_extreme,
            "updated_at": datetime.now()
        })
🔧 你接下来的工作建议：
步骤	内容
1	准备历史开奖记录 history_draws（例如：按期号列表，包含生肖、五行、色波信息）
2	准备组合列表 combo_list（所有策略涉及的组合）
3	建立 combo_extremes 表
4	集成 ExtremeStatTracker 并与预测器联动（逼近极限则加入候选池）

步：组合列表生成器（组合定义器）
这是极限追踪系统和策略系统都要依赖的 核心输入模块。我们需要为每种策略生成其完整组合清单，用于后续的统计与预测。

✅ 模块名称：ComboGenerator（组合定义器）
📌 目标：
为以下多维策略生成完整的组合结构：

类型	示例组合	描述说明
生肖4组合	马虎兔羊	从12生肖中任选4个组合
五行2组合	火+水	从五行中两两组合
色波组合	红绿波、红蓝波、蓝绿波	固定三种波段组合
多维生肖组合	左肖+右肖、吉肖+凶肖	按“标签系统”组合（2标签组合）
多维号码组合	大数+合数、双数+尾数1	自定义标签组合

📦 一、组合生成器的核心数据结构
python
复制
编辑
from itertools import combinations, product

class ComboGenerator:
    def __init__(self):
        self.shengxiao = ["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"]
        self.wuxing = ["金","木","水","火","土"]
        self.sebo = ["红波", "绿波", "蓝波"]
        self.shengxiao_tags = {
            "左肖": ["鼠","牛","龙","蛇","马","羊"],
            "右肖": ["虎","兔","猴","鸡","狗","猪"],
            "吉肖": ["马","羊","猴","鸡","狗","猪"],
            "凶肖": ["鼠","牛","虎","兔","龙","蛇"],
            "阳肖": ["虎","马","龙","猴","狗","鸡"],
            "阴肖": ["鼠","牛","兔","蛇","羊","猪"]
        }

    def generate_shengxiao_4(self):
        return [{"type": "生肖4组合", "key": ",".join(c), "members": list(c)} for c in combinations(self.shengxiao, 4)]

    def generate_wuxing_2(self):
        return [{"type": "五行2组合", "key": "+".join(c), "members": list(c)} for c in combinations(self.wuxing, 2)]

    def generate_sebo(self):
        return [
            {"type": "色波组合", "key": "红绿波", "members": ["红波", "绿波"]},
            {"type": "色波组合", "key": "红蓝波", "members": ["红波", "蓝波"]},
            {"type": "色波组合", "key": "蓝绿波", "members": ["蓝波", "绿波"]}
        ]

    def generate_shengxiao_tag_combos(self):
        tag_keys = list(self.shengxiao_tags.keys())
        combos = combinations(tag_keys, 2)
        combo_list = []
        for a, b in combos:
            members = list(set(self.shengxiao_tags[a]) | set(self.shengxiao_tags[b]))
            combo_list.append({
                "type": "多维生肖组合",
                "key": f"{a}+{b}",
                "members": members
            })
        return combo_list
🧱 输出示例（结构标准化）
json
复制
编辑
{
  "type": "多维生肖组合",
  "key": "吉肖+凶肖",
  "members": ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
}
📤 模块用途
✅ 为极限追踪模块提供“统计对象清单”
✅ 为策略 DSL 提供“组合清单注册表”
✅ 为预测时提供“组合识别与打标签”逻辑（如是否属于逼近极限）

✅ 下一步推荐：
如果你同意该结构，我们可以：

实现组合生成模块并存储为 JSON 文件（供后续导入 DB 使用）

生成每类组合并同步写入数据库（用于前端筛选与追踪）

接入 ExtremeStatTracker 模块做历史遍历统计

极限组合统计与回测模块
📌 模块名称：ExtremeStatTracker
🎯 模块目标：
对所有组合（如生肖组合、五行组合等）进行：

任务	说明
✅ 历史遗漏统计	每个组合在历史开奖中连续未出现多少期
✅ 极限值计算	每个组合历史最大遗漏值是多少
✅ 当前状态分析	当前是否处于逼近极限（如：当前遗漏 = 极限 - 1）
✅ 警示信号输出	是否需要预测提示（如命中临界区间）
✅ 回测分析支持	查看每期是否命中、命中前遗漏值、是否在预测中

🗂️ 数据表设计建议（建议存入 SQLite / PostgreSQL）
1. combo_stat_extremes – 组合极限统计表：
字段名	类型	说明
id	INTEGER	主键
combo_key	TEXT	组合标识，如“马虎羊兔”
combo_type	TEXT	类型，如“生肖4组合”
max_omit	INTEGER	历史最大遗漏期数
last_hit_phase	TEXT	上一次命中的期号
last_omit_count	INTEGER	最近一次的遗漏数
current_omit	INTEGER	当前遗漏期数（动态更新）
near_extreme	INTEGER	是否逼近极限（1=是，0=否）

2. combo_trace_log – 回测日志表
字段名	类型	说明
phase	TEXT	开奖期号
combo_key	TEXT	组合标识，如“马虎羊兔”
hit	INTEGER	是否命中该组合（1 是，0 否）
omit_before_hit	INTEGER	命中前的遗漏值（用于回测评估）
combo_type	TEXT	类型

📈 模块核心逻辑（伪代码）
python
复制
编辑
class ExtremeStatTracker:
    def __init__(self, history_data, all_combos):
        self.history = history_data  # 历史开奖数据（含日期、特码、生肖、五行等）
        self.combos = all_combos     # 从 ComboGenerator 获取所有组合

    def trace_all(self):
        result_map = {}  # combo_key -> [omit, max_omit, last_hit]
        logs = []

        for combo in self.combos:
            key = combo['key']
            members = set(combo['members'])
            max_omit = 0
            current_omit = 0
            last_hit = None
            for phase in self.history:
                target = phase['target_shengxiao']  # 或其他字段
                hit = target in members
                if hit:
                    max_omit = max(max_omit, current_omit)
                    logs.append({
                        "phase": phase["phase"],
                        "combo_key": key,
                        "combo_type": combo["type"],
                        "hit": 1,
                        "omit_before_hit": current_omit
                    })
                    current_omit = 0
                    last_hit = phase["phase"]
                else:
                    logs.append({
                        "phase": phase["phase"],
                        "combo_key": key,
                        "combo_type": combo["type"],
                        "hit": 0,
                        "omit_before_hit": current_omit
                    })
                    current_omit += 1

            result_map[key] = {
                "combo_type": combo["type"],
                "max_omit": max_omit,
                "last_hit_phase": last_hit,
                "current_omit": current_omit,
                "near_extreme": int(current_omit >= max_omit - 1)
            }

        return result_map, logs
📦 输出结构
极限统计表 → 用于实时展示与预测警示

回测日志表 → 用于模型训练/测试/策略评分

✅ 下一步建议（分支选择）：
🔧 实现并测试 ExtremeStatTracker 回测器

🧠 设计【策略评分算法（融合/命中率）】

🛠️ 构建【预测引擎】，支持组合调用与策略融合

🔄 构建【每日数据更新 + 自动同步 + 极限状态提醒】

5️⃣ 策略评分与融合算法模块设计
📌 模块名称：StrategyScorerAndFusionEngine
🎯 模块目标：
功能	说明
✅ 策略评分计算	依据回测结果对每种组合策略评分
✅ 模型权重调整	自动根据历史效果为策略打分/加权
✅ 策略融合输出	不同策略综合后得出最终推荐号码
✅ 灵活策略组合	支持手动/自动配置策略组合和权重
✅ 模型排名与排序	用户可直观选择高分策略优先使用

🗂️ 核心结构
1. 策略评分表 strategy_scores
字段名	类型	说明
strategy_id	TEXT	策略唯一标识，如“生肖组合4码极限”
type	TEXT	策略类型，如“生肖”、“五行”
period_count	INTEGER	回测期数
hit_count	INTEGER	命中次数
miss_count	INTEGER	未命中次数
hit_rate	FLOAT	命中率 = 命中 / 总期数
avg_omit_before_hit	FLOAT	命中前的平均遗漏
score	FLOAT	策略得分（公式详见下方）
weight	FLOAT	当前策略用于融合时的权重（自动/手动）

📊 策略评分公式建议（可配置）
python
复制
编辑
# 策略评分 = 命中率 * 权重因子A + (1 / 平均遗漏) * 权重因子B
score = hit_rate * 0.7 + (1 / (avg_omit_before_hit + 0.001)) * 0.3
支持将上述参数通过配置文件或策略 DSL 自动定义：

yaml
复制
编辑
strategy_scoring:
  hit_rate_weight: 0.7
  avg_omit_weight: 0.3
🔁 策略融合算法设计
1. 基于权重融合（加权投票）
python
复制
编辑
def fuse_predictions(strategies):
    score_map = defaultdict(float)
    for strat in strategies:
        for number in strat["predicted_numbers"]:
            score_map[number] += strat["weight"]
    final_candidates = sorted(score_map.items(), key=lambda x: x[1], reverse=True)
    return [n for n, _ in final_candidates[:N]]  # 保留前N个号码
2. 支持以下融合策略：
策略名称	描述
加权投票	多策略预测号码权重加权求和，取前N名
策略命中率排序	命中率高的策略优先选择其号码
多策略交集	所有策略都命中的号码才推荐
分层融合策略	一级策略提名，二级策略评分筛选

📦 输出结构（融合结果）
json
复制
编辑
{
  "final_prediction": [12, 23, 38, 41, 45],
  "details": [
    {"strategy": "生肖4码极限", "weight": 0.35, "numbers": [12, 23]},
    {"strategy": "五行组合3组追踪", "weight": 0.25, "numbers": [23, 38, 45]},
    {"strategy": "冷尾热码法", "weight": 0.15, "numbers": [41, 12]}
  ]
}
🔐 可扩展设计建议
✅ 用户可手动配置权重（GUI 表单或 DSL）

✅ 支持评分算法自定义 DSL（下节详解）

✅ 保留历史预测记录、融合路径记录

✅ 预测结果可保存至本地 / 数据库 / 自动标注

✅ 下一步建议（分支）：
✏️ 设计策略评分与融合结果 GUI 展示模块

📑 建立策略评分与融合规则配置 DSL（策略 DSL）

🧪 实现并测试融合引擎、评分器回调极限模块

📦 接入预测主流程与导出结果系统

6️⃣ 策略 DSL（Domain-Specific Language）设计
🎯 目标：
用一种“类 YAML / JSON 的配置语言”定义策略结构、规则参数、评分逻辑和组合方式，最终支持：

快速配置/测试策略组合

动态调整评分与融合策略

GUI 接口对接配置表单

多版本策略切换与管理

🗂️ DSL 总结构示例（基于 YAML 格式）
yaml
复制
编辑
strategies:
  - id: "生肖极限组合"
    type: "生肖"
    source: "multi_zodiac_4"
    mode: "极限遗漏"
    filters:
      - type: "history_miss"
        max_omit: 120
      - type: "zodiac_in_year"
        include: ["虎", "兔", "马", "羊"]
    scoring:
      hit_weight: 0.6
      omit_weight: 0.4
    active: true

  - id: "五行三组组合"
    type: "五行"
    source: "wuxing_combos"
    mode: "高频追踪"
    filters:
      - type: "element_distribution"
        min_elements: 3
    scoring:
      hit_weight: 0.8
      omit_weight: 0.2
    active: true
🔧 DSL 关键词定义说明
字段名	类型/说明
id	策略唯一标识（可显示于前端界面）
type	策略分类：如 生肖, 五行, 号码组合, 色波, 尾数 等
source	策略来源名称：绑定数据库组合表或程序生成逻辑，如 multi_zodiac_4
mode	分析/追踪模式：如 极限遗漏, 频率优先, 反向构造, 混合评分
filters	过滤条件数组，支持多种过滤器（遗漏、组合包含、权重限制、年份生肖变化）
scoring	策略评分参数，指定命中率/遗漏率等权重
active	是否启用此策略（GUI 可用于批量启用/禁用）

🧠 内建过滤器定义（filters）
过滤器类型	说明
history_miss	历史遗漏值限制，如 max_omit
zodiac_in_year	本年生肖限制，如 include: ["牛"]
element_distribution	五行分布要求
number_parity	单双要求，如奇偶平衡
color_wave	色波分布要求

📊 DSL 示例：定义融合规则
yaml
复制
编辑
fusion:
  strategy_weighting: "manual"  # 或 "auto"
  max_numbers: 14
  method: "weighted_union"      # weighted_union / intersection / priority_top
  fallback: "high_score_first"
📦 DSL 应用场景举例
场景	DSL 表达方式
启用生肖极限策略 + 五行组合策略	active: true 设置
控制融合最多14个号码	fusion.max_numbers: 14
权重由用户界面调整	strategy_weighting: manual
所有策略号码做交集	fusion.method: intersection
命中率低的策略禁用	配置 GUI/回测评分后自动设为 active: false

🧪 模块对接建议
模块	使用 DSL 部分
策略加载器	strategies 全体
策略评分器	scoring
融合输出引擎	fusion 部分
GUI 编辑器	提取 DSL 显示为表单结构
回测引擎	依据 DSL 策略逐条回测

✅ 下一步建议
你可以选择继续进行：

🔹 第7步：策略融合引擎与预测输出主流程
融合引擎读取 DSL

动态加载策略评分和号码组合

生成最终推荐预测结果（可视化输出）

或

🔹 第8步：构建 GUI 策略管理与评分调整界面
通过 GUI 编辑策略 DSL

一键运行评分与融合

导出融合预测与评分报告

 第7步：策略融合引擎与预测输出主流程设计
🎯 总目标
将所有通过评分过滤的策略结果进行融合，并输出最终预测号码列表，同时生成：

各策略得分报告

融合过程透明记录（便于调试与优化）

最终预测结果落盘或界面展示

🔁 策略融合主流程图（逻辑）
text
复制
编辑
         +--------------------+
         | 加载策略 DSL 配置  |
         +--------------------+
                   |
                   v
         +--------------------+
         | 获取每个策略组合   | <---- 来自 DB or 生成器
         +--------------------+
                   |
                   v
         +--------------------+
         | 应用 filters 过滤器 |
         +--------------------+
                   |
                   v
         +------------------------+
         | 计算策略评分 scoring   |
         +------------------------+
                   |
                   v
         +------------------------------+
         | 策略得分排序/筛选 active 策略 |
         +------------------------------+
                   |
                   v
         +--------------------------+
         | 号码融合 (按权重/优先级)  |
         +--------------------------+
                   |
                   v
         +------------------------------+
         | 控制输出个数（如最多14个）    |
         +------------------------------+
                   |
                   v
         +------------------------------+
         | 输出预测结果 + 策略评分报告   |
         +------------------------------+
⚙️ 核心模块拆解
模块名称	功能说明
StrategyLoader	从 DSL 配置中加载所有启用策略
ComboFetcher	从数据库中拉取各类策略组合（如：multi_zodiac_4）
FilterEngine	按策略内 filters 条件过滤组合
ScoringEngine	对每个组合打分（命中率、遗漏率等）
FusionEngine	将策略输出融合为一组预测号码
Limiter	控制输出个数（如：只输出 12~16 个）
OutputWriter	输出最终号码、得分报告、图表数据

🔧 融合算法支持（FusionEngine）
方法类型	描述
weighted_union	所有组合按评分权重合并，并按分数排序，取前N个
intersection	所有组合交集，得到共同推荐的号码（适合策略趋同融合）
priority_top	按策略优先级逐层选取高评分组合填充预测号码
voting_system	多策略中号码出现频率计分，出现次数高的优先
conditional_fusion	条件触发融合，如“如果出现极限遗漏则用策略X”

🧮 示例伪代码（Python 风格）
python
复制
编辑
for strategy in strategies:
    combos = load_combos(strategy.source)
    filtered = apply_filters(combos, strategy.filters)
    scored = score_combos(filtered, strategy.scoring)
    strategy_results.append(scored)

# 融合逻辑
final_candidates = fusion_engine(strategy_results, method="weighted_union", top_n=14)

# 输出
save_results(final_candidates, to="预测结果.txt")
display_to_gui(final_candidates)
📂 输出数据结构建议
python
复制
编辑
{
  "预测日期": "2025-07-18",
  "融合策略": "weighted_union",
  "预测号码": [3, 11, 14, 18, 25, 30, 31, 35, 38, 41, 43, 47],
  "策略评分详情": [
    {
      "策略名": "生肖极限组合",
      "得分": 82.4,
      "使用组合": ["虎兔马羊"],
      "命中率": 63.4,
      "遗漏值": 88
    },
    ...
  ]
}
✅ 本模块完成后，你将获得：
可执行融合预测主流程

自动评分并控制预测数量（如 12~16 个）

完整预测结果报告，可回测可导出

与 GUI 联动展示每个策略和预测来源

🔜 下一步建议：
进入 第8步：策略 GUI 管理与评分融合可视化界面设计，实现：

策略 DSL 的图形化配置与编辑

实时查看评分/预测结果

可视化评分对比图表（雷达图、条形图等）

策略运行开关、融合方式控制

 第8步：策略 GUI 管理与评分融合可视化界面设计
🎯 目标
图形化管理策略 DSL 配置（策略开关、权重、过滤条件等）

可视化每个策略评分与预测组合详情

控制融合方式、结果展示数量

预览当前预测结果与历史回测效果

生成策略配置、评分报告、结果导出文件

🖼️ 主界面草图（分区）
text
复制
编辑
╔════════════════════════════════════════════════════════════════════╗
║                       [策略融合与预测管理界面]                    ║
╠══════════════════╦════════════════════════════════════════════════╣
║ ▌策略管理区      ║ ▌预测结果展示区                               ║
║ ──────────────── ║ ───────────────────────────────────────────── ║
║ [√] 生肖极限组合   ║ 日期：2025-07-18                              ║
║ [√] 绿波组合策略   ║ 策略融合方式：Weighted Union                ║
║ [√] 多维五行组合   ║ 输出号码：                                   ║
║ [×] 龙虎偏移策略   ║ 03 11 14 18 25 30 31 35 38 41 43 47          ║
║                   ║                                              ║
║ 策略权重调整：     ║ [导出预测]  [查看评分]  [回测分析]           ║
║ ┌───────────────┐ ║                                              ║
║ │生肖极限：80%   │ ║                                              ║
║ │绿波组合：60%   │ ║                                              ║
║ └───────────────┘ ║                                              ║
╠══════════════════╩════════════════════════════════════════════════╣
║ ▌策略评分雷达图 | 策略融合详情 | 回测结果折线图                  ║
╚════════════════════════════════════════════════════════════════════╝
🧩 模块拆解与功能
区域	功能名称	描述
策略管理区	StrategyControlPanel	策略开关、权重输入、过滤条件配置
策略评分区	ScoringRadarChart	显示每个策略评分（如命中率、遗漏、热度）雷达图
融合控制区	FusionSettingsPanel	选择融合方法、号码个数限制、排序方式
预测展示区	PredictionViewer	最终号码、来源策略、组合列表
历史回测区	BacktestChart	显示每组预测在历史期号中的命中情况（曲线或柱状图）
导出/保存区	ExportPanel	保存预测结果、导出策略评分、生成报告等

💾 数据结构接口建议（前端与后端交互）
策略配置结构（可读写）
json
复制
编辑
{
  "strategies": [
    {
      "name": "multi_zodiac_4",
      "enabled": true,
      "weight": 0.85,
      "filters": {
        "max_omission": 48,
        "min_hit_rate": 0.25
      }
    },
    ...
  ],
  "fusion_method": "weighted_union",
  "output_count": 14
}
策略评分展示结构
json
复制
编辑
{
  "strategy_scores": [
    {
      "name": "绿波组合",
      "score": 78.2,
      "omission": 44,
      "hit_rate": 0.36,
      "avg_delay": 7.1
    },
    ...
  ]
}
融合结果结构
json
复制
编辑
{
  "final_prediction": [3, 11, 18, 25, 30, 31, 35, 38, 41, 43, 47],
  "origin_mapping": {
    "multi_zodiac_4": [11, 18, 35],
    "green_wave_limit": [30, 38, 43],
    "five_elements_top": [3, 25, 47]
  }
}
✅ 完成本模块后你将拥有：
全图形化策略管理界面（DSL 语法屏蔽）

策略与融合流程可控、可调试、可视化

预测输出结果与策略对应关系一览无余

实时评分、历史命中趋势可视化

导出预测结果与策略权重快照报告

📌 技术选型建议
技术	用途
PyQt / PySide	GUI 框架
Matplotlib / PyQtGraph	图表绘制
SQLite / JSON 文件	存储策略配置、评分报告
自定义 DSL 解析器	翻译图形配置为 DSL 配置文本，供系统执行

下一步建议
第9步：构建策略调度器与模型预测入口统一调度系统

建立统一的入口 PredictEngine.run()，可调用所有策略、融合引擎、评分系统、回测系统。

支持 CLI 调用、GUI 按钮触发、定时任务预测。