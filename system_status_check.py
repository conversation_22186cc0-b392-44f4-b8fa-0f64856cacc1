"""
系统状态全面检查工具
检查所有文件、数据库连接、预测模组和分析功能是否正常工作
"""

import os
import sqlite3
import json
import traceback
from datetime import datetime


class SystemStatusChecker:
    """系统状态检查器"""
    
    def __init__(self):
        self.results = {
            'files': {},
            'database': {},
            'modules': {},
            'predictions': {},
            'overall_status': 'UNKNOWN'
        }
    
    def check_all(self):
        """执行全面检查"""
        print("🔍 开始系统状态全面检查...")
        print("=" * 60)
        
        # 1. 检查核心文件
        self.check_core_files()
        
        # 2. 检查数据库
        self.check_database()
        
        # 3. 检查模块导入
        self.check_module_imports()
        
        # 4. 测试预测功能
        self.test_prediction_system()
        
        # 5. 生成总结报告
        self.generate_summary()
        
        return self.results
    
    def check_core_files(self):
        """检查核心文件状态"""
        print("\n📁 检查核心文件...")
        
        core_files = [
            'main.py',
            'combo_generator.py',
            'extreme_stat_tracker.py',
            'strategy_scorer.py',
            'lottery_data.db',
            'lottery_data_20250717.csv'
        ]
        
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                self.results['files'][file] = {
                    'exists': True,
                    'size': size,
                    'status': 'OK' if size > 0 else 'EMPTY'
                }
                status = "✅" if size > 0 else "⚠️"
                print(f"  {status} {file}: {size} bytes")
            else:
                self.results['files'][file] = {
                    'exists': False,
                    'size': 0,
                    'status': 'MISSING'
                }
                print(f"  ❌ {file}: 文件不存在")
    
    def check_database(self):
        """检查数据库连接和数据"""
        print("\n🗄️ 检查数据库...")
        
        try:
            conn = sqlite3.connect('lottery_data.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            self.results['database']['connection'] = 'OK'
            self.results['database']['tables'] = tables
            
            print(f"  ✅ 数据库连接正常")
            print(f"  📊 发现 {len(tables)} 个表:")
            
            # 检查每个表的数据量
            table_data = {}
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_data[table] = count
                    print(f"    - {table}: {count} 条记录")
                except Exception as e:
                    table_data[table] = f"ERROR: {e}"
                    print(f"    - {table}: 查询失败 - {e}")
            
            self.results['database']['table_data'] = table_data
            
            # 检查关键数据
            if 'lottery_records' in tables:
                cursor.execute("SELECT COUNT(*) FROM lottery_records")
                record_count = cursor.fetchone()[0]
                
                if record_count > 0:
                    cursor.execute("SELECT period_number, draw_date FROM lottery_records ORDER BY period_number DESC LIMIT 1")
                    latest = cursor.fetchone()
                    print(f"  📅 最新记录: {latest[0]} ({latest[1]})")
                    self.results['database']['latest_record'] = latest
                else:
                    print(f"  ⚠️ lottery_records 表为空")
            
            conn.close()
            
        except Exception as e:
            self.results['database']['connection'] = f'ERROR: {e}'
            print(f"  ❌ 数据库连接失败: {e}")
    
    def check_module_imports(self):
        """检查模块导入"""
        print("\n📦 检查模块导入...")
        
        modules_to_test = [
            'combo_generator',
            'extreme_stat_tracker', 
            'strategy_scorer'
        ]
        
        for module_name in modules_to_test:
            try:
                module = __import__(module_name)
                self.results['modules'][module_name] = 'OK'
                print(f"  ✅ {module_name}: 导入成功")
                
                # 检查主要类
                if module_name == 'combo_generator':
                    generator = module.ComboGenerator()
                    combos = generator.generate_shengxiao_4()
                    print(f"    - 生成 {len(combos)} 个生肖4组合")
                
                elif module_name == 'extreme_stat_tracker':
                    tracker = module.ExtremeStatTracker(None, [], [])
                    print(f"    - ExtremeStatTracker 类可用")
                
                elif module_name == 'strategy_scorer':
                    scorer = module.StrategyScorerAndFusionEngine({}, [])
                    print(f"    - StrategyScorerAndFusionEngine 类可用")
                    
            except Exception as e:
                self.results['modules'][module_name] = f'ERROR: {e}'
                print(f"  ❌ {module_name}: 导入失败 - {e}")
    
    def test_prediction_system(self):
        """测试预测系统"""
        print("\n🎯 测试预测系统...")
        
        try:
            # 测试完整预测流程
            from combo_generator import ComboGenerator
            from extreme_stat_tracker import ExtremeStatTracker
            from strategy_scorer import StrategyScorerAndFusionEngine
            
            # 1. 生成组合
            print("  🔄 生成组合...")
            combo_gen = ComboGenerator()
            shengxiao_4_combos = combo_gen.generate_shengxiao_4()
            wuxing_2_combos = combo_gen.generate_wuxing_2()
            all_combos = shengxiao_4_combos + wuxing_2_combos
            print(f"    - 生成 {len(all_combos)} 个组合")
            
            # 2. 加载历史数据
            print("  📊 加载历史数据...")
            history_draws = self.load_test_data()
            print(f"    - 加载 {len(history_draws)} 条历史记录")
            
            # 3. 运行统计追踪
            print("  📈 运行统计追踪...")
            tracker = ExtremeStatTracker(None, all_combos[:10], history_draws)  # 只测试前10个组合
            tracker.run_tracking()
            print("    - 统计追踪完成")
            
            # 4. 模拟策略评分
            print("  🎯 模拟策略评分...")
            strategies = {
                "test_strategy_1": {
                    "hit_count": 5,
                    "avg_omit_before_hit": 8,
                    "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
                },
                "test_strategy_2": {
                    "hit_count": 3,
                    "avg_omit_before_hit": 12,
                    "predicted_numbers": [5, 12, 19, 26, 33, 40, 47]
                }
            }
            
            scorer = StrategyScorerAndFusionEngine(strategies, history_draws)
            scores = scorer.calculate_scores()
            print(f"    - 计算了 {len(scores)} 个策略评分")
            
            # 5. 测试预测融合
            print("  🔮 测试预测融合...")
            predictions_to_fuse = [
                {
                    "strategy_id": strat_id,
                    "weight": scores[strat_id]["score"],
                    "numbers": strat_data["predicted_numbers"]
                }
                for strat_id, strat_data in strategies.items()
            ]
            
            final_prediction = scorer.fuse_predictions(predictions_to_fuse, method='weighted_union', top_n=12)
            print(f"    - 生成最终预测: {sorted(final_prediction)}")
            
            self.results['predictions'] = {
                'status': 'OK',
                'combos_generated': len(all_combos),
                'history_loaded': len(history_draws),
                'strategies_scored': len(scores),
                'final_prediction': sorted(final_prediction)
            }
            
            print("  ✅ 预测系统测试成功")
            
        except Exception as e:
            self.results['predictions'] = {
                'status': f'ERROR: {e}',
                'error_details': traceback.format_exc()
            }
            print(f"  ❌ 预测系统测试失败: {e}")
    
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 尝试从CSV文件加载
            import csv
            history = []
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    history.append({
                        'period': row[period_key],
                        'zodiac': row.get('zodiac', ''),
                        'five_element': row.get('five_element', '')
                    })
            return list(reversed(history))  # 反向排序
            
        except Exception as e:
            print(f"    ⚠️ CSV加载失败，使用数据库: {e}")
            
            # 尝试从数据库加载
            try:
                conn = sqlite3.connect('lottery_data.db')
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT period_number, zodiac, five_element 
                    FROM lottery_records 
                    ORDER BY period_number DESC 
                    LIMIT 100
                """)
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        'period': str(row[0]),
                        'zodiac': row[1] or '',
                        'five_element': row[2] or ''
                    })
                
                conn.close()
                return history
                
            except Exception as db_e:
                print(f"    ⚠️ 数据库加载也失败: {db_e}")
                # 返回模拟数据
                return [
                    {'period': '2025001', 'zodiac': '鼠', 'five_element': '金'},
                    {'period': '2025002', 'zodiac': '牛', 'five_element': '木'},
                    {'period': '2025003', 'zodiac': '虎', 'five_element': '水'},
                ]
    
    def generate_summary(self):
        """生成总结报告"""
        print("\n📋 系统状态总结")
        print("=" * 40)
        
        # 统计各模块状态
        file_ok = sum(1 for f in self.results['files'].values() if f['status'] == 'OK')
        file_total = len(self.results['files'])
        
        module_ok = sum(1 for m in self.results['modules'].values() if m == 'OK')
        module_total = len(self.results['modules'])
        
        db_ok = self.results['database'].get('connection') == 'OK'
        pred_ok = self.results['predictions'].get('status') == 'OK'
        
        print(f"📁 文件状态: {file_ok}/{file_total} 正常")
        print(f"🗄️ 数据库: {'✅ 正常' if db_ok else '❌ 异常'}")
        print(f"📦 模块导入: {module_ok}/{module_total} 成功")
        print(f"🎯 预测系统: {'✅ 正常' if pred_ok else '❌ 异常'}")
        
        # 确定总体状态
        if file_ok == file_total and db_ok and module_ok == module_total and pred_ok:
            self.results['overall_status'] = 'EXCELLENT'
            print(f"\n🎉 总体状态: 优秀 - 所有功能正常")
        elif file_ok >= file_total * 0.8 and (db_ok or pred_ok) and module_ok >= module_total * 0.8:
            self.results['overall_status'] = 'GOOD'
            print(f"\n✅ 总体状态: 良好 - 主要功能正常")
        elif file_ok >= file_total * 0.6 or db_ok or module_ok >= module_total * 0.6:
            self.results['overall_status'] = 'FAIR'
            print(f"\n⚠️ 总体状态: 一般 - 部分功能可用")
        else:
            self.results['overall_status'] = 'POOR'
            print(f"\n❌ 总体状态: 较差 - 多数功能异常")
        
        # 保存详细报告
        self.save_report()
    
    def save_report(self):
        """保存详细报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'results': self.results
        }
        
        with open('system_status_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存到: system_status_report.json")


def main():
    """主函数"""
    print("🔧 系统状态全面检查工具")
    print("检查所有文件、数据库连接、预测模组和分析功能")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    checker = SystemStatusChecker()
    results = checker.check_all()
    
    print(f"\n🏁 检查完成!")
    return results


if __name__ == "__main__":
    main()
