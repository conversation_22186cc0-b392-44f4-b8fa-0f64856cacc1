import unittest
from three_layer_filter_engine import ThreeLayerFilterEngine
from typing import List

class TestThreeLayerFilterEngine(unittest.TestCase):

    def setUp(self):
        self.engine = ThreeLayerFilterEngine()
        self.test_candidates = list(range(1, 49))
        self.test_history = [7, 14, 21, 28, 35, 42]

    def test_initialization(self):
        """测试引擎初始化"""
        self.assertIsNotNone(self.engine)
        self.assertIsNotNone(self.engine.tail_filter)

    def test_large_filter(self):
        """测试大筛子"""
        filtered = self.engine.large_filter(self.test_candidates)
        self.assertIsInstance(filtered, list)
        self.assertTrue(all(isinstance(x, int) for x in filtered))

    def test_medium_filter(self):
        """测试中筛子"""
        filtered = self.engine.medium_filter(self.test_candidates)
        self.assertIsInstance(filtered, list)
        self.assertTrue(len(filtered) < len(self.test_candidates))

    def test_small_filter(self):
        """测试小筛子"""
        filtered = self.engine.small_filter(self.test_candidates)
        self.assertIsInstance(filtered, dict)
        self.assertTrue(all(isinstance(v, list) for v in filtered.values()))

    def test_apply_all_filters(self):
        """测试完整筛选流程"""
        results = self.engine.apply_all_filters(self.test_candidates)
        self.assertIsInstance(results, dict)
        self.assertTrue(len(results) > 0)

    def test_analyze_filter_effectiveness(self):
        """测试筛选效果分析"""
        analysis = self.engine.analyze_filter_effectiveness(
            self.test_history,
            self.test_candidates
        )
        self.assertIn('original_hit_rate', analysis)
        self.assertIn('large_filter_hit_rate', analysis)
        self.assertIn('medium_filter_hit_rate', analysis)
        self.assertIn('small_filter_hit_rate', analysis)

    def test_filter_statistics(self):
        """测试筛选统计"""
        stats = self.engine.get_filter_statistics(self.test_candidates)
        self.assertIn('initial_count', stats)
        self.assertIn('after_large_filter', stats)
        self.assertIn('after_medium_filter', stats)
        self.assertIn('tail_group_count', stats)

    def test_edge_cases(self):
        """测试边界情况"""
        # 空输入
        empty_results = self.engine.apply_all_filters([])
        self.assertIsInstance(empty_results, dict)

        # 单个数字
        single_results = self.engine.apply_all_filters([1])
        self.assertIsInstance(single_results, dict)

        # 重复数字
        duplicate_results = self.engine.apply_all_filters([1, 1, 1])
        self.assertIsInstance(duplicate_results, dict)

if __name__ == '__main__':
    unittest.main(verbosity=2)
