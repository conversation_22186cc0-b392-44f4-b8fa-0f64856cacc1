#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测结果对比分析工具
对比原系统和真实预测引擎的结果
"""

import json
from datetime import datetime
from real_prediction_engine import RealPredictionEngine

def analyze_prediction_differences():
    """分析预测差异"""
    print("🔍 预测系统对比分析")
    print("=" * 60)
    
    # 运行真实预测引擎
    real_engine = RealPredictionEngine()
    real_result = real_engine.run_real_prediction()
    
    print("\n📊 对比分析结果:")
    print("-" * 40)
    
    # 原系统的问题
    print("❌ 原系统存在的问题:")
    print("   1. 硬编码预测号码 - 多个策略使用固定号码")
    print("   2. 固定期号 - 使用硬编码的'2025199'期号")
    print("   3. 模拟统计数据 - hit_count=10, avg_omit=5 等固定值")
    print("   4. 示例性质 - 波色、大小单双等策略使用示例号码")
    print("   5. 缺乏真实分析 - 没有基于历史数据的真实统计")
    
    print("\n✅ 真实预测引擎的改进:")
    print("   1. 基于真实历史数据 - 1940条真实记录")
    print("   2. 动态期号计算 - 自动计算下一期号")
    print("   3. 真实统计分析 - 基于历史数据的频率、遗漏分析")
    print("   4. 多维度策略 - 生肖、五行、号码统计三重分析")
    print("   5. 科学融合算法 - 基于置信度的加权融合")
    
    print(f"\n🎯 真实预测结果:")
    print(f"   目标期号: {real_result['target_period']}")
    print(f"   推荐号码: {real_result['final_numbers']}")
    print(f"   置信度: {real_result['confidence']:.2%}")
    print(f"   分析基础: {real_result['analysis_periods']} 期历史数据")
    
    # 分析策略详情
    print(f"\n📋 策略分析详情:")
    for i, strategy in enumerate(real_result['strategy_details'], 1):
        print(f"   {i}. {strategy['name']}:")
        print(f"      - 预测号码: {strategy['numbers']}")
        print(f"      - 置信度: {strategy['confidence']:.2%}")
        print(f"      - 权重: {strategy['weight']:.0%}")
    
    return real_result

def create_prediction_report():
    """创建预测报告"""
    result = analyze_prediction_differences()
    
    # 生成HTML报告
    html_report = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>六合彩真实预测报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; color: #333; margin-bottom: 30px; border-bottom: 3px solid #007bff; padding-bottom: 20px; }}
        .section {{ margin: 20px 0; padding: 20px; border-left: 4px solid #007bff; background: #f8f9fa; }}
        .numbers {{ font-size: 24px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background: #fff3cd; border-radius: 8px; margin: 15px 0; }}
        .confidence {{ font-size: 20px; color: #28a745; text-align: center; margin: 10px 0; }}
        .strategy {{ margin: 15px 0; padding: 15px; background: white; border-radius: 5px; border: 1px solid #dee2e6; }}
        .problem {{ color: #dc3545; }}
        .solution {{ color: #28a745; }}
        table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
        th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 六合彩真实预测报告</h1>
            <p>基于1940期历史数据的科学预测分析</p>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="section">
            <h2>🎲 预测结果</h2>
            <div class="numbers">
                推荐号码: {', '.join(map(str, result['final_numbers']))}
            </div>
            <div class="confidence">
                整体置信度: {result['confidence']:.2%}
            </div>
            <p><strong>目标期号:</strong> {result['target_period']}</p>
            <p><strong>分析基础:</strong> {result['analysis_periods']} 期历史数据</p>
            <p><strong>使用策略:</strong> {result['strategies_used']} 个</p>
        </div>
        
        <div class="section">
            <h2>📊 策略分析</h2>
            <table>
                <tr>
                    <th>策略名称</th>
                    <th>预测号码</th>
                    <th>置信度</th>
                    <th>权重</th>
                </tr>
    """
    
    for strategy in result['strategy_details']:
        html_report += f"""
                <tr>
                    <td>{strategy['name']}</td>
                    <td>{', '.join(map(str, strategy['numbers']))}</td>
                    <td>{strategy['confidence']:.2%}</td>
                    <td>{strategy['weight']:.0%}</td>
                </tr>
        """
    
    html_report += f"""
            </table>
        </div>
        
        <div class="section">
            <h2>⚠️ 原系统问题分析</h2>
            <div class="problem">
                <h3>发现的主要问题:</h3>
                <ul>
                    <li><strong>硬编码预测号码:</strong> 多个策略使用固定的示例号码，如[1,2,3,4,5,6,7,8]</li>
                    <li><strong>固定期号:</strong> 使用硬编码的'2025199'期号，不会动态更新</li>
                    <li><strong>模拟统计数据:</strong> hit_count=10, avg_omit=5 等固定值，非真实统计</li>
                    <li><strong>示例性质:</strong> 波色、大小单双等策略使用示例号码，缺乏真实逻辑</li>
                    <li><strong>重复预测:</strong> 由于硬编码，每次预测结果相同</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>✅ 真实预测引擎改进</h2>
            <div class="solution">
                <h3>主要改进措施:</h3>
                <ul>
                    <li><strong>真实数据驱动:</strong> 基于1940条真实历史记录进行分析</li>
                    <li><strong>动态期号计算:</strong> 自动计算下一期号，无硬编码</li>
                    <li><strong>科学统计分析:</strong> 真实的频率、遗漏期数、极值分析</li>
                    <li><strong>多维度策略:</strong> 生肖、五行、号码统计三重分析</li>
                    <li><strong>智能融合:</strong> 基于置信度的加权融合算法</li>
                    <li><strong>动态预测:</strong> 每次运行产生不同的、基于最新数据的预测</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 技术特色</h2>
            <ul>
                <li><strong>数据完整性:</strong> 覆盖2020-2025年完整数据</li>
                <li><strong>算法科学性:</strong> 基于统计学和概率论的预测模型</li>
                <li><strong>结果可解释:</strong> 每个预测都有详细的分析依据</li>
                <li><strong>置信度评估:</strong> 科学的置信度计算方法</li>
                <li><strong>实时更新:</strong> 可随时加载最新数据进行预测</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>⚠️ 重要声明</h2>
            <p style="color: #dc3545; font-weight: bold;">
                本预测结果仅供学习和研究使用，基于历史数据的统计分析。
                彩票具有随机性，任何预测都不能保证准确性。
                请理性对待，合理投注，切勿沉迷。
            </p>
        </div>
    </div>
</body>
</html>
    """
    
    # 保存HTML报告
    with open('真实预测报告.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"\n📄 详细报告已生成: 真实预测报告.html")
    return result

def main():
    """主函数"""
    print("🔧 启动预测系统对比分析...")
    result = create_prediction_report()
    
    print(f"\n🎉 分析完成!")
    print(f"📊 真实预测置信度: {result['confidence']:.2%}")
    print(f"🎯 推荐号码数量: {len(result['final_numbers'])} 个")
    print(f"⏱️ 执行时间: {result['execution_time']:.3f} 秒")
    
    print(f"\n💡 建议:")
    print(f"   1. 使用真实预测引擎替代原系统")
    print(f"   2. 定期更新历史数据以提高准确性")
    print(f"   3. 结合多种策略进行综合判断")
    print(f"   4. 关注置信度较高的预测结果")

if __name__ == "__main__":
    main()
