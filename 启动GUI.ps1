# 六合彩智能预测系统 PowerShell 启动脚本
# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置窗口标题
$Host.UI.RawUI.WindowTitle = "六合彩智能预测系统 v1.0 - PowerShell启动器"

# 显示启动横幅
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                    六合彩智能预测系统 v1.0                    ║" -ForegroundColor Cyan
Write-Host "║                      PowerShell启动器                        ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

Write-Host "🚀 正在启动六合彩智能预测系统..." -ForegroundColor Green
Write-Host ""

# 检查Python环境
Write-Host "🔍 检查Python环境..." -ForegroundColor Yellow

$pythonCmd = $null
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        $pythonCmd = "python"
        Write-Host "✅ 找到Python: $pythonVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  python命令不可用，尝试py命令..." -ForegroundColor Yellow
}

if (-not $pythonCmd) {
    try {
        $pythonVersion = py --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            $pythonCmd = "py"
            Write-Host "✅ 找到Python: $pythonVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ py命令也不可用" -ForegroundColor Red
    }
}

if (-not $pythonCmd) {
    Write-Host "❌ 错误: 未找到Python环境" -ForegroundColor Red
    Write-Host ""
    Write-Host "请确保已安装Python 3.7+并添加到PATH环境变量中" -ForegroundColor Yellow
    Write-Host "下载地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按Enter键退出"
    exit 1
}

# 检查核心文件
Write-Host ""
Write-Host "📁 检查核心文件..." -ForegroundColor Yellow

$coreFiles = @(
    "gui_main.py",
    "prediction_engine.py", 
    "lottery_data.db",
    "lottery_data_20250717.csv"
)

$missingFiles = @()
foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "  ✅ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ 以下核心文件缺失:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "   - $file" -ForegroundColor Red
    }
    Write-Host ""
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "✅ 核心文件检查通过" -ForegroundColor Green

# 检查Python依赖
Write-Host ""
Write-Host "📦 检查Python依赖..." -ForegroundColor Yellow

$dependencies = @(
    @{Name="tkinter"; Required=$true},
    @{Name="yaml"; Required=$false},
    @{Name="pandas"; Required=$false},
    @{Name="numpy"; Required=$false},
    @{Name="openpyxl"; Required=$false}
)

foreach ($dep in $dependencies) {
    try {
        & $pythonCmd -c "import $($dep.Name)" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ $($dep.Name)" -ForegroundColor Green
        } else {
            if ($dep.Required) {
                Write-Host "  ❌ $($dep.Name) (必需)" -ForegroundColor Red
                Write-Host ""
                Write-Host "❌ 缺少必需的依赖包: $($dep.Name)" -ForegroundColor Red
                Read-Host "按Enter键退出"
                exit 1
            } else {
                Write-Host "  ⚠️  $($dep.Name) (可选，未安装)" -ForegroundColor Yellow
            }
        }
    } catch {
        if ($dep.Required) {
            Write-Host "  ❌ $($dep.Name) (检查失败)" -ForegroundColor Red
            exit 1
        } else {
            Write-Host "  ⚠️  $($dep.Name) (检查失败)" -ForegroundColor Yellow
        }
    }
}

# 创建日志目录
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
    Write-Host "📁 创建日志目录" -ForegroundColor Green
}

# 启动GUI
Write-Host ""
Write-Host "🎯 启动GUI界面..." -ForegroundColor Green
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║  GUI界面即将启动，请在弹出的窗口中进行操作                    ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "║  主要功能:                                                   ║" -ForegroundColor Cyan
Write-Host "║  • 🎯 智能预测 - 多策略融合预测                              ║" -ForegroundColor Cyan
Write-Host "║  • 📊 回测分析 - 历史数据回测验证                            ║" -ForegroundColor Cyan
Write-Host "║  • 📄 报告生成 - 详细分析报告                                ║" -ForegroundColor Cyan
Write-Host "║  • 🧠 高级分析 - Z-Score深度分析                             ║" -ForegroundColor Cyan
Write-Host "║  • 📈 实时监控 - 系统状态监控                                ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "║  关闭此窗口将同时关闭GUI程序                                 ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# 记录启动日志
$logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 系统启动"
Add-Content -Path "logs\system.log" -Value $logEntry

try {
    # 启动主程序
    & $pythonCmd gui_main.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ GUI程序已正常退出" -ForegroundColor Green
    } else {
        throw "GUI启动失败，错误代码: $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "❌ GUI启动失败: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 检查是否有其他程序占用相关资源" -ForegroundColor Yellow
    Write-Host "2. 确保所有依赖包已正确安装" -ForegroundColor Yellow
    Write-Host "3. 检查系统日志: logs\system.log" -ForegroundColor Yellow
    Write-Host "4. 尝试以管理员身份运行" -ForegroundColor Yellow
    Write-Host ""
    
    # 尝试启动备用GUI
    Write-Host "🔄 尝试启动备用GUI界面..." -ForegroundColor Yellow
    
    $backupGuis = @("minimal_gui.py", "simple_gui.py")
    $backupStarted = $false
    
    foreach ($gui in $backupGuis) {
        if (Test-Path $gui) {
            try {
                Write-Host "启动 $gui..." -ForegroundColor Yellow
                & $pythonCmd $gui
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ 备用GUI启动成功" -ForegroundColor Green
                    $backupStarted = $true
                    break
                }
            } catch {
                Write-Host "备用GUI $gui 启动失败" -ForegroundColor Red
            }
        }
    }
    
    if (-not $backupStarted) {
        # 尝试启动Web版本
        Write-Host "🌐 尝试启动Web版本..." -ForegroundColor Yellow
        if (Test-Path "prediction_demo.html") {
            Start-Process "prediction_demo.html"
            Write-Host "✅ Web版本已在浏览器中打开" -ForegroundColor Green
        } else {
            Write-Host "❌ 所有启动方式均失败" -ForegroundColor Red
        }
    }
}

# 记录关闭日志
$logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - 系统关闭"
Add-Content -Path "logs\system.log" -Value $logEntry

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                    感谢使用六合彩智能预测系统                 ║" -ForegroundColor Cyan
Write-Host "║                                                              ║" -ForegroundColor Cyan
Write-Host "║  如有问题或建议，请查看:                                     ║" -ForegroundColor Cyan
Write-Host "║  • 系统日志: logs\system.log                                ║" -ForegroundColor Cyan
Write-Host "║  • 使用说明: 系统使用说明.md                                 ║" -ForegroundColor Cyan
Write-Host "║  • 功能说明: GUI_功能说明.md                                 ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

Read-Host "按Enter键退出"
