#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维生肖分析引擎
"""

import sqlite3
import json
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from multi_dimensional_zodiac_db import MultiDimensionalZodiacDB
from dynamic_mapping_system import DynamicMappingSystem
from data_attributes import HistoryDataManager

class MultiDimensionalZodiacAnalyzer:
    """多维生肖分析引擎"""
    
    def __init__(self):
        self.db = MultiDimensionalZodiacDB()
        self.mapping_system = DynamicMappingSystem()
        self.data_manager = HistoryDataManager()
        
        # 分析参数
        self.extreme_threshold = 0.9  # 极限阈值（90%历史最大值）
        self.reback_threshold = 0.7   # 回补阈值
        self.analysis_periods = 100   # 分析期数
        
    def analyze_historical_data(self) -> Dict[str, Any]:
        """分析历史数据，构建多维生肖特征"""
        print("🔍 开始多维生肖历史数据分析...")
        
        # 1. 加载历史数据
        historical_data = self.data_manager.load_data()
        if not historical_data:
            print("❌ 历史数据加载失败")
            return {}
        
        print(f"📊 加载 {len(historical_data)} 条历史记录")
        
        # 2. 获取多维生肖分类
        dimensions = self.db.get_zodiac_dimensions()
        print(f"📋 多维分类: {len(dimensions)} 个维度")
        
        # 3. 分析每期的多维特征
        period_features = {}
        dimension_stats = defaultdict(lambda: {
            'hit_count': 0,
            'miss_count': 0,
            'current_miss': 0,
            'max_miss': 0,
            'hit_periods': [],
            'miss_streaks': []
        })
        
        for i, record in enumerate(historical_data):
            period_number = record.get('period_number', '')
            special_code = record.get('special_code', 0)
            
            if not special_code:
                continue
            
            # 获取特码生肖
            try:
                zodiac = self.mapping_system.get_zodiac_for_number(special_code, period_number)
            except:
                continue
            
            # 构建当期多维特征
            period_feature = self._build_period_features(zodiac, dimensions)
            period_features[period_number] = period_feature
            
            # 更新维度统计
            self._update_dimension_stats(dimension_stats, period_feature, period_number)
        
        # 4. 计算极限统计
        extreme_stats = self._calculate_extreme_stats(dimension_stats)
        
        # 5. 识别回补模式
        reback_patterns = self._identify_reback_patterns(dimension_stats)
        
        return {
            'period_features': period_features,
            'dimension_stats': dict(dimension_stats),
            'extreme_stats': extreme_stats,
            'reback_patterns': reback_patterns,
            'analysis_summary': self._generate_analysis_summary(dimension_stats, extreme_stats)
        }
    
    def _build_period_features(self, zodiac: str, dimensions: Dict[str, List[str]]) -> Dict[str, bool]:
        """构建单期多维特征向量"""
        features = {}
        
        for dim_key, dim_zodiacs in dimensions.items():
            # 检查当期生肖是否命中该维度
            features[dim_key] = zodiac in dim_zodiacs
        
        return features
    
    def _update_dimension_stats(self, stats: Dict, features: Dict[str, bool], period: str):
        """更新维度统计"""
        for dim_key, is_hit in features.items():
            stat = stats[dim_key]
            
            if is_hit:
                stat['hit_count'] += 1
                stat['hit_periods'].append(period)
                
                # 记录遗漏周期
                if stat['current_miss'] > 0:
                    stat['miss_streaks'].append(stat['current_miss'])
                    stat['max_miss'] = max(stat['max_miss'], stat['current_miss'])
                
                stat['current_miss'] = 0
            else:
                stat['miss_count'] += 1
                stat['current_miss'] += 1
    
    def _calculate_extreme_stats(self, dimension_stats: Dict) -> Dict[str, Any]:
        """计算极限统计"""
        extreme_stats = {}
        
        for dim_key, stat in dimension_stats.items():
            total_periods = stat['hit_count'] + stat['miss_count']
            hit_rate = stat['hit_count'] / total_periods if total_periods > 0 else 0
            
            # 计算极限指标
            extreme_ratio = stat['current_miss'] / stat['max_miss'] if stat['max_miss'] > 0 else 0
            is_near_extreme = extreme_ratio >= self.extreme_threshold
            is_extreme = stat['current_miss'] >= stat['max_miss']
            
            # 计算回补概率
            avg_miss = sum(stat['miss_streaks']) / len(stat['miss_streaks']) if stat['miss_streaks'] else 0
            reback_probability = min(1.0, extreme_ratio * 1.2) if extreme_ratio > 0.5 else hit_rate
            
            extreme_stats[dim_key] = {
                'hit_rate': hit_rate,
                'current_miss': stat['current_miss'],
                'max_miss': stat['max_miss'],
                'extreme_ratio': extreme_ratio,
                'is_near_extreme': is_near_extreme,
                'is_extreme': is_extreme,
                'avg_miss': avg_miss,
                'reback_probability': reback_probability,
                'urgency_level': self._get_urgency_level(extreme_ratio, is_extreme)
            }
        
        return extreme_stats
    
    def _get_urgency_level(self, extreme_ratio: float, is_extreme: bool) -> str:
        """获取紧迫度等级"""
        if is_extreme:
            return "🔥 超极限"
        elif extreme_ratio >= 0.9:
            return "⚡ 极限警戒"
        elif extreme_ratio >= 0.7:
            return "💧 临界回补"
        elif extreme_ratio >= 0.5:
            return "❄️ 正常偏高"
        else:
            return "✅ 正常范围"
    
    def _identify_reback_patterns(self, dimension_stats: Dict) -> Dict[str, Any]:
        """识别回补模式"""
        reback_patterns = {}
        
        for dim_key, stat in dimension_stats.items():
            if len(stat['hit_periods']) < 3:
                continue
            
            # 分析连续命中模式
            consecutive_hits = self._find_consecutive_hits(stat['hit_periods'])
            
            # 分析回补强度
            reback_intensity = self._calculate_reback_intensity(stat['miss_streaks'])
            
            reback_patterns[dim_key] = {
                'consecutive_hits': consecutive_hits,
                'reback_intensity': reback_intensity,
                'has_reback_pattern': len(consecutive_hits) > 0 and reback_intensity > 0.5
            }
        
        return reback_patterns
    
    def _find_consecutive_hits(self, hit_periods: List[str]) -> List[int]:
        """查找连续命中模式"""
        if len(hit_periods) < 2:
            return []
        
        consecutive_counts = []
        current_count = 1
        
        # 简化：假设期号是连续的数字
        for i in range(1, len(hit_periods)):
            try:
                prev_num = int(hit_periods[i-1][-3:])  # 取后3位
                curr_num = int(hit_periods[i][-3:])
                
                if curr_num == prev_num + 1:
                    current_count += 1
                else:
                    if current_count > 1:
                        consecutive_counts.append(current_count)
                    current_count = 1
            except:
                continue
        
        if current_count > 1:
            consecutive_counts.append(current_count)
        
        return consecutive_counts
    
    def _calculate_reback_intensity(self, miss_streaks: List[int]) -> float:
        """计算回补强度"""
        if not miss_streaks:
            return 0.0
        
        # 回补强度 = 长遗漏后的快速回补频率
        long_misses = [m for m in miss_streaks if m >= 5]
        if not long_misses:
            return 0.0
        
        avg_long_miss = sum(long_misses) / len(long_misses)
        intensity = min(1.0, len(long_misses) / len(miss_streaks) * (avg_long_miss / 10))
        
        return intensity
    
    def _generate_analysis_summary(self, dimension_stats: Dict, extreme_stats: Dict) -> str:
        """生成分析摘要"""
        total_dimensions = len(dimension_stats)
        extreme_dimensions = sum(1 for stat in extreme_stats.values() if stat['is_near_extreme'] or stat['is_extreme'])
        
        summary = f"多维生肖分析摘要:\n"
        summary += f"- 总维度数: {total_dimensions}\n"
        summary += f"- 极限/临界维度: {extreme_dimensions}\n"
        
        # 找出最紧迫的维度
        urgent_dims = sorted(
            [(k, v) for k, v in extreme_stats.items()],
            key=lambda x: x[1]['extreme_ratio'],
            reverse=True
        )[:5]
        
        if urgent_dims:
            summary += f"- 最紧迫维度:\n"
            for dim_key, stat in urgent_dims:
                summary += f"  {dim_key}: {stat['urgency_level']} (遗漏{stat['current_miss']}期)\n"
        
        return summary
    
    def get_current_predictions(self, target_period: str) -> Dict[str, Any]:
        """获取当前预测"""
        print(f"🎯 生成期号 {target_period} 的多维生肖预测...")
        
        # 分析历史数据
        analysis_result = self.analyze_historical_data()
        extreme_stats = analysis_result['extreme_stats']
        
        # 筛选极限/临界维度
        urgent_dimensions = {}
        for dim_key, stat in extreme_stats.items():
            if stat['is_near_extreme'] or stat['is_extreme']:
                urgent_dimensions[dim_key] = stat
        
        # 生成预测候选
        predictions = self._generate_predictions_from_urgent_dimensions(urgent_dimensions)
        
        return {
            'target_period': target_period,
            'urgent_dimensions': urgent_dimensions,
            'predictions': predictions,
            'analysis_summary': analysis_result['analysis_summary']
        }
    
    def _generate_predictions_from_urgent_dimensions(self, urgent_dimensions: Dict) -> Dict[str, Any]:
        """从紧迫维度生成预测"""
        # 获取所有维度定义
        all_dimensions = self.db.get_zodiac_dimensions()
        
        # 统计生肖出现频率
        zodiac_scores = defaultdict(float)
        
        for dim_key, stat in urgent_dimensions.items():
            if dim_key in all_dimensions:
                zodiacs = all_dimensions[dim_key]
                weight = stat['reback_probability'] * (1 + stat['extreme_ratio'])
                
                for zodiac in zodiacs:
                    zodiac_scores[zodiac] += weight
        
        # 排序并选择top候选
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'top_zodiacs': sorted_zodiacs[:6],  # 前6个生肖
            'zodiac_scores': dict(zodiac_scores),
            'prediction_logic': "基于极限/临界维度的回补概率加权"
        }

def test_multi_dimensional_analyzer():
    """测试多维生肖分析器"""
    print("🧪 测试多维生肖分析器")
    print("="*50)
    
    analyzer = MultiDimensionalZodiacAnalyzer()
    
    # 获取当前预测
    predictions = analyzer.get_current_predictions("2025199")
    
    print(f"🎯 预测结果:")
    print(f"目标期号: {predictions['target_period']}")
    print(f"紧迫维度数: {len(predictions['urgent_dimensions'])}")
    
    print(f"\n📊 前6个候选生肖:")
    for i, (zodiac, score) in enumerate(predictions['predictions']['top_zodiacs'], 1):
        print(f"   {i}. {zodiac}: {score:.3f}")
    
    print(f"\n📋 分析摘要:")
    print(predictions['analysis_summary'])
    
    print(f"\n✅ 多维生肖分析器测试完成")

if __name__ == "__main__":
    test_multi_dimensional_analyzer()
