#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测系统监测脚本
监测预测运行状态和数据库连接
"""

import os
import sys
import time
import sqlite3
import csv
import json
import traceback
from datetime import datetime


class PredictionMonitor:
    """预测系统监测器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.log = []
        self.results = {}
    
    def log_info(self, message):
        """记录信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        self.log.append(log_entry)
    
    def log_error(self, message, error=None):
        """记录错误"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        if error:
            log_entry = f"[{timestamp}] ❌ {message}: {error}"
        else:
            log_entry = f"[{timestamp}] ❌ {message}"
        print(log_entry)
        self.log.append(log_entry)
    
    def check_database_connection(self):
        """检查数据库连接"""
        self.log_info("🔍 检查数据库连接...")
        
        try:
            conn = sqlite3.connect('lottery_data.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            self.log_info(f"✅ 数据库连接成功，发现 {len(tables)} 个表")
            
            # 检查主要数据表
            if 'lottery_records' in tables:
                cursor.execute("SELECT COUNT(*) FROM lottery_records")
                count = cursor.fetchone()[0]
                self.log_info(f"📊 lottery_records 表: {count} 条记录")
                
                if count > 0:
                    cursor.execute("SELECT period_number, draw_date, zodiac, five_element FROM lottery_records ORDER BY period_number DESC LIMIT 3")
                    recent_records = cursor.fetchall()
                    self.log_info("📅 最近3条记录:")
                    for record in recent_records:
                        self.log_info(f"   期号:{record[0]} 日期:{record[1]} 生肖:{record[2]} 五行:{record[3]}")
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_error("数据库连接失败", e)
            return False
    
    def check_csv_data(self):
        """检查CSV数据文件"""
        self.log_info("🔍 检查CSV数据文件...")
        
        try:
            if not os.path.exists('lottery_data_20250717.csv'):
                self.log_error("CSV文件不存在")
                return False
            
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
            self.log_info(f"✅ CSV文件读取成功，{len(rows)} 条记录")
            
            # 检查数据格式
            if rows:
                first_row = rows[0]
                last_row = rows[-1]
                
                # 处理BOM字符
                period_key = 'period_number'
                if '\ufeffperiod_number' in first_row:
                    period_key = '\ufeffperiod_number'
                
                self.log_info(f"📅 数据范围: {first_row[period_key]} 到 {last_row[period_key]}")
                self.log_info(f"🔍 数据字段: {list(first_row.keys())}")
                
                # 检查最近几条数据
                self.log_info("📊 最近3条CSV数据:")
                for i, row in enumerate(rows[-3:]):
                    self.log_info(f"   {row[period_key]} {row['draw_date']} {row['zodiac']} {row['five_element']}")
            
            return True
            
        except Exception as e:
            self.log_error("CSV文件检查失败", e)
            return False
    
    def test_module_imports(self):
        """测试模块导入"""
        self.log_info("🔍 测试核心模块导入...")
        
        modules = {
            'combo_generator': 'ComboGenerator',
            'extreme_stat_tracker': 'ExtremeStatTracker', 
            'strategy_scorer': 'StrategyScorerAndFusionEngine'
        }
        
        imported_modules = {}
        
        for module_name, class_name in modules.items():
            try:
                module = __import__(module_name)
                cls = getattr(module, class_name)
                imported_modules[module_name] = {'module': module, 'class': cls}
                self.log_info(f"✅ {module_name}.{class_name} 导入成功")
            except Exception as e:
                self.log_error(f"{module_name} 导入失败", e)
                return None
        
        return imported_modules
    
    def run_prediction_workflow(self):
        """运行预测工作流程"""
        self.log_info("🚀 开始运行预测工作流程...")
        
        try:
            # 1. 导入模块
            modules = self.test_module_imports()
            if not modules:
                return False
            
            # 2. 初始化组合生成器
            self.log_info("🔧 初始化组合生成器...")
            combo_gen = modules['combo_generator']['class']()
            
            # 3. 生成组合
            self.log_info("🎲 生成预测组合...")
            shengxiao_4_combos = combo_gen.generate_shengxiao_4()
            wuxing_2_combos = combo_gen.generate_wuxing_2()
            all_combos = shengxiao_4_combos + wuxing_2_combos
            
            self.log_info(f"✅ 组合生成完成:")
            self.log_info(f"   生肖4组合: {len(shengxiao_4_combos)} 个")
            self.log_info(f"   五行2组合: {len(wuxing_2_combos)} 个")
            self.log_info(f"   总计: {len(all_combos)} 个组合")
            
            # 4. 加载历史数据
            self.log_info("📊 加载历史数据...")
            history_draws = self.load_historical_data()
            if not history_draws:
                self.log_error("历史数据加载失败")
                return False
            
            self.log_info(f"✅ 历史数据加载成功: {len(history_draws)} 条记录")
            
            # 5. 运行统计追踪 (只测试前10个组合以节省时间)
            self.log_info("📈 运行统计追踪...")
            test_combos = all_combos[:10]  # 只测试前10个组合
            
            tracker_class = modules['extreme_stat_tracker']['class']
            tracker = tracker_class(db_connection=None, combo_list=test_combos, history_draws=history_draws)
            tracker.run_tracking()
            
            self.log_info(f"✅ 统计追踪完成: 处理了 {len(test_combos)} 个组合")
            
            # 6. 策略评分和预测融合
            self.log_info("🎯 执行策略评分和预测融合...")
            
            # 模拟策略数据
            strategies = {
                "shengxiao_4_extreme": {
                    "hit_count": 5,
                    "avg_omit_before_hit": 8,
                    "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
                },
                "wuxing_2_hot": {
                    "hit_count": 10,
                    "avg_omit_before_hit": 4,
                    "predicted_numbers": [5, 10, 15, 20, 25, 30, 35]
                }
            }
            
            scorer_class = modules['strategy_scorer']['class']
            scorer = scorer_class(strategies, history_draws)
            strategy_scores = scorer.calculate_scores()
            
            self.log_info(f"✅ 策略评分完成: {len(strategy_scores)} 个策略")
            
            # 显示策略评分
            for strat_id, score_data in strategy_scores.items():
                self.log_info(f"   {strat_id}: 评分={score_data['score']:.3f}, 命中率={score_data['hit_rate']:.3f}")
            
            # 7. 预测融合
            predictions_to_fuse = [
                {
                    "strategy_id": strat_id,
                    "weight": strategy_scores[strat_id]["score"],
                    "numbers": strat_data["predicted_numbers"]
                }
                for strat_id, strat_data in strategies.items()
            ]
            
            final_prediction = scorer.fuse_predictions(predictions_to_fuse, method='weighted_union', top_n=12)
            
            self.log_info("🎉 预测融合完成!")
            self.log_info(f"🎯 最终预测号码: {sorted(final_prediction)}")
            
            # 保存结果
            self.results = {
                'combos_generated': len(all_combos),
                'history_records': len(history_draws),
                'strategies_scored': len(strategy_scores),
                'final_prediction': sorted(final_prediction),
                'strategy_scores': strategy_scores
            }
            
            return True
            
        except Exception as e:
            self.log_error("预测工作流程执行失败", e)
            self.log_info(f"详细错误: {traceback.format_exc()}")
            return False
    
    def load_historical_data(self):
        """加载历史数据"""
        try:
            history = []
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 处理BOM字符
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    history.append({
                        'period': row[period_key],
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
            
            # 反向排序 (最新的在前面)
            return list(reversed(history))
            
        except Exception as e:
            self.log_error("历史数据加载失败", e)
            return []
    
    def generate_report(self):
        """生成监测报告"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        report = {
            'timestamp': end_time.isoformat(),
            'duration_seconds': duration,
            'log': self.log,
            'results': self.results
        }
        
        # 保存到文件
        with open('prediction_monitor_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log_info(f"📄 监测报告已保存: prediction_monitor_report.json")
        self.log_info(f"⏱️ 总耗时: {duration:.2f} 秒")
    
    def run_full_monitor(self):
        """运行完整监测"""
        self.log_info("🔧 六合彩预测系统监测开始")
        self.log_info("=" * 50)
        
        # 1. 检查数据库连接
        db_ok = self.check_database_connection()
        
        # 2. 检查CSV数据
        csv_ok = self.check_csv_data()
        
        # 3. 运行预测工作流程
        if db_ok or csv_ok:  # 至少有一个数据源可用
            prediction_ok = self.run_prediction_workflow()
        else:
            self.log_error("数据源不可用，跳过预测流程")
            prediction_ok = False
        
        # 4. 生成总结
        self.log_info("\n📋 监测总结")
        self.log_info("=" * 30)
        self.log_info(f"🗄️ 数据库连接: {'✅ 正常' if db_ok else '❌ 异常'}")
        self.log_info(f"📊 CSV数据: {'✅ 正常' if csv_ok else '❌ 异常'}")
        self.log_info(f"🎯 预测流程: {'✅ 正常' if prediction_ok else '❌ 异常'}")
        
        if prediction_ok:
            self.log_info(f"🎉 系统运行正常！预测结果: {self.results.get('final_prediction', [])}")
        else:
            self.log_info("⚠️ 系统存在问题，需要检查")
        
        # 5. 生成报告
        self.generate_report()
        
        return prediction_ok


def main():
    """主函数"""
    monitor = PredictionMonitor()
    success = monitor.run_full_monitor()
    
    if success:
        print("\n🎉 监测完成：系统运行正常！")
    else:
        print("\n⚠️ 监测完成：发现问题，请查看日志")
    
    return success


if __name__ == "__main__":
    main()
