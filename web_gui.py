#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩预测系统 - Web界面
基于HTTP服务器的简单Web GUI
"""

import http.server
import socketserver
import json
import urllib.parse
import threading
import webbrowser
from datetime import datetime
import csv

# 导入核心模块
from combo_generator import ComboGenerator
from extreme_stat_tracker import ExtremeStatTracker
from strategy_scorer import StrategyScorerAndFusionEngine


class PredictionWebHandler(http.server.SimpleHTTPRequestHandler):
    """Web请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 初始化数据
        self.history_data = []
        self.combo_data = {}
        self.prediction_results = {}
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path == '/api/status':
            self.serve_status()
        elif self.path == '/api/load_data':
            self.serve_load_data()
        elif self.path == '/api/generate_combos':
            self.serve_generate_combos()
        elif self.path == '/api/run_prediction':
            self.serve_run_prediction()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """提供主页面"""
        html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六合彩智能预测系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .panel { border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .control-panel { width: 300px; float: left; }
        .result-panel { margin-left: 320px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .button:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 5px; border-radius: 3px; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log-area { height: 200px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
        .result-area { height: 300px; overflow-y: scroll; border: 1px solid #ddd; padding: 10px; background: #f8f9fa; white-space: pre-wrap; }
        .clear { clear: both; }
        .prediction-numbers { font-size: 18px; font-weight: bold; color: #dc3545; text-align: center; padding: 20px; background: #fff3cd; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 六合彩智能预测系统</h1>
            <p>基于统计分析和策略融合的智能预测平台</p>
        </div>
        
        <div class="control-panel panel">
            <h3>📊 控制面板</h3>
            
            <div class="status info" id="dataStatus">
                <strong>数据状态:</strong> <span id="dataStatusText">未加载</span><br>
                <strong>历史记录:</strong> <span id="recordCount">0 条</span>
            </div>
            
            <hr>
            
            <button class="button" onclick="loadData()">📥 加载数据</button>
            <button class="button" onclick="generateCombos()" id="genCombosBtn" disabled>🎲 生成组合</button>
            <button class="button" onclick="runPrediction()" id="runPredBtn" disabled>🚀 运行预测</button>
            <button class="button" onclick="clearLog()">🗑️ 清空日志</button>
            
            <hr>
            
            <div>
                <label><strong>预测参数:</strong></label><br>
                <label>输出号码数: </label>
                <input type="number" id="topN" value="12" min="1" max="49" style="width: 60px;">
            </div>
        </div>
        
        <div class="result-panel panel">
            <h3>🎯 预测结果</h3>
            <div id="predictionNumbers" class="prediction-numbers" style="display: none;">
                最终预测号码: <span id="finalNumbers"></span>
            </div>
            <div id="resultArea" class="result-area">等待预测结果...</div>
        </div>
        
        <div class="clear"></div>
        
        <div class="panel">
            <h3>📝 运行日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        let dataLoaded = false;
        let combosGenerated = false;
        
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        function updateStatus(status, count = 0) {
            const statusElement = document.getElementById('dataStatus');
            const statusText = document.getElementById('dataStatusText');
            const recordCount = document.getElementById('recordCount');
            
            if (status === 'loaded') {
                statusElement.className = 'status success';
                statusText.textContent = '已加载';
                recordCount.textContent = `${count} 条`;
                dataLoaded = true;
                document.getElementById('genCombosBtn').disabled = false;
            } else if (status === 'error') {
                statusElement.className = 'status error';
                statusText.textContent = '加载失败';
            }
        }
        
        async function loadData() {
            log('🔄 开始加载历史数据...');
            try {
                const response = await fetch('/api/load_data');
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 数据加载成功: ${result.count} 条记录`);
                    log(`📅 数据范围: ${result.range}`);
                    updateStatus('loaded', result.count);
                } else {
                    log(`❌ 数据加载失败: ${result.error}`);
                    updateStatus('error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error}`);
                updateStatus('error');
            }
        }
        
        async function generateCombos() {
            if (!dataLoaded) {
                alert('请先加载数据');
                return;
            }
            
            log('🎲 开始生成预测组合...');
            try {
                const response = await fetch('/api/generate_combos');
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 组合生成成功:`);
                    log(`   - 生肖4组合: ${result.shengxiao_4} 个`);
                    log(`   - 五行2组合: ${result.wuxing_2} 个`);
                    log(`   - 色波组合: ${result.sebo} 个`);
                    log(`   总计: ${result.total} 个组合`);
                    combosGenerated = true;
                    document.getElementById('runPredBtn').disabled = false;
                } else {
                    log(`❌ 组合生成失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error}`);
            }
        }
        
        async function runPrediction() {
            if (!dataLoaded || !combosGenerated) {
                alert('请先加载数据并生成组合');
                return;
            }
            
            const topN = document.getElementById('topN').value;
            log('🚀 开始运行预测流程...');
            
            try {
                const response = await fetch(`/api/run_prediction?top_n=${topN}`);
                const result = await response.json();
                
                if (result.success) {
                    log('🎉 预测完成!');
                    
                    // 显示预测结果
                    const resultArea = document.getElementById('resultArea');
                    resultArea.textContent = result.detailed_result;
                    
                    // 显示预测号码
                    const numbersDiv = document.getElementById('predictionNumbers');
                    const finalNumbers = document.getElementById('finalNumbers');
                    finalNumbers.textContent = result.final_prediction.join(', ');
                    numbersDiv.style.display = 'block';
                    
                } else {
                    log(`❌ 预测失败: ${result.error}`);
                }
            } catch (error) {
                log(`❌ 请求失败: ${error}`);
            }
        }
        
        // 页面加载完成后自动加载数据
        window.onload = function() {
            log('🔧 六合彩预测系统已启动');
            log('💡 点击"加载数据"开始使用系统');
        };
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))
    
    def serve_status(self):
        """提供状态信息"""
        status = {
            'data_loaded': len(self.history_data) > 0,
            'record_count': len(self.history_data),
            'combos_generated': len(self.combo_data) > 0
        }
        self.send_json_response(status)
    
    def serve_load_data(self):
        """加载历史数据"""
        try:
            history = []
            with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    period_key = 'period_number'
                    if '\ufeffperiod_number' in row:
                        period_key = '\ufeffperiod_number'
                    
                    history.append({
                        'period': row[period_key],
                        'zodiac': row['zodiac'],
                        'five_element': row['five_element']
                    })
            
            self.history_data = list(reversed(history))
            
            response = {
                'success': True,
                'count': len(self.history_data),
                'range': f"{history[0][period_key]} 到 {history[-1]['period']}"
            }
            
        except Exception as e:
            response = {'success': False, 'error': str(e)}
        
        self.send_json_response(response)
    
    def serve_generate_combos(self):
        """生成组合"""
        try:
            combo_gen = ComboGenerator()
            
            shengxiao_4 = combo_gen.generate_shengxiao_4()
            wuxing_2 = combo_gen.generate_wuxing_2()
            sebo = combo_gen.generate_sebo()
            
            self.combo_data = {
                'shengxiao_4': shengxiao_4,
                'wuxing_2': wuxing_2,
                'sebo': sebo
            }
            
            response = {
                'success': True,
                'shengxiao_4': len(shengxiao_4),
                'wuxing_2': len(wuxing_2),
                'sebo': len(sebo),
                'total': len(shengxiao_4) + len(wuxing_2) + len(sebo)
            }
            
        except Exception as e:
            response = {'success': False, 'error': str(e)}
        
        self.send_json_response(response)
    
    def serve_run_prediction(self):
        """运行预测"""
        try:
            # 解析参数
            query = urllib.parse.urlparse(self.path).query
            params = urllib.parse.parse_qs(query)
            top_n = int(params.get('top_n', [12])[0])
            
            # 运行预测流程
            all_combos = (self.combo_data['shengxiao_4'][:10] + 
                         self.combo_data['wuxing_2'][:5])
            
            # 统计追踪
            tracker = ExtremeStatTracker(None, all_combos, self.history_data)
            tracker.run_tracking()
            
            # 策略定义
            strategies = {
                "生肖4极值策略": {
                    "hit_count": 5,
                    "avg_omit_before_hit": 8,
                    "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
                },
                "五行2热门策略": {
                    "hit_count": 10,
                    "avg_omit_before_hit": 4,
                    "predicted_numbers": [5, 10, 15, 20, 25, 30, 35]
                },
                "色波均衡策略": {
                    "hit_count": 7,
                    "avg_omit_before_hit": 6,
                    "predicted_numbers": [2, 8, 14, 19, 26, 33, 40]
                }
            }
            
            # 策略评分
            scorer = StrategyScorerAndFusionEngine(strategies, self.history_data)
            strategy_scores = scorer.calculate_scores()
            
            # 预测融合
            predictions_to_fuse = [
                {
                    "strategy_id": strat_id,
                    "weight": strategy_scores[strat_id]["score"],
                    "numbers": strat_data["predicted_numbers"]
                }
                for strat_id, strat_data in strategies.items()
            ]
            
            final_prediction = scorer.fuse_predictions(predictions_to_fuse, 
                                                     method='weighted_union', 
                                                     top_n=top_n)
            
            # 格式化详细结果
            detailed_result = f"六合彩智能预测结果\\n"
            detailed_result += f"=" * 40 + "\\n"
            detailed_result += f"预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n"
            
            detailed_result += f"策略评分详情:\\n"
            detailed_result += f"-" * 30 + "\\n"
            for strat_id, score_data in strategy_scores.items():
                detailed_result += f"{strat_id}:\\n"
                detailed_result += f"  综合评分: {score_data['score']:.4f}\\n"
                detailed_result += f"  命中率: {score_data['hit_rate']:.4f}\\n"
                detailed_result += f"  平均遗漏: {score_data['avg_omit_before_hit']:.1f}\\n\\n"
            
            detailed_result += f"最终预测号码 (Top {len(final_prediction)}):\\n"
            detailed_result += f"-" * 30 + "\\n"
            detailed_result += f"{', '.join(map(str, sorted(final_prediction)))}\\n"
            
            response = {
                'success': True,
                'final_prediction': sorted(final_prediction),
                'strategy_scores': strategy_scores,
                'detailed_result': detailed_result
            }
            
        except Exception as e:
            response = {'success': False, 'error': str(e)}
        
        self.send_json_response(response)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))


def start_web_server(port=8888):
    """启动Web服务器"""
    try:
        with socketserver.TCPServer(("", port), PredictionWebHandler) as httpd:
            print(f"🌐 六合彩预测系统Web界面已启动")
            print(f"📍 访问地址: http://localhost:{port}")
            print(f"🔧 服务器运行在端口 {port}")
            print(f"💡 在浏览器中打开上述地址即可使用系统")
            print(f"⏹️ 按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            threading.Timer(1.0, lambda: webbrowser.open(f'http://localhost:{port}')).start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


if __name__ == "__main__":
    start_web_server()
