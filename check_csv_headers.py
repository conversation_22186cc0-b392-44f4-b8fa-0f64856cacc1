#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查CSV文件的列名和BOM字符问题
"""

import csv

def check_csv_headers():
    """检查CSV文件头部"""
    print("📊 检查CSV文件头部和数据")
    print("="*50)
    
    csv_file = 'lottery_data_20250717.csv'
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # 读取原始第一行
            first_line = f.readline()
            print(f"原始第一行: {repr(first_line)}")
            
            # 检查BOM
            if first_line.startswith('\ufeff'):
                print("⚠️ 发现BOM字符")
            else:
                print("✅ 无BOM字符")
        
        # 使用csv.DictReader读取
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            print(f"\n📋 CSV列名:")
            for i, fieldname in enumerate(reader.fieldnames, 1):
                print(f"   {i}. {repr(fieldname)}")
            
            # 读取前3行数据
            print(f"\n📊 前3行数据:")
            for i, row in enumerate(reader, 1):
                if i <= 3:
                    print(f"   行{i}: {dict(row)}")
                else:
                    break
        
        # 测试数据管理器
        print(f"\n🔧 测试修复后的数据管理器:")
        from data_attributes import HistoryDataManager
        
        manager = HistoryDataManager()
        data = manager.load_data()
        
        if data:
            print(f"✅ 数据加载成功: {len(data)} 条记录")
            print(f"📋 第一条记录: {data[0]}")
        else:
            print(f"❌ 数据加载仍然为空")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_csv_headers()
