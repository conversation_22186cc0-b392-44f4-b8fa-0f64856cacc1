import tkinter as tk
from tkinter import ttk, messagebox
from config_manager import Config<PERSON><PERSON><PERSON>
from typing import Any, Callable, Dict
import json

class ConfigDialog:
    def __init__(self, parent):
        """
        配置对话框
        Args:
            parent: 父窗口
        """
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("系统设置")
        self.dialog.geometry("800x600")

        # 获取配置管理器实例
        self.config_manager = ConfigManager()

        # 创建界面
        self._create_widgets()

        # 加载配置
        self._load_config()

    def _create_widgets(self):
        """创建界面组件"""
        # 创建标签页
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建各个设置页
        self.app_frame = self._create_app_settings()
        self.data_frame = self._create_data_settings()
        self.analysis_frame = self._create_analysis_settings()
        self.gui_frame = self._create_gui_settings()

        # 添加标签页
        self.notebook.add(self.app_frame, text="基本设置")
        self.notebook.add(self.data_frame, text="数据设置")
        self.notebook.add(self.analysis_frame, text="分析设置")
        self.notebook.add(self.gui_frame, text="界面设置")

        # 创建按钮框
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill='x', padx=5, pady=5)

        ttk.Button(button_frame, text="保存",
                  command=self._save_config).pack(side='right', padx=5)
        ttk.Button(button_frame, text="取消",
                  command=self.dialog.destroy).pack(side='right', padx=5)
        ttk.Button(button_frame, text="恢复默认",
                  command=self._reset_config).pack(side='left', padx=5)

    def _create_app_settings(self) -> ttk.Frame:
        """创建基本设置页"""
        frame = ttk.Frame(self.notebook)
        frame.columnconfigure(1, weight=1)

        # 主题设置
        row = 0
        ttk.Label(frame, text="界面主题:").grid(row=row, column=0, padx=5, pady=5)
        self.theme_var = tk.StringVar()
        theme_combo = ttk.Combobox(frame, textvariable=self.theme_var)
        theme_combo['values'] = ('light', 'dark')
        theme_combo.grid(row=row, column=1, sticky='ew', padx=5, pady=5)

        # 语言设置
        row += 1
        ttk.Label(frame, text="界面语言:").grid(row=row, column=0, padx=5, pady=5)
        self.language_var = tk.StringVar()
        lang_combo = ttk.Combobox(frame, textvariable=self.language_var)
        lang_combo['values'] = ('zh_CN', 'en_US')
        lang_combo.grid(row=row, column=1, sticky='ew', padx=5, pady=5)

        # 自动保存
        row += 1
        self.auto_save_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="自动保存",
                       variable=self.auto_save_var).grid(row=row, column=0,
                       columnspan=2, sticky='w', padx=5, pady=5)

        # 自动备份
        row += 1
        self.backup_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="启用自动备份",
                       variable=self.backup_var).grid(row=row, column=0,
                       columnspan=2, sticky='w', padx=5, pady=5)

        return frame

    def _create_data_settings(self) -> ttk.Frame:
        """创建数据设置页"""
        frame = ttk.Frame(self.notebook)
        frame.columnconfigure(1, weight=1)

        # 数据库路径
        row = 0
        ttk.Label(frame, text="数据库路径:").grid(row=row, column=0, padx=5, pady=5)
        self.db_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.db_path_var).grid(row=row, column=1,
                 sticky='ew', padx=5, pady=5)

        # 导入目录
        row += 1
        ttk.Label(frame, text="导入目录:").grid(row=row, column=0, padx=5, pady=5)
        self.import_path_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.import_path_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 自动更新设置
        row += 1
        self.auto_update_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="自动更新数据",
                       variable=self.auto_update_var).grid(row=row, column=0,
                       columnspan=2, sticky='w', padx=5, pady=5)

        # 更新间隔
        row += 1
        ttk.Label(frame, text="更新间隔(秒):").grid(row=row, column=0, padx=5, pady=5)
        self.update_interval_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.update_interval_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        return frame

    def _create_analysis_settings(self) -> ttk.Frame:
        """创建分析设置页"""
        frame = ttk.Frame(self.notebook)
        frame.columnconfigure(1, weight=1)

        # 默认分析期数
        row = 0
        ttk.Label(frame, text="默认分析期数:").grid(row=row, column=0, padx=5, pady=5)
        self.default_periods_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.default_periods_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 热门组合数量
        row += 1
        ttk.Label(frame, text="热门组合数量:").grid(row=row, column=0, padx=5, pady=5)
        self.hot_group_count_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.hot_group_count_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 最小稳定性得分
        row += 1
        ttk.Label(frame, text="最小稳定性得分:").grid(row=row, column=0, padx=5, pady=5)
        self.min_stability_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.min_stability_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 启用缓存
        row += 1
        self.cache_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="启用数据缓存",
                       variable=self.cache_enabled_var).grid(row=row, column=0,
                       columnspan=2, sticky='w', padx=5, pady=5)

        # 缓存时间
        row += 1
        ttk.Label(frame, text="缓存时间(秒):").grid(row=row, column=0, padx=5, pady=5)
        self.cache_duration_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.cache_duration_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        return frame

    def _create_gui_settings(self) -> ttk.Frame:
        """创建界面设置页"""
        frame = ttk.Frame(self.notebook)
        frame.columnconfigure(1, weight=1)

        # 窗口大小
        row = 0
        ttk.Label(frame, text="窗口大小:").grid(row=row, column=0, padx=5, pady=5)
        self.window_size_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.window_size_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 字体大小
        row += 1
        ttk.Label(frame, text="字体大小:").grid(row=row, column=0, padx=5, pady=5)
        self.font_size_var = tk.StringVar()
        ttk.Entry(frame, textvariable=self.font_size_var).grid(row=row,
                 column=1, sticky='ew', padx=5, pady=5)

        # 显示工具提示
        row += 1
        self.show_tooltips_var = tk.BooleanVar()
        ttk.Checkbutton(frame, text="显示工具提示",
                       variable=self.show_tooltips_var).grid(row=row, column=0,
                       columnspan=2, sticky='w', padx=5, pady=5)

        # 图表风格
        row += 1
        ttk.Label(frame, text="图表风格:").grid(row=row, column=0, padx=5, pady=5)
        self.chart_style_var = tk.StringVar()
        style_combo = ttk.Combobox(frame, textvariable=self.chart_style_var)
        style_combo['values'] = ('seaborn', 'ggplot', 'classic')
        style_combo.grid(row=row, column=1, sticky='ew', padx=5, pady=5)

        return frame

    def _load_config(self):
        """加载配置到界面"""
        config = self.config_manager.get_all()

        # 基本设置
        app_settings = config.get('app_settings', {})
        self.theme_var.set(app_settings.get('theme', 'light'))
        self.language_var.set(app_settings.get('language', 'zh_CN'))
        self.auto_save_var.set(app_settings.get('auto_save', True))
        self.backup_var.set(app_settings.get('backup_enabled', True))

        # 数据设置
        data_settings = config.get('data_settings', {})
        self.db_path_var.set(data_settings.get('db_path', 'lottery_data.db'))
        self.import_path_var.set(data_settings.get('csv_import_path', 'data/import'))
        self.auto_update_var.set(data_settings.get('auto_update', True))
        self.update_interval_var.set(str(data_settings.get('update_interval', 300)))

        # 分析设置
        analysis_settings = config.get('analysis_settings', {})
        self.default_periods_var.set(str(analysis_settings.get('default_periods', 100)))
        self.hot_group_count_var.set(str(analysis_settings.get('hot_group_count', 10)))
        self.min_stability_var.set(str(analysis_settings.get('min_stability_score', 0.5)))
        self.cache_enabled_var.set(analysis_settings.get('cache_enabled', True))
        self.cache_duration_var.set(str(analysis_settings.get('cache_duration', 3600)))

        # 界面设置
        gui_settings = config.get('gui_settings', {})
        self.window_size_var.set(gui_settings.get('window_size', '1200x800'))
        self.font_size_var.set(str(gui_settings.get('font_size', 12)))
        self.show_tooltips_var.set(gui_settings.get('show_tooltips', True))
        self.chart_style_var.set(gui_settings.get('chart_style', 'seaborn'))

    def _save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.config_manager.set('app_settings.theme', self.theme_var.get())
            self.config_manager.set('app_settings.language', self.language_var.get())
            self.config_manager.set('app_settings.auto_save', self.auto_save_var.get())
            self.config_manager.set('app_settings.backup_enabled', self.backup_var.get())

            self.config_manager.set('data_settings.db_path', self.db_path_var.get())
            self.config_manager.set('data_settings.csv_import_path', self.import_path_var.get())
            self.config_manager.set('data_settings.auto_update', self.auto_update_var.get())
            self.config_manager.set('data_settings.update_interval', int(self.update_interval_var.get()))

            self.config_manager.set('analysis_settings.default_periods', int(self.default_periods_var.get()))
            self.config_manager.set('analysis_settings.hot_group_count', int(self.hot_group_count_var.get()))
            self.config_manager.set('analysis_settings.min_stability_score', float(self.min_stability_var.get()))
            self.config_manager.set('analysis_settings.cache_enabled', self.cache_enabled_var.get())
            self.config_manager.set('analysis_settings.cache_duration', int(self.cache_duration_var.get()))

            self.config_manager.set('gui_settings.window_size', self.window_size_var.get())
            self.config_manager.set('gui_settings.font_size', int(self.font_size_var.get()))
            self.config_manager.set('gui_settings.show_tooltips', self.show_tooltips_var.get())
            self.config_manager.set('gui_settings.chart_style', self.chart_style_var.get())

            messagebox.showinfo("成功", "配置已保存")
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def _reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要恢复默认设置吗？"):
            self.config_manager.reset_to_defaults()
            self._load_config()
            messagebox.showinfo("成功", "已恢复默认设置")
