#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析wuxing_2_hot预测号码差异的原因
"""

from prediction_engine import PredictionEngine
from dynamic_mapping_system import DynamicMappingSystem
from combo_generator import ComboGenerator
import time

def analyze_prediction_difference():
    """分析预测差异"""
    print("🔍 wuxing_2_hot预测号码差异分析")
    print("="*60)
    
    # 原来的预测号码
    old_prediction = [5, 10, 15, 17, 20, 24, 38, 41]
    # 现在的预测号码  
    new_prediction = [2, 8, 13, 19, 24, 30, 35, 41]
    
    print("📊 预测号码对比:")
    print(f"   原来: {old_prediction}")
    print(f"   现在: {new_prediction}")
    
    # 分析共同号码
    common_numbers = set(old_prediction) & set(new_prediction)
    only_old = set(old_prediction) - set(new_prediction)
    only_new = set(new_prediction) - set(old_prediction)
    
    print(f"\n📋 差异分析:")
    print(f"   共同号码: {sorted(common_numbers)} ({len(common_numbers)}个)")
    print(f"   仅在原来: {sorted(only_old)} ({len(only_old)}个)")
    print(f"   仅在现在: {sorted(only_new)} ({len(only_new)}个)")
    print(f"   重合率: {len(common_numbers)/len(old_prediction)*100:.1f}%")

def check_prediction_engine_logic():
    """检查预测引擎逻辑"""
    print(f"\n🔧 检查预测引擎逻辑")
    print("="*60)
    
    try:
        engine = PredictionEngine()
        
        # 查看_run_traditional_strategies方法中的wuxing_2_hot逻辑
        print("📋 检查策略预测号码生成逻辑...")
        
        # 运行预测看实际生成的号码
        result = engine.run_prediction("2025199")
        
        if result.strategy_details:
            for strategy in result.strategy_details:
                if 'wuxing' in strategy.get('strategy_id', '').lower():
                    strategy_name = strategy.get('strategy_name', 'Unknown')
                    predicted_numbers = strategy.get('predicted_numbers', [])
                    
                    print(f"✅ 找到五行策略: {strategy_name}")
                    print(f"   预测号码: {predicted_numbers}")
                    
                    # 分析号码的五行属性
                    analyze_numbers_wuxing(predicted_numbers)
                    break
        else:
            print("❌ 未找到策略详情")
    
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def analyze_numbers_wuxing(numbers):
    """分析号码的五行属性"""
    print(f"\n🔥 分析号码五行属性")
    print("-" * 40)
    
    try:
        mapping_system = DynamicMappingSystem()
        current_period = "2025199"
        
        print(f"期号: {current_period}")
        print("号码  五行")
        print("-" * 15)
        
        wuxing_count = {}
        
        for number in numbers:
            wuxing = mapping_system.get_wuxing_for_number(number, current_period)
            print(f"{number:2d}    {wuxing}")
            
            wuxing_count[wuxing] = wuxing_count.get(wuxing, 0) + 1
        
        print(f"\n📊 五行分布:")
        for wuxing, count in wuxing_count.items():
            print(f"   {wuxing}: {count} 个号码")
        
        # 分析可能的五行组合
        print(f"\n🔍 可能的五行2组合:")
        wuxing_list = list(wuxing_count.keys())
        if len(wuxing_list) >= 2:
            from itertools import combinations
            for combo in combinations(wuxing_list, 2):
                print(f"   {combo[0]}+{combo[1]}")
    
    except Exception as e:
        print(f"❌ 五行分析失败: {e}")

def check_strategy_implementation():
    """检查策略实现"""
    print(f"\n🧠 检查策略实现")
    print("="*60)
    
    # 1. 检查组合生成器
    print("📊 1. 检查五行组合生成器...")
    try:
        generator = ComboGenerator()
        wuxing_combos = generator.generate_wuxing_2()
        
        print(f"   生成 {len(wuxing_combos)} 个五行2组合")
        for i, combo in enumerate(wuxing_combos[:5], 1):
            print(f"     {i}. {combo['key']}: {combo['members']}")
        
    except Exception as e:
        print(f"   ❌ 组合生成失败: {e}")
    
    # 2. 检查预测引擎中的号码生成逻辑
    print(f"\n🎯 2. 检查预测引擎中的号码生成...")
    
    # 查看prediction_engine.py中的具体实现
    try:
        import inspect
        engine = PredictionEngine()
        
        # 获取_run_traditional_strategies方法的源码
        source = inspect.getsource(engine._run_traditional_strategies)
        
        # 查找wuxing相关的号码生成逻辑
        lines = source.split('\n')
        wuxing_lines = []
        
        for i, line in enumerate(lines):
            if 'wuxing' in line.lower() and 'predicted_numbers' in line:
                # 找到相关行，显示上下文
                start = max(0, i-2)
                end = min(len(lines), i+3)
                wuxing_lines.extend(lines[start:end])
        
        if wuxing_lines:
            print("   找到五行号码生成逻辑:")
            for line in wuxing_lines:
                if line.strip():
                    print(f"     {line}")
        else:
            print("   ❌ 未找到具体的五行号码生成逻辑")
    
    except Exception as e:
        print(f"   ❌ 源码检查失败: {e}")

def test_multiple_predictions():
    """测试多次预测的一致性"""
    print(f"\n🔄 测试预测一致性")
    print("="*60)
    
    try:
        engine = PredictionEngine()
        
        print("📊 连续3次预测同一期号...")
        predictions = []
        
        for i in range(3):
            result = engine.run_prediction("2025199")
            
            wuxing_numbers = None
            if result.strategy_details:
                for strategy in result.strategy_details:
                    if 'wuxing' in strategy.get('strategy_id', '').lower():
                        wuxing_numbers = strategy.get('predicted_numbers', [])
                        break
            
            predictions.append(wuxing_numbers)
            print(f"   第{i+1}次: {wuxing_numbers}")
            
            time.sleep(0.1)  # 短暂延迟
        
        # 检查一致性
        if all(pred == predictions[0] for pred in predictions):
            print("✅ 预测结果一致")
        else:
            print("❌ 预测结果不一致，可能存在随机性")
    
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")

def analyze_possible_causes():
    """分析可能的原因"""
    print(f"\n💡 可能原因分析")
    print("="*60)
    
    causes = """
🔍 预测号码差异的可能原因:

1. 📊 数据更新:
   - 历史数据发生变化
   - 新增了最新期的开奖数据
   - 五行映射规则更新

2. 🔧 算法修改:
   - 预测引擎逻辑被修改
   - 五行组合评分算法调整
   - 号码生成策略改变

3. 🎲 随机性因素:
   - 代码中存在随机数生成
   - 时间戳影响预测结果
   - 浮点数精度导致的差异

4. 🔄 缓存问题:
   - 缓存数据不一致
   - 旧缓存影响新预测
   - 缓存更新时机问题

5. 📋 配置变化:
   - 策略配置文件被修改
   - 权重参数调整
   - 过滤条件改变

6. 🧠 实现差异:
   - 存在多个五行预测实现
   - 不同版本的代码并存
   - 模块间的调用关系变化
"""
    
    print(causes)

def recommend_solutions():
    """推荐解决方案"""
    print(f"\n🔧 解决方案建议")
    print("="*60)
    
    solutions = """
🎯 建议的解决方案:

1. 🔍 代码审查:
   - 检查prediction_engine.py中的五行号码生成逻辑
   - 确认是否存在多个实现版本
   - 验证策略配置是否正确

2. 📊 数据验证:
   - 检查历史数据是否有更新
   - 验证五行映射是否正确
   - 确认缓存数据的一致性

3. 🧪 测试验证:
   - 多次运行相同预测验证一致性
   - 对比不同时间点的预测结果
   - 分析预测号码的五行属性

4. 📋 文档记录:
   - 记录每次预测的详细参数
   - 保存预测逻辑的变更历史
   - 建立预测结果的版本控制

5. 🔧 代码统一:
   - 确保只有一个五行预测实现
   - 移除过时或测试代码
   - 标准化预测接口
"""
    
    print(solutions)

def main():
    """主分析流程"""
    print("🔍 wuxing_2_hot预测差异完整分析")
    print("="*70)
    
    # 分析预测差异
    analyze_prediction_difference()
    
    # 检查预测引擎逻辑
    check_prediction_engine_logic()
    
    # 检查策略实现
    check_strategy_implementation()
    
    # 测试预测一致性
    test_multiple_predictions()
    
    # 分析可能原因
    analyze_possible_causes()
    
    # 推荐解决方案
    recommend_solutions()
    
    print(f"\n🎯 分析结论:")
    print("预测号码的差异可能由多种因素造成，")
    print("需要进一步检查代码实现和数据一致性。")

if __name__ == "__main__":
    main()
