{
    // Preserve existing settings
    "terminal.integrated.cwd": "I:\\编程2025-4-30\\test2",
    "claudeCodeChat.permissions.yoloMode": true,
    "github.copilot.nextEditSuggestions.enabled": true,
    // Editor settings
    "editor.fontSize": 14,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.rulers": [
        80,
        100
    ],
    "editor.wordWrap": "on",
    "editor.formatOnSave": true,
    "editor.renderWhitespace": "boundary",
    "editor.suggestSelection": "first",
    // Python settings
    "python.defaultInterpreterPath": "python",
    "python.analysis.typeCheckingMode": "basic",
    "python.formatting.provider": "autopep8",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    // File settings
    "files.encoding": "utf8",
    "files.eol": "\n",
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    // Git settings
    "git.enableSmartCommit": true,
    "git.autofetch": true,
    // Workbench settings
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "vs-seti",
    "workbench.startupEditor": "newUntitledFile"
}
