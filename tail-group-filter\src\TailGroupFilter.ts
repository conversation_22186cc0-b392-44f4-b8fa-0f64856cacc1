import { TailGroupConfig } from './interfaces/TailGroupConfig';
import { get_numbers_by_tail } from './utils/numberMatcher';

export class TailGroupFilter {
    private tailGroups: TailGroupConfig[];

    constructor(config: TailGroupConfig[]) {
        this.tailGroups = config;
    }

    public filter(candidateNumbers: number[]): { [key: string]: number[] } {
        const filteredResults: { [key: string]: number[] } = {};

        this.tailGroups.forEach(group => {
            filteredResults[group.label] = get_numbers_by_tail(candidateNumbers, group.tails);
        });

        return filteredResults;
    }
}