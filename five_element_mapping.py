#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五行映射模块
定义号码/生肖到五行的映射关系和五行组合
"""

import json
from itertools import combinations

# 五行元素
FIVE_ELEMENTS = ["金", "木", "水", "火", "土"]

# 生肖五行映射（固定）
ZODIAC_ELEMENT_MAPPING = {
    "鼠": "水", "牛": "土", "虎": "木", "兔": "木",
    "龙": "土", "蛇": "火", "马": "火", "羊": "土",
    "猴": "金", "鸡": "金", "狗": "土", "猪": "水"
}

def get_five_element_combinations():
    """生成所有五行两两组合（10种）"""
    combos = []
    for combo in combinations(FIVE_ELEMENTS, 2):
        # 按字母顺序排序，确保一致性
        sorted_combo = "".join(sorted(combo))
        combos.append(sorted_combo)
    return sorted(combos)

def get_element_from_zodiac(zodiac: str) -> str:
    """根据生肖获取五行"""
    return ZODIAC_ELEMENT_MAPPING.get(zodiac)

def get_element_from_number(number: int, year: int, element_mappings: dict = None) -> str:
    """根据号码和年份获取五行"""
    if element_mappings and str(year) in element_mappings:
        year_mapping = element_mappings[str(year)]
        for element, numbers in year_mapping.items():
            if number in numbers:
                return element
    return None

def extract_combinations_from_elements(elements: list) -> list:
    """从五行列表中提取所有两两组合"""
    if len(elements) < 2:
        return []
    
    combos = []
    for combo in combinations(set(elements), 2):  # 使用set去重
        sorted_combo = "".join(sorted(combo))
        combos.append(sorted_combo)
    
    return list(set(combos))  # 去重

def get_combination_elements(combination: str) -> tuple:
    """从组合字符串中提取两个五行元素"""
    if len(combination) == 2:
        return tuple(combination)
    return None

def validate_five_element_system():
    """验证五行系统的完整性"""
    print("=== 五行系统验证 ===")
    
    # 验证五行组合
    combinations = get_five_element_combinations()
    print(f"五行组合总数: {len(combinations)}")
    print(f"所有组合: {combinations}")
    
    # 验证生肖五行映射
    print(f"\n=== 生肖五行映射 ===")
    element_count = {}
    for zodiac, element in ZODIAC_ELEMENT_MAPPING.items():
        print(f"{zodiac}: {element}")
        element_count[element] = element_count.get(element, 0) + 1
    
    print(f"\n=== 五行分布统计 ===")
    for element, count in sorted(element_count.items()):
        print(f"{element}: {count}个生肖")
    
    return True

def get_element_statistics(element_mappings: dict = None):
    """获取五行统计信息"""
    stats = {
        'zodiac_distribution': {},
        'number_distribution': {},
        'combinations': get_five_element_combinations()
    }
    
    # 生肖分布
    for zodiac, element in ZODIAC_ELEMENT_MAPPING.items():
        if element not in stats['zodiac_distribution']:
            stats['zodiac_distribution'][element] = []
        stats['zodiac_distribution'][element].append(zodiac)
    
    # 号码分布（如果提供了映射）
    if element_mappings:
        for year, year_mapping in element_mappings.items():
            stats['number_distribution'][year] = {}
            for element, numbers in year_mapping.items():
                stats['number_distribution'][year][element] = {
                    'count': len(numbers),
                    'numbers': numbers
                }
    
    return stats

def test_combination_extraction():
    """测试组合提取功能"""
    print("=== 组合提取测试 ===")
    
    test_cases = [
        ["金", "木"],
        ["金", "木", "水"],
        ["火", "火", "土"],  # 重复测试
        ["水"],             # 单个元素
        []                  # 空列表
    ]
    
    for elements in test_cases:
        combos = extract_combinations_from_elements(elements)
        print(f"输入: {elements} → 组合: {combos}")

def get_combination_description(combination: str) -> str:
    """获取五行组合的描述"""
    descriptions = {
        "金木": "金克木 - 相克关系",
        "金水": "金生水 - 相生关系", 
        "金火": "火克金 - 相克关系",
        "金土": "土生金 - 相生关系",
        "木水": "水生木 - 相生关系",
        "木火": "木生火 - 相生关系",
        "木土": "木克土 - 相克关系",
        "水火": "水克火 - 相克关系",
        "水土": "土克水 - 相克关系",
        "火土": "火生土 - 相生关系"
    }
    return descriptions.get(combination, "未知关系")

def analyze_combination_relationships():
    """分析五行组合的相生相克关系"""
    print("=== 五行组合关系分析 ===")
    
    combinations = get_five_element_combinations()
    
    sheng_count = 0  # 相生
    ke_count = 0     # 相克
    
    for combo in combinations:
        desc = get_combination_description(combo)
        print(f"{combo}: {desc}")
        
        if "相生" in desc:
            sheng_count += 1
        elif "相克" in desc:
            ke_count += 1
    
    print(f"\n统计: 相生关系 {sheng_count}个, 相克关系 {ke_count}个")

if __name__ == "__main__":
    # 验证系统
    validate_five_element_system()
    
    # 测试组合提取
    print()
    test_combination_extraction()
    
    # 分析关系
    print()
    analyze_combination_relationships()
    
    # 显示统计
    print()
    stats = get_element_statistics()
    print("=== 系统统计 ===")
    print(f"五行组合数: {len(stats['combinations'])}")
    print(f"生肖五行分布: {stats['zodiac_distribution']}")
