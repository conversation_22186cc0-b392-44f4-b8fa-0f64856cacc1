@echo off
echo 正在安装六合彩预测系统依赖包...
echo =====================================

echo.
echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 尝试使用py命令...
    py --version
    if %errorlevel% neq 0 (
        echo 错误: 未找到Python环境
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo.
echo 使用Python命令: %PYTHON_CMD%

echo.
echo 升级pip...
%PYTHON_CMD% -m pip install --upgrade pip

echo.
echo 安装核心依赖包...
%PYTHON_CMD% -m pip install numpy>=1.21.0
%PYTHON_CMD% -m pip install pandas>=1.3.0
%PYTHON_CMD% -m pip install scikit-learn>=1.0.0
%PYTHON_CMD% -m pip install joblib>=1.0.0

echo.
echo 安装配置和文件处理包...
%PYTHON_CMD% -m pip install PyYAML>=6.0
%PYTHON_CMD% -m pip install openpyxl>=3.0.0
%PYTHON_CMD% -m pip install xlsxwriter>=3.0.0
%PYTHON_CMD% -m pip install python-dateutil>=2.8.0

echo.
echo 验证安装...
%PYTHON_CMD% -c "import numpy, pandas, sklearn, yaml, openpyxl; print('所有依赖包安装成功!')"

if %errorlevel% equ 0 (
    echo.
    echo ✅ 依赖安装完成！
    echo.
    echo 现在可以运行以下命令:
    echo   %PYTHON_CMD% main.py                 # 运行基础预测
    echo   %PYTHON_CMD% gui_main.py             # 启动GUI界面
    echo   %PYTHON_CMD% system_status_check.py  # 完整系统检查
) else (
    echo.
    echo ❌ 依赖安装失败，请检查错误信息
)

echo.
pause
