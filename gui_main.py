#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩智能预测系统 - GUI主界面
基于tkinter的可视化管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from typing import Dict, List
from datetime import datetime
import json
import os

# 导入预测和筛选引擎
from prediction_engine import PredictionEngine
from tail_group_filter import TailGroupFilter

class MainWindow:
    def __init__(self):
        """初始化主窗口"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("六合彩智能预测系统")
        self.root.geometry("1200x800")

        # 初始化所有属性
        self._init_variables()
        self._init_engines()
        self._init_ui()
        self.load_config()

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        try:
            self.is_predicting = True
            self.log_message("开始预测...")

            # 进行预测
            result = self.prediction_engine.predict(target_period)

            # 显示预测结果
            self.show_prediction_result(result)

            # 应用筛选
            if self.use_filter_var.get():
                self.apply_filters(result["numbers"])

        except Exception as e:
            self.log_message(f"预测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_predicting = False

    def _init_variables(self):
        """初始化变量"""
        # 状态变量
        self.is_predicting = False
        self.is_backtesting = False

        # 控件变量
        self.period_var = tk.StringVar()
        self.start_period_var = tk.StringVar()
        self.end_period_var = tk.StringVar()
        self.large_filter_var = tk.BooleanVar(value=True)
        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)
        self.use_filter_var = tk.BooleanVar(value=True)

        # 文本控件
        self.log_text = None
        self.prediction_result_text = None
        self.backtest_result_text = None
        self.filter_result_text = None
        self.data_stats_text = None
        self.analysis_report_text = None

    def _init_engines(self):
        """初始化引擎"""
        self.prediction_engine = PredictionEngine()
        self.filter_engine = TailGroupFilter()

    def _init_ui(self):
        """初始化UI"""
        # 配置主窗口网格
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)

        # 创建组件
        left_frame = self.create_left_panel()
        right_frame = self.create_right_panel()

        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # 创建日志面板
        log_frame = self.create_log_panel()
        log_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        try:
            self.is_predicting = True
            self.log_message("开始预测...")

            # 进行预测
            result = self.prediction_engine.predict(target_period)

            # 显示预测结果
            self.show_prediction_result(result)

            # 应用筛选
            if self.use_filter_var.get():
                self.apply_filters(result["numbers"])

        except Exception as e:
            self.log_message(f"预测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_predicting = False

        # 初始化控件变量
        self.period_var = tk.StringVar()
        self.start_period_var = tk.StringVar()
        self.end_period_var = tk.StringVar()
        self.large_filter_var = tk.BooleanVar(value=True)
        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)
        self.use_filter_var = tk.BooleanVar(value=True)

        # 初始化引擎
        self.prediction_engine = PredictionEngine()
        self.filter_engine = TailGroupFilter()

        # 配置主窗口网格
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)

        # 创建组件
        left_frame = self.create_left_panel()
        right_frame = self.create_right_panel()

        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # 创建日志面板
        log_frame = self.create_log_panel()
        log_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)

        # 加载配置
        self.load_config()

    def create_panels(self):
        """创建所有面板"""
        # 创建左右面板
        left_frame = self.create_left_panel()
        right_frame = self.create_right_panel()

        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # 创建日志面板
        log_frame = self.create_log_panel()
        log_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)

    def create_left_panel(self):
        """创建左侧面板"""
        left_frame = ttk.Frame(self.root)

        # 预测控制区
        prediction_frame = ttk.LabelFrame(left_frame, text="预测控制")
        prediction_frame.pack(fill=tk.X, padx=5, pady=5)

        # 期号输入
        ttk.Label(prediction_frame, text="目标期号:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(prediction_frame, textvariable=self.period_var).pack(side=tk.LEFT, padx=5)
        ttk.Button(prediction_frame, text="开始预测", command=self.start_prediction).pack(side=tk.LEFT, padx=5)

        # 筛选设置区
        filter_frame = ttk.LabelFrame(left_frame, text="筛选设置")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # 筛选选项
        ttk.Checkbutton(filter_frame, text="大筛子(生肖五行)",
                       variable=self.large_filter_var).pack(anchor=tk.W, padx=5)
        ttk.Checkbutton(filter_frame, text="中筛子(号码特征)",
                       variable=self.medium_filter_var).pack(anchor=tk.W, padx=5)
        ttk.Checkbutton(filter_frame, text="小筛子(尾数组合)",
                       variable=self.small_filter_var).pack(anchor=tk.W, padx=5)

        # 预测结果区
        result_frame = ttk.LabelFrame(left_frame, text="预测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.prediction_result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.prediction_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.filter_result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.filter_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        return left_frame

    def create_right_panel(self):
        """创建右侧面板"""
        right_frame = ttk.Frame(self.root)

        # 回测控制区
        backtest_frame = ttk.LabelFrame(right_frame, text="回测控制")
        backtest_frame.pack(fill=tk.X, padx=5, pady=5)

        # 回测期号范围
        range_frame = ttk.Frame(backtest_frame)
        range_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(range_frame, text="开始期号:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(range_frame, textvariable=self.start_period_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(range_frame, text="结束期号:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(range_frame, textvariable=self.end_period_var).pack(side=tk.LEFT, padx=5)

        ttk.Button(backtest_frame, text="开始回测",
                  command=self.run_backtest).pack(padx=5, pady=5)

        # 回测结果区
        backtest_frame = ttk.LabelFrame(right_frame, text="回测结果")
        backtest_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.backtest_result_text = scrolledtext.ScrolledText(backtest_frame, height=10)
        self.backtest_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 数据分析区
        analysis_frame = ttk.LabelFrame(right_frame, text="数据分析")
        analysis_frame.pack(fill=tk.X, padx=5, pady=5)

        self.data_stats_text = scrolledtext.ScrolledText(analysis_frame, height=10)
        self.data_stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        return right_frame

    def create_log_panel(self):
        """创建日志面板"""
        self.log_frame = ttk.LabelFrame(self.root, text="系统日志")
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=8)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        return self.log_frame

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        try:
            self.is_predicting = True
            self.log_message("开始预测...")

            # 进行预测
            result = self.prediction_engine.predict(target_period)

            # 显示预测结果
            self.show_prediction_result(result)

            # 应用筛选
            if self.use_filter_var.get():
                self.apply_filters(result["numbers"])

        except Exception as e:
            self.log_message(f"预测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_predicting = False

    def run_backtest(self):
        """运行回测"""
        if self.is_backtesting:
            messagebox.showwarning("警告", "回测正在进行中，请稍候...")
            return

        start = self.start_period_var.get().strip()
        end = self.end_period_var.get().strip()

        if not start or not end:
            messagebox.showerror("错误", "请输入回测区间")
            return

        try:
            self.is_backtesting = True
            self.log_message(f"开始回测: {start} - {end}")
            self.backtest_result_text.delete("1.0", tk.END)
            self.backtest_result_text.insert(tk.END, "回测功能开发中...\n")

        except Exception as e:
            self.log_message(f"回测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_backtesting = False

    def apply_filters(self, numbers):
        """应用筛选"""
        try:
            self.log_message("应用筛选...")
            filtered = numbers

            # 应用三层筛选
            if self.large_filter_var.get():
                self.log_message("应用大筛子...")
                # TODO: 实现大筛子逻辑

            if self.medium_filter_var.get():
                self.log_message("应用中筛子...")
                # TODO: 实现中筛子逻辑

            if self.small_filter_var.get():
                self.log_message("应用小筛子...")
                results = self.filter_engine.filter_candidates(filtered)
                self.show_filter_results(results)

        except Exception as e:
            self.log_message(f"筛选出错: {str(e)}")
            raise

    def save_config(self):
        """保存配置"""
        try:
            config = {
                "filters": {
                    "use_large": self.large_filter_var.get(),
                    "use_medium": self.medium_filter_var.get(),
                    "use_small": self.small_filter_var.get()
                }
            }

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")

        except Exception as e:
            self.log_message(f"保存配置出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def load_config(self):
        """加载配置"""
        try:
            if not os.path.exists("config.json"):
                return

            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            filters = config.get("filters", {})
            self.large_filter_var.set(filters.get("use_large", True))
            self.medium_filter_var.set(filters.get("use_medium", True))
            self.small_filter_var.set(filters.get("use_small", True))

            self.log_message("配置已加载")

        except Exception as e:
            self.log_message(f"加载配置出错: {str(e)}")

    def show_prediction_result(self, result):
        """显示预测结果"""
        self.prediction_result_text.delete("1.0", tk.END)
        self.prediction_result_text.insert(tk.END, f"目标期号: {result['period']}\n")
        self.prediction_result_text.insert(tk.END, f"推荐号码: {result['numbers']}\n")
        self.prediction_result_text.insert(tk.END, f"置信度: {result['confidence']:.2%}\n")

    def show_filter_results(self, results):
        """显示筛选结果"""
        self.filter_result_text.delete("1.0", tk.END)
        for label, numbers in results.items():
            self.filter_result_text.insert(tk.END, f"{label}: {numbers}\n")

    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """程序入口"""
    app = MainWindow()
    app.run()

if __name__ == "__main__":
    main()

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from typing import Dict, List
from datetime import datetime
import json
import os

# 导入预测和筛选引擎
from prediction_engine import PredictionEngine
from tail_group_filter import TailGroupFilter

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("六合彩智能预测系统")
        self.root.geometry("1200x800")

        # 初始化状态变量
        self.is_predicting = False
        self.is_backtesting = False

        # 初始化控件变量
        self.period_var = tk.StringVar()
        self.start_period_var = tk.StringVar()
        self.end_period_var = tk.StringVar()
        self.large_filter_var = tk.BooleanVar(value=True)
        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)
        self.use_filter_var = tk.BooleanVar(value=True)

        # 文本控件
        self.log_text = None
        self.prediction_result_text = None
        self.backtest_result_text = None
        self.filter_result_text = None
        self.data_stats_text = None
        self.analysis_report_text = None

        # 配置主窗口网格
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)

        # 初始化引擎
        self.prediction_engine = PredictionEngine()
        self.filter_engine = TailGroupFilter()

        # 创建组件
        self.create_left_panel()
        self.create_right_panel()

        # 创建日志面板
        log_frame = self.create_log_panel()
        log_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)

        # 加载配置
        self.load_config()

    def create_left_panel(self):
        """创建左侧面板"""
        left_frame = ttk.Frame(self.root)
        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # 预测控制区
        prediction_frame = ttk.LabelFrame(left_frame, text="预测控制")
        prediction_frame.pack(fill=tk.X, padx=5, pady=5)

        # 期号输入
        ttk.Label(prediction_frame, text="目标期号:").pack(side=tk.LEFT, padx=5)
        self.period_var = tk.StringVar()
        ttk.Entry(prediction_frame, textvariable=self.period_var).pack(side=tk.LEFT, padx=5)

        # 预测按钮
        ttk.Button(prediction_frame, text="开始预测", command=self.start_prediction).pack(side=tk.LEFT, padx=5)

        # 筛选设置区
        filter_frame = ttk.LabelFrame(left_frame, text="筛选设置")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # 筛选选项
        self.large_filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(filter_frame, text="大筛子(生肖五行)", variable=self.large_filter_var).pack(anchor=tk.W, padx=5)

        self.medium_filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(filter_frame, text="中筛子(号码特征)", variable=self.medium_filter_var).pack(anchor=tk.W, padx=5)

        self.small_filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(filter_frame, text="小筛子(尾数组合)", variable=self.small_filter_var).pack(anchor=tk.W, padx=5)

        # 预测结果区
        result_frame = ttk.LabelFrame(left_frame, text="预测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.prediction_result_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.prediction_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_right_panel(self):
        """创建右侧面板"""
        right_frame = ttk.Frame(self.root)
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # 回测控制区
        backtest_frame = ttk.LabelFrame(right_frame, text="回测控制")
        backtest_frame.pack(fill=tk.X, padx=5, pady=5)

        # 回测期号范围
        range_frame = ttk.Frame(backtest_frame)
        range_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(range_frame, text="开始期号:").pack(side=tk.LEFT, padx=5)
        self.start_period_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.start_period_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(range_frame, text="结束期号:").pack(side=tk.LEFT, padx=5)
        self.end_period_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.end_period_var).pack(side=tk.LEFT, padx=5)

        # 回测按钮
        ttk.Button(backtest_frame, text="开始回测", command=self.run_backtest).pack(padx=5, pady=5)

        # 回测结果区
        backtest_result_frame = ttk.LabelFrame(right_frame, text="回测结果")
        backtest_result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.backtest_result_text = scrolledtext.ScrolledText(backtest_result_frame, height=10)
        self.backtest_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 高级分析区
        analysis_frame = ttk.LabelFrame(right_frame, text="高级分析")
        analysis_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(analysis_frame, text="生成分析报告", command=self.generate_analysis_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(analysis_frame, text="检查数据完整性", command=self.check_data_integrity).pack(side=tk.LEFT, padx=5)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """程序入口"""
    app = MainWindow()
    app.run()

if __name__ == "__main__":
    main()

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from typing import Dict, List
from datetime import datetime
import json
import threading
from datetime import datetime
import os
import yaml

# 导入后端模块
# from prediction_engine import PredictionEngine  # 注释掉原系统
from prediction_engine_adapter import PredictionEngineAdapter  # 使用适配器
from dsl_strategy_parser import DSLStrategyParser
from report_generator import ReportGenerator
from backtest_engine import BacktestEngine
from data_attributes import HistoryDataManager, DataAttributeMapper
from result_exporter import ResultExporter
from zodiac_group_analyzer import ZodiacGroupAnalyzer
from extreme_reback_engine import ExtremeRebackEngine
from three_layer_filter_engine import ThreeLayerFilterEngine  # 导入三层筛选系统
from tail_group_filter import TailGroupFilter  # 导入尾数筛选器

class LotteryPredictionGUI:
    """六合彩预测系统GUI主界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("六合彩智能预测系统 v1.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # 初始化后端组件
        self.prediction_engine = PredictionEngineAdapter()  # 使用适配器，内部调用真实预测引擎
        self.dsl_parser = DSLStrategyParser()
        self.report_generator = ReportGenerator()
        self.data_manager = HistoryDataManager()

        try:
            self.filter_engine = ThreeLayerFilterEngine()  # 初始化三层筛选系统
            print("✅ 三层筛选系统已启用")
        except Exception as e:
            print(f"⚠️ 三层筛选系统不可用: {str(e)}")
            self.filter_engine = None
        self.attribute_mapper = DataAttributeMapper()
        self.result_exporter = ResultExporter()
        self.zodiac_analyzer = ZodiacGroupAnalyzer()
        self.reback_engine = ExtremeRebackEngine()

        # 状态变量
        self.is_predicting = False
        self.is_backtesting = False
        self.last_prediction_result = None
        self.last_backtest_result = None

        # 创建界面
        self.create_widgets()
        self.load_initial_data()

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.create_main_frame()

        # 创建菜单栏
        self.create_menu()

        # 创建工具栏
        self.create_toolbar()

        # 创建主要内容区域
        self.create_content_area()

        # 创建状态栏
        self.create_status_bar()

    def create_main_frame(self):
        """创建主框架"""
        # 主容器
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入历史数据", command=self.import_data)
        file_menu.add_command(label="导出预测结果", command=self.export_prediction)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 策略菜单
        strategy_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="策略", menu=strategy_menu)
        strategy_menu.add_command(label="策略配置", command=self.open_strategy_config)
        strategy_menu.add_command(label="重新加载策略", command=self.reload_strategies)

        # 模型菜单
        model_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="模型", menu=model_menu)
        model_menu.add_command(label="训练模型", command=self.train_models)
        model_menu.add_command(label="模型状态", command=self.show_model_status)

        # 分析菜单
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="分析", menu=analysis_menu)
        analysis_menu.add_command(label="运行回测", command=self.run_backtest)
        analysis_menu.add_command(label="生成报告", command=self.generate_report)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="高级Z-Score分析", command=self.open_advanced_analysis)
        analysis_menu.add_command(label="刷新高级分析", command=self.refresh_advanced_analysis)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 预测按钮
        self.predict_btn = ttk.Button(
            toolbar,
            text="🎯 开始预测",
            command=self.start_prediction,
            style="Accent.TButton"
        )
        self.predict_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 三层筛选快捷按钮
        self.quick_three_layer_btn = ttk.Button(
            toolbar,
            text="🎯 三层筛选",
            command=self.quick_three_layer_prediction,
            style="Accent.TButton"
        )
        self.quick_three_layer_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 回测按钮
        ttk.Button(
            toolbar,
            text="📊 运行回测",
            command=self.run_backtest
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 报告按钮
        ttk.Button(
            toolbar,
            text="📄 生成报告",
            command=self.generate_report
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 高级分析按钮
        ttk.Button(
            toolbar,
            text="🧠 高级分析",
            command=self.open_advanced_analysis
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 三层筛选按钮
        ttk.Button(
            toolbar,
            text="🎯 三层筛选",
            command=self.open_three_layer_filter
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # 期号输入
        ttk.Label(toolbar, text="目标期号:").pack(side=tk.LEFT, padx=(0, 5))
        self.period_var = tk.StringVar(value="2025201")
        period_entry = ttk.Entry(toolbar, textvariable=self.period_var, width=10)
        period_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 刷新按钮
        ttk.Button(
            toolbar,
            text="🔄 刷新",
            command=self.refresh_data
        ).pack(side=tk.RIGHT)

    def create_content_area(self):
        """创建主要内容区域"""
        # 创建notebook用于页面切换
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建主预测页面
        self.create_main_prediction_page()

        # 创建三层筛选页面
        self.create_three_layer_filter_page()

        # 创建回测页面
        self.create_backtest_page()

        # 创建数据管理页面
        self.create_data_management_page()

        # 创建分析报告页面
        self.create_analysis_page()

        # 创建高级分析页面
        self.create_advanced_page()

        # 创建日志显示区域
        self.create_log_area()

    def create_log_area(self):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(self.main_frame, text="系统日志")
        log_frame.pack(fill=tk.X, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=5,
            width=80,
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.X, padx=5, pady=5)

    def create_main_prediction_page(self):
        """创建主预测页面"""
        prediction_page = ttk.Frame(self.notebook)
        self.notebook.add(prediction_page, text="预测")

        # 预测控制区
        control_frame = ttk.LabelFrame(prediction_page, text="预测控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 期号输入
        period_frame = ttk.Frame(control_frame)
        period_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(period_frame, text="目标期号:").pack(side=tk.LEFT)
        self.period_var = tk.StringVar()
        ttk.Entry(period_frame, textvariable=self.period_var).pack(side=tk.LEFT, padx=5)

        # 预测按钮
        ttk.Button(control_frame, text="开始预测", command=self.start_prediction).pack(pady=5)

        # 预测结果显示
        result_frame = ttk.LabelFrame(prediction_page, text="预测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.prediction_result_text = scrolledtext.ScrolledText(
            result_frame,
            height=15,
            wrap=tk.WORD
        )
        self.prediction_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_three_layer_filter_page(self):
        """创建三层筛选页面"""
        filter_page = ttk.Frame(self.notebook)
        self.notebook.add(filter_page, text="三层筛选")

        # 筛选控制
        control_frame = ttk.LabelFrame(filter_page, text="筛选控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 各层筛选开关
        self.large_filter_var = tk.BooleanVar(value=True)
        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)

        ttk.Checkbutton(
            control_frame,
            text="大筛子(生肖五行)",
            variable=self.large_filter_var
        ).pack(side=tk.LEFT, padx=5)

        ttk.Checkbutton(
            control_frame,
            text="中筛子(号码特征)",
            variable=self.medium_filter_var
        ).pack(side=tk.LEFT, padx=5)

        ttk.Checkbutton(
            control_frame,
            text="小筛子(尾数组合)",
            variable=self.small_filter_var
        ).pack(side=tk.LEFT, padx=5)

        # 筛选结果显示
        result_frame = ttk.LabelFrame(filter_page, text="筛选结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.filter_result_text = scrolledtext.ScrolledText(
            result_frame,
            height=15,
            wrap=tk.WORD
        )
        self.filter_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_data_management_page(self):
        """创建数据管理页面"""
        data_page = ttk.Frame(self.notebook)
        self.notebook.add(data_page, text="数据管理")

        # 数据操作按钮
        button_frame = ttk.Frame(data_page)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            button_frame,
            text="导入历史数据",
            command=self.import_data
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="数据完整性检查",
            command=self.check_data_integrity
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="导出数据报告",
            command=self.export_data_report
        ).pack(side=tk.LEFT, padx=5)

        # 数据统计显示
        stats_frame = ttk.LabelFrame(data_page, text="数据统计")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.data_stats_text = scrolledtext.ScrolledText(
            stats_frame,
            height=15,
            wrap=tk.WORD
        )
        self.data_stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_backtest_page(self):
        """创建回测页面"""
        backtest_page = ttk.Frame(self.notebook)
        self.notebook.add(backtest_page, text="回测")

        # 回测控制
        control_frame = ttk.LabelFrame(backtest_page, text="回测控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 回测范围设置
        range_frame = ttk.Frame(control_frame)
        range_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(range_frame, text="起始期号:").pack(side=tk.LEFT)
        self.start_period_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.start_period_var).pack(side=tk.LEFT, padx=5)

        ttk.Label(range_frame, text="结束期号:").pack(side=tk.LEFT)
        self.end_period_var = tk.StringVar()
        ttk.Entry(range_frame, textvariable=self.end_period_var).pack(side=tk.LEFT, padx=5)

        # 回测按钮
        ttk.Button(
            control_frame,
            text="开始回测",
            command=self.run_backtest
        ).pack(pady=5)

        # 回测结果显示
        result_frame = ttk.LabelFrame(backtest_page, text="回测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.backtest_result_text = scrolledtext.ScrolledText(
            result_frame,
            height=15,
            wrap=tk.WORD
        )
        self.backtest_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_analysis_page(self):
        """创建分析报告页面"""
        analysis_page = ttk.Frame(self.notebook)
        self.notebook.add(analysis_page, text="分析报告")

        # 报告控制
        control_frame = ttk.LabelFrame(analysis_page, text="报告控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            control_frame,
            text="生成分析报告",
            command=self.generate_analysis_report
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            control_frame,
            text="导出报告",
            command=self.export_analysis_report
        ).pack(side=tk.LEFT, padx=5)

        # 报告显示区域
        report_frame = ttk.LabelFrame(analysis_page, text="分析报告")
        report_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.analysis_report_text = scrolledtext.ScrolledText(
            report_frame,
            height=15,
            wrap=tk.WORD
        )
        self.analysis_report_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_advanced_page(self):
        """创建高级分析页面"""
        advanced_page = ttk.Frame(self.notebook)
        self.notebook.add(advanced_page, text="高级分析")

        # 分析工具选择
        tools_frame = ttk.LabelFrame(advanced_page, text="分析工具")
        tools_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            tools_frame,
            text="生肖能量分析",
            command=self.analyze_zodiac_energy
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            tools_frame,
            text="组合模式分析",
            command=self.analyze_combination_patterns
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            tools_frame,
            text="极限追踪分析",
            command=self.analyze_extreme_tracking
        ).pack(side=tk.LEFT, padx=5)

        # 分析结果显示
        result_frame = ttk.LabelFrame(advanced_page, text="分析结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.advanced_analysis_text = scrolledtext.ScrolledText(
            result_frame,
            height=15,
            wrap=tk.WORD
        )
        self.advanced_analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        # 获取目标期号
        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        try:
            self.is_predicting = True
            self.log_message(f"🎯 开始预测 - 目标期号: {target_period}")

            # 调用预测引擎
            result = self.prediction_engine.predict(target_period)

            # 显示预测结果
            self.show_prediction_result(result)

            # 如果启用三层筛选，进行筛选
            if self.filter_engine:
                self.apply_three_layer_filter(result.get('numbers', []))

        except Exception as e:
            self.log_message(f"❌ 预测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_predicting = False

    def apply_three_layer_filter(self, numbers: List[int]):
        """应用三层筛选"""
        try:
            self.log_message("开始三层筛选...")

            # 获取筛选设置
            use_large = self.large_filter_var.get()
            use_medium = self.medium_filter_var.get()
            use_small = self.small_filter_var.get()

            filtered_numbers = numbers

            # 大筛子
            if use_large:
                self.log_message("应用大筛子(生肖五行)...")
                filtered_numbers = self.filter_engine.large_filter(filtered_numbers)

            # 中筛子
            if use_medium:
                self.log_message("应用中筛子(号码特征)...")
                filtered_numbers = self.filter_engine.medium_filter(filtered_numbers)

            # 小筛子
            if use_small:
                self.log_message("应用小筛子(尾数组合)...")
                tail_groups = self.filter_engine.small_filter(filtered_numbers)
                self.show_tail_group_results(tail_groups)

            # 获取筛选统计
            stats = self.filter_engine.get_filter_statistics(numbers)
            self.show_filter_statistics(stats)

        except Exception as e:
            self.log_message(f"❌ 筛选出错: {str(e)}")
            raise

    def run_backtest(self):
        """运行回测"""
        if self.is_backtesting:
            messagebox.showwarning("警告", "回测正在进行中，请稍候...")
            return

        start_period = self.start_period_var.get().strip()
        end_period = self.end_period_var.get().strip()

        if not start_period or not end_period:
            messagebox.showerror("错误", "请输入回测期号范围")
            return

        try:
            self.is_backtesting = True
            self.log_message(f"开始回测: {start_period} - {end_period}")

            # TODO: 实现回测逻辑
            self.backtest_result_text.delete(1.0, tk.END)
            self.backtest_result_text.insert(tk.END, "回测功能开发中...\n")

        except Exception as e:
            self.log_message(f"❌ 回测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_backtesting = False

    def generate_analysis_report(self):
        """生成分析报告"""
        try:
            self.log_message("生成分析报告...")

            # TODO: 实现报告生成逻辑
            self.analysis_report_text.delete(1.0, tk.END)
            self.analysis_report_text.insert(tk.END, "报告生成功能开发中...\n")

        except Exception as e:
            self.log_message(f"❌ 报告生成出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def check_data_integrity(self):
        """检查数据完整性"""
        try:
            self.log_message("检查数据完整性...")

            # TODO: 实现数据完整性检查
            self.data_stats_text.delete(1.0, tk.END)
            self.data_stats_text.insert(tk.END, "数据完整性检查功能开发中...\n")

        except Exception as e:
            self.log_message(f"❌ 数据检查出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def show_prediction_result(self, result: Dict):
        """显示预测结果"""
        self.prediction_result_text.delete(1.0, tk.END)
        self.prediction_result_text.insert(tk.END, "=== 预测结果 ===\n\n")

        if 'numbers' in result:
            self.prediction_result_text.insert(tk.END, f"推荐号码: {result['numbers']}\n")
        if 'confidence' in result:
            self.prediction_result_text.insert(tk.END, f"置信度: {result['confidence']:.2%}\n")

    def show_tail_group_results(self, tail_groups: Dict[str, List[int]]):
        """显示尾数组合结果"""
        self.filter_result_text.delete(1.0, tk.END)
        self.filter_result_text.insert(tk.END, "=== 尾数组合筛选结果 ===\n\n")

        for label, numbers in tail_groups.items():
            self.filter_result_text.insert(tk.END, f"{label}: {numbers}\n")

    def show_filter_statistics(self, stats: Dict[str, int]):
        """显示筛选统计信息"""
        self.filter_result_text.insert(tk.END, "\n=== 筛选统计 ===\n")
        self.filter_result_text.insert(tk.END, f"初始数量: {stats['initial_count']}\n")
        self.filter_result_text.insert(tk.END, f"大筛后: {stats['after_large_filter']}\n")
        self.filter_result_text.insert(tk.END, f"中筛后: {stats['after_medium_filter']}\n")
        self.filter_result_text.insert(tk.END, f"小筛组数: {stats['tail_group_count']}\n")

    def create_log_panel(self):
        """创建日志面板"""
        self.log_frame = ttk.LabelFrame(self.root, text="系统日志")
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=8)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        return self.log_frame

    def toggle_filter_options(self):
        """切换筛选选项的启用状态"""
        state = "normal" if self.use_filter_var.get() else "disabled"
        for widget in self.filter_config_frame.winfo_children():
            widget.configure(state=state)

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        # 获取目标期号
        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        try:
            self.is_predicting = True
            self.log_message("开始预测...")

            # 进行预测
            result = self.prediction_engine.predict(target_period)

            # 显示预测结果
            self.show_prediction_result(result)

            # 应用筛选
            if self.use_filter_var.get():
                self.apply_filters(result["numbers"])

        except Exception as e:
            self.log_message(f"预测出错: {str(e)}")
            messagebox.showerror("错误", str(e))
        finally:
            self.is_predicting = False

    def apply_filters(self, numbers):
        """应用筛选"""
        try:
            self.log_message("应用筛选...")
            filtered = numbers

            # 应用三层筛选
            if self.large_filter_var.get():
                self.log_message("应用大筛子...")
                # TODO: 实现大筛子逻辑

            if self.medium_filter_var.get():
                self.log_message("应用中筛子...")
                # TODO: 实现中筛子逻辑

            if self.small_filter_var.get():
                self.log_message("应用小筛子...")
                results = self.filter_engine.filter_candidates(filtered)
                self.show_filter_results(results)

        except Exception as e:
            self.log_message(f"筛选出错: {str(e)}")
            raise

    def run_backtest(self):
        """运行回测"""
        try:
            # 获取回测区间
            start = self.start_period_var.get().strip()
            end = self.end_period_var.get().strip()

            if not start or not end:
                messagebox.showerror("错误", "请输入回测区间")
                return

            self.log_message(f"开始回测: {start} - {end}")
            # TODO: 实现回测逻辑

        except Exception as e:
            self.log_message(f"回测出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def save_config(self):
        """保存配置"""
        try:
            config = {
                "filters": {
                    "use_large": self.large_filter_var.get(),
                    "use_medium": self.medium_filter_var.get(),
                    "use_small": self.small_filter_var.get()
                }
            }

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")

        except Exception as e:
            self.log_message(f"保存配置出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def load_config(self):
        """加载配置"""
        try:
            if not os.path.exists("config.json"):
                return

            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            filters = config.get("filters", {})
            self.large_filter_var.set(filters.get("use_large", True))
            self.medium_filter_var.set(filters.get("use_medium", True))
            self.small_filter_var.set(filters.get("use_small", True))

            self.log_message("配置已加载")

        except Exception as e:
            self.log_message(f"加载配置出错: {str(e)}")

    def show_prediction_result(self, result):
        """显示预测结果"""
        self.prediction_result_text.delete("1.0", tk.END)
        self.prediction_result_text.insert(tk.END, f"目标期号: {result['period']}\n")
        self.prediction_result_text.insert(tk.END, f"推荐号码: {result['numbers']}\n")
        self.prediction_result_text.insert(tk.END, f"置信度: {result['confidence']:.2%}\n")

    def show_filter_results(self, results):
        """显示筛选结果"""
        self.filter_result_text.delete("1.0", tk.END)
        for label, numbers in results.items():
            self.filter_result_text.insert(tk.END, f"{label}: {numbers}\n")

    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)

        ttk.Checkbutton(
            self.filter_config_frame,
            text="大筛子(生肖五行)",
            variable=self.large_filter_var
        ).pack(side=tk.LEFT, padx=5)

        ttk.Checkbutton(
            self.filter_config_frame,
            text="中筛子(号码特征)",
            variable=self.medium_filter_var
        ).pack(side=tk.LEFT, padx=5)

        ttk.Checkbutton(
            self.filter_config_frame,
            text="小筛子(尾数组合)",
            variable=self.small_filter_var
        ).pack(side=tk.LEFT, padx=5)

        # 预测按钮
        button_frame = ttk.Frame(self.prediction_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(
            button_frame,
            text="开始预测",
            command=self.start_prediction
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            button_frame,
            text="清空结果",
            command=self.clear_results
        ).pack(side=tk.LEFT, padx=5)

        # 创建结果显示区域
        self.results_frame = ttk.LabelFrame(self.main_frame, text="预测结果")
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建文本显示区
        self.result_text = scrolledtext.ScrolledText(
            self.results_frame,
            wrap=tk.WORD,
            width=80,
            height=20
        )
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def toggle_filter_options(self):
        """切换筛选选项的可用状态"""
        state = "normal" if self.use_filter_var.get() else "disabled"
        for child in self.filter_config_frame.winfo_children():
            child.configure(state=state)

    def clear_results(self):
        """清空结果显示区"""
        self.result_text.delete(1.0, tk.END)
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 预测结果页
        self.create_prediction_tab()

        # 策略管理页
        self.create_strategy_tab()

        # 回测分析页
        self.create_backtest_tab()

        # 历史数据页
        self.create_data_tab()

        # 高级分析页
        self.create_advanced_analysis_tab()

        # 系统监控页
        self.create_monitor_tab()

    def create_prediction_tab(self):
        """创建预测结果页"""
        pred_frame = ttk.Frame(self.notebook)
        self.notebook.add(pred_frame, text="🎯 预测结果")

        # 左侧：预测配置
        left_frame = ttk.LabelFrame(pred_frame, text="预测配置", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10), pady=5)

        # 预测模式选择
        ttk.Label(left_frame, text="预测模式:").pack(anchor=tk.W)
        self.pred_mode_var = tk.StringVar(value="完整预测")
        mode_combo = ttk.Combobox(
            left_frame,
            textvariable=self.pred_mode_var,
            values=["完整预测", "仅传统策略", "仅机器学习", "自定义"],
            state="readonly",
            width=15
        )
        mode_combo.pack(fill=tk.X, pady=(0, 10))

        # 输出号码数量
        ttk.Label(left_frame, text="输出号码数:").pack(anchor=tk.W)
        self.num_count_var = tk.IntVar(value=14)
        num_spin = ttk.Spinbox(
            left_frame,
            from_=8,
            to=20,
            textvariable=self.num_count_var,
            width=15
        )
        num_spin.pack(fill=tk.X, pady=(0, 10))

        # 置信度阈值
        ttk.Label(left_frame, text="置信度阈值:").pack(anchor=tk.W)
        self.confidence_var = tk.DoubleVar(value=0.1)
        conf_scale = ttk.Scale(
            left_frame,
            from_=0.0,
            to=1.0,
            variable=self.confidence_var,
            orient=tk.HORIZONTAL
        )
        conf_scale.pack(fill=tk.X, pady=(0, 5))

        self.conf_label = ttk.Label(left_frame, text="0.10")
        self.conf_label.pack(anchor=tk.W, pady=(0, 10))
        conf_scale.configure(command=self.update_confidence_label)

        # 右侧：预测结果
        right_frame = ttk.LabelFrame(pred_frame, text="预测结果", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)

        # 推荐号码显示
        self.numbers_frame = ttk.Frame(right_frame)
        self.numbers_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(self.numbers_frame, text="推荐号码:", font=("Arial", 12, "bold")).pack(anchor=tk.W)

        # 号码显示区域
        self.numbers_display = ttk.Frame(self.numbers_frame)
        self.numbers_display.pack(fill=tk.X, pady=5)

        # 预测信息
        info_frame = ttk.LabelFrame(right_frame, text="预测信息", padding=5)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建信息显示标签
        self.info_labels = {}
        info_items = [
            ("置信度", "confidence"),
            ("使用策略", "strategies"),
            ("使用模型", "models"),
            ("执行时间", "time"),
            ("预测时间", "datetime")
        ]

        for i, (label, key) in enumerate(info_items):
            row = i // 2
            col = i % 2

            ttk.Label(info_frame, text=f"{label}:").grid(
                row=row, column=col*2, sticky=tk.W, padx=(0, 5), pady=2
            )
            self.info_labels[key] = ttk.Label(info_frame, text="--")
            self.info_labels[key].grid(
                row=row, column=col*2+1, sticky=tk.W, padx=(0, 20), pady=2
            )

        # 详细结果
        detail_frame = ttk.LabelFrame(right_frame, text="详细分析", padding=5)
        detail_frame.pack(fill=tk.BOTH, expand=True)

        # 创建树形视图显示策略详情
        columns = ("策略/模型", "推荐号码", "置信度", "权重")
        self.detail_tree = ttk.Treeview(detail_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.detail_tree.heading(col, text=col)
            self.detail_tree.column(col, width=120)

        # 添加滚动条
        detail_scroll = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_tree.yview)
        self.detail_tree.configure(yscrollcommand=detail_scroll.set)

        self.detail_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_strategy_tab(self):
        """创建策略管理页"""
        strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(strategy_frame, text="⚙️ 策略管理")

        # 策略列表
        left_frame = ttk.LabelFrame(strategy_frame, text="策略列表", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10), pady=5)

        # 策略树形视图
        strategy_columns = ("策略名称", "类型", "状态", "权重", "命中率")
        self.strategy_tree = ttk.Treeview(left_frame, columns=strategy_columns, show="headings")

        for col in strategy_columns:
            self.strategy_tree.heading(col, text=col)
            self.strategy_tree.column(col, width=100)

        strategy_scroll = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.strategy_tree.yview)
        self.strategy_tree.configure(yscrollcommand=strategy_scroll.set)

        self.strategy_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        strategy_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 策略控制面板
        right_frame = ttk.LabelFrame(strategy_frame, text="策略控制", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # 策略操作按钮
        ttk.Button(right_frame, text="启用策略", command=self.enable_strategy).pack(fill=tk.X, pady=2)
        ttk.Button(right_frame, text="禁用策略", command=self.disable_strategy).pack(fill=tk.X, pady=2)
        ttk.Button(right_frame, text="调整权重", command=self.adjust_weight).pack(fill=tk.X, pady=2)

        ttk.Separator(right_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # 权重调整
        ttk.Label(right_frame, text="权重调整:").pack(anchor=tk.W)
        self.weight_var = tk.DoubleVar(value=1.0)
        weight_scale = ttk.Scale(
            right_frame,
            from_=0.0,
            to=2.0,
            variable=self.weight_var,
            orient=tk.HORIZONTAL
        )
        weight_scale.pack(fill=tk.X, pady=5)

        self.weight_label = ttk.Label(right_frame, text="1.00")
        self.weight_label.pack(anchor=tk.W)
        weight_scale.configure(command=self.update_weight_label)

        ttk.Button(right_frame, text="应用权重", command=self.apply_weight).pack(fill=tk.X, pady=10)

    def create_data_tab(self):
        """创建历史数据管理页"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="📊 历史数据")

        # 数据控制面板
        control_frame = ttk.LabelFrame(data_frame, text="数据管理", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 控制按钮行1
        control_row1 = ttk.Frame(control_frame)
        control_row1.pack(fill=tk.X, pady=2)

        ttk.Button(control_row1, text="➕ 手动输入", command=self.manual_input_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_row1, text="📁 导入CSV", command=self.import_csv_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_row1, text="💾 导出CSV", command=self.export_csv_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_row1, text="🔄 刷新数据", command=self.refresh_history_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_row1, text="📈 数据统计", command=self.show_data_statistics).pack(side=tk.LEFT, padx=(0, 5))

        # 控制按钮行2
        control_row2 = ttk.Frame(control_frame)
        control_row2.pack(fill=tk.X, pady=2)

        ttk.Label(control_row2, text="显示记录数:").pack(side=tk.LEFT)
        self.data_limit_var = tk.IntVar(value=100)
        ttk.Spinbox(
            control_row2,
            from_=50,
            to=1000,
            increment=50,
            textvariable=self.data_limit_var,
            width=10,
            command=self.refresh_history_data
        ).pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(control_row2, text="搜索期号:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(control_row2, textvariable=self.search_var, width=15)
        search_entry.pack(side=tk.LEFT, padx=(5, 5))
        search_entry.bind('<KeyRelease>', self.search_data)

        ttk.Button(control_row2, text="🔍 搜索", command=self.search_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_row2, text="❌ 清空", command=self.clear_search).pack(side=tk.LEFT)

        # 数据显示区域
        data_display_frame = ttk.LabelFrame(data_frame, text="历史开奖数据", padding=5)
        data_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建数据表格
        columns = ("期号", "开奖日期", "特码", "生肖", "五行", "波色", "大小", "单双", "尾数")
        self.data_tree = ttk.Treeview(data_display_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        column_widths = {"期号": 80, "开奖日期": 100, "特码": 60, "生肖": 60, "五行": 60, "波色": 60, "大小": 50, "单双": 50, "尾数": 50}
        for col in columns:
            self.data_tree.heading(col, text=col, command=lambda c=col: self.sort_data(c))
            self.data_tree.column(col, width=column_widths.get(col, 80), anchor=tk.CENTER)

        # 添加滚动条
        data_v_scroll = ttk.Scrollbar(data_display_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        data_h_scroll = ttk.Scrollbar(data_display_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=data_v_scroll.set, xscrollcommand=data_h_scroll.set)

        # 布局
        self.data_tree.grid(row=0, column=0, sticky="nsew")
        data_v_scroll.grid(row=0, column=1, sticky="ns")
        data_h_scroll.grid(row=1, column=0, sticky="ew")

        data_display_frame.grid_rowconfigure(0, weight=1)
        data_display_frame.grid_columnconfigure(0, weight=1)

        # 数据信息面板
        info_frame = ttk.LabelFrame(data_frame, text="数据信息", padding=10)
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建信息标签
        self.data_info_labels = {}
        info_items = [
            ("总记录数", "total_records"),
            ("最新期号", "latest_period"),
            ("最早期号", "earliest_period"),
            ("数据来源", "data_source"),
            ("最后更新", "last_update"),
            ("数据完整性", "data_integrity")
        ]

        for i, (label, key) in enumerate(info_items):
            row = i // 3
            col = i % 3

            ttk.Label(info_frame, text=f"{label}:").grid(
                row=row*2, column=col, sticky=tk.W, padx=10, pady=2
            )
            self.data_info_labels[key] = ttk.Label(
                info_frame,
                text="--",
                font=("Arial", 9, "bold")
            )
            self.data_info_labels[key].grid(
                row=row*2+1, column=col, sticky=tk.W, padx=10, pady=(0, 10)
            )

        # 绑定双击事件
        self.data_tree.bind('<Double-1>', self.on_data_double_click)

    def create_advanced_analysis_tab(self):
        """创建高级分析页面"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="🧠 高级分析")

        # 控制面板
        control_frame = ttk.LabelFrame(advanced_frame, text="Z-Score动态分析控制", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 控制按钮
        control_buttons = ttk.Frame(control_frame)
        control_buttons.pack(fill=tk.X, pady=5)

        ttk.Button(control_buttons, text="🔄 刷新分析", command=self.refresh_advanced_analysis).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_buttons, text="📊 生成报告", command=self.generate_advanced_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_buttons, text="📄 导出TXT", command=self.export_advanced_txt).pack(side=tk.LEFT, padx=(0, 5))

        # Z-Score阈值调整
        threshold_frame = ttk.Frame(control_frame)
        threshold_frame.pack(fill=tk.X, pady=5)

        ttk.Label(threshold_frame, text="Z-Score阈值:").pack(side=tk.LEFT)
        self.zscore_threshold_var = tk.DoubleVar(value=2.0)
        threshold_scale = ttk.Scale(
            threshold_frame,
            from_=1.0,
            to=4.0,
            variable=self.zscore_threshold_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        threshold_scale.pack(side=tk.LEFT, padx=(10, 5))

        self.threshold_label = ttk.Label(threshold_frame, text="2.00")
        self.threshold_label.pack(side=tk.LEFT)
        threshold_scale.configure(command=self.update_threshold_label)

        # 主要内容区域 - 使用Notebook创建子标签页
        self.advanced_notebook = ttk.Notebook(advanced_frame)
        self.advanced_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Z-Score分析标签页
        zscore_frame = ttk.Frame(self.advanced_notebook)
        self.advanced_notebook.add(zscore_frame, text="📊 Z-Score分析")

        # 回补预测标签页
        reback_frame = ttk.Frame(self.advanced_notebook)
        self.advanced_notebook.add(reback_frame, text="🔥 极限回补预测")

        # 创建Z-Score分析内容
        self.create_zscore_analysis_content(zscore_frame)

        # 创建回补预测内容
        self.create_reback_prediction_content(reback_frame)

    def create_zscore_analysis_content(self, parent_frame):
        """创建Z-Score分析内容"""
        # 使用PanedWindow分割
        main_paned = ttk.PanedWindow(parent_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧：候选小组列表
        left_frame = ttk.LabelFrame(main_paned, text="高Z-Score候选小组", padding=5)
        main_paned.add(left_frame, weight=1)

        # 候选小组表格
        candidates_columns = ("排名", "小组成员", "Z-Score", "遗漏期数", "紧迫度", "推荐强度")
        self.candidates_tree = ttk.Treeview(left_frame, columns=candidates_columns, show="headings", height=12)

        for col in candidates_columns:
            self.candidates_tree.heading(col, text=col)
            if col == "小组成员":
                self.candidates_tree.column(col, width=120)
            elif col == "Z-Score":
                self.candidates_tree.column(col, width=80)
            else:
                self.candidates_tree.column(col, width=70)

        # 滚动条
        candidates_scroll = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.candidates_tree.yview)
        self.candidates_tree.configure(yscrollcommand=candidates_scroll.set)

        self.candidates_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        candidates_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 右侧：详细分析
        right_frame = ttk.LabelFrame(main_paned, text="详细分析", padding=5)
        main_paned.add(right_frame, weight=1)

        # 系统状态
        status_frame = ttk.LabelFrame(right_frame, text="系统状态", padding=5)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        self.advanced_status_labels = {}
        status_items = [
            ("总小组数", "total_groups"),
            ("活跃候选", "active_candidates"),
            ("平均Z-Score", "avg_z_score"),
            ("最高Z-Score", "max_z_score")
        ]

        for i, (label, key) in enumerate(status_items):
            row = i // 2
            col = i % 2

            ttk.Label(status_frame, text=f"{label}:").grid(
                row=row*2, column=col, sticky=tk.W, padx=10, pady=2
            )
            self.advanced_status_labels[key] = ttk.Label(
                status_frame,
                text="--",
                font=("Arial", 9, "bold")
            )
            self.advanced_status_labels[key].grid(
                row=row*2+1, column=col, sticky=tk.W, padx=10, pady=(0, 10)
            )

        # 推荐号码
        recommend_frame = ttk.LabelFrame(right_frame, text="高级推荐号码", padding=5)
        recommend_frame.pack(fill=tk.X, pady=(0, 10))

        self.advanced_numbers_display = ttk.Frame(recommend_frame)
        self.advanced_numbers_display.pack(fill=tk.X, pady=5)

        # 能量分析
        energy_frame = ttk.LabelFrame(right_frame, text="生肖能量分析", padding=5)
        energy_frame.pack(fill=tk.BOTH, expand=True)

        # 特定小组历史信息显示区域
        group_info_frame = ttk.LabelFrame(energy_frame, text="选中小组历史信息 (2020-2025年统计)", padding=5)
        group_info_frame.pack(fill=tk.X, pady=(0, 10))

        # 添加快速查看按钮
        info_button_frame = tk.Frame(group_info_frame)
        info_button_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(info_button_frame, text="🔥 查看最紧迫小组",
                  command=self.show_most_urgent_groups).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(info_button_frame, text="📊 刷新历史数据",
                  command=self.refresh_group_analyzer).pack(side=tk.LEFT)

        self.group_info_text = tk.Text(group_info_frame, height=6, wrap=tk.WORD,
                                      font=("Consolas", 9), bg="#f0f0f0")
        self.group_info_text.pack(fill=tk.X)
        self.group_info_text.insert(tk.END, "请选择候选小组查看详细历史信息...\n\n💡 提示: 点击上方按钮可查看最紧迫的4肖组合")
        self.group_info_text.config(state=tk.DISABLED)

        # 能量分析表格
        energy_columns = ("生肖", "平均压力", "能量等级")
        self.energy_tree = ttk.Treeview(energy_frame, columns=energy_columns, show="headings", height=6)

        for col in energy_columns:
            self.energy_tree.heading(col, text=col)
            self.energy_tree.column(col, width=80, anchor=tk.CENTER)

        energy_scroll = ttk.Scrollbar(energy_frame, orient=tk.VERTICAL, command=self.energy_tree.yview)
        self.energy_tree.configure(yscrollcommand=energy_scroll.set)

        self.energy_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        energy_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.candidates_tree.bind('<<TreeviewSelect>>', self.on_candidate_select)

    def create_reback_prediction_content(self, parent_frame):
        """创建回补预测内容"""
        # 控制面板
        reback_control_frame = ttk.LabelFrame(parent_frame, text="极限回补预测控制", padding=10)
        reback_control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 控制按钮
        reback_buttons = ttk.Frame(reback_control_frame)
        reback_buttons.pack(fill=tk.X, pady=5)

        ttk.Button(reback_buttons, text="🔥 刷新回补预测", command=self.refresh_reback_prediction).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(reback_buttons, text="📄 生成回补报告", command=self.generate_reback_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(reback_buttons, text="📊 导出回补数据", command=self.export_reback_data).pack(side=tk.LEFT, padx=(0, 5))

        # 阈值调整
        reback_threshold_frame = ttk.Frame(reback_control_frame)
        reback_threshold_frame.pack(fill=tk.X, pady=5)

        ttk.Label(reback_threshold_frame, text="临界阈值:").pack(side=tk.LEFT)
        self.reback_threshold_var = tk.DoubleVar(value=0.70)
        reback_threshold_scale = ttk.Scale(
            reback_threshold_frame,
            from_=0.50,
            to=0.95,
            variable=self.reback_threshold_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        reback_threshold_scale.pack(side=tk.LEFT, padx=5)
        self.reback_threshold_label = ttk.Label(reback_threshold_frame, text="0.70")
        self.reback_threshold_label.pack(side=tk.LEFT)
        reback_threshold_scale.configure(command=self.update_reback_threshold_label)

        # 主要内容区域
        reback_main_paned = ttk.PanedWindow(parent_frame, orient=tk.HORIZONTAL)
        reback_main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧：回补候选列表
        reback_left_frame = ttk.LabelFrame(reback_main_paned, text="极限回补候选", padding=5)
        reback_main_paned.add(reback_left_frame, weight=1)

        # 回补候选表格
        reback_columns = ("排名", "小组成员", "临界比例", "回补评分", "预测模式", "推荐等级", "风险等级")
        self.reback_tree = ttk.Treeview(reback_left_frame, columns=reback_columns, show="headings", height=12)

        for col in reback_columns:
            self.reback_tree.heading(col, text=col)
            if col == "小组成员":
                self.reback_tree.column(col, width=120)
            elif col == "预测模式":
                self.reback_tree.column(col, width=100)
            else:
                self.reback_tree.column(col, width=80)

        # 滚动条
        reback_scroll = ttk.Scrollbar(reback_left_frame, orient=tk.VERTICAL, command=self.reback_tree.yview)
        self.reback_tree.configure(yscrollcommand=reback_scroll.set)

        self.reback_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reback_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 右侧：回补详情和状态
        reback_right_frame = ttk.LabelFrame(reback_main_paned, text="回补分析详情", padding=5)
        reback_main_paned.add(reback_right_frame, weight=1)

        # 系统状态显示
        reback_status_frame = ttk.LabelFrame(reback_right_frame, text="回补预测状态", padding=5)
        reback_status_frame.pack(fill=tk.X, pady=(0, 10))

        self.reback_status_text = tk.Text(reback_status_frame, height=4, wrap=tk.WORD,
                                         font=("Consolas", 9), bg="#f0f0f0")
        self.reback_status_text.pack(fill=tk.X)
        self.reback_status_text.insert(tk.END, "点击'🔥 刷新回补预测'开始分析...")
        self.reback_status_text.config(state=tk.DISABLED)

        # 选中候选详情
        reback_detail_frame = ttk.LabelFrame(reback_right_frame, text="选中候选详细分析", padding=5)
        reback_detail_frame.pack(fill=tk.BOTH, expand=True)

        self.reback_detail_text = tk.Text(reback_detail_frame, wrap=tk.WORD,
                                         font=("Consolas", 9), bg="#f0f0f0")
        reback_detail_scroll = ttk.Scrollbar(reback_detail_frame, orient=tk.VERTICAL, command=self.reback_detail_text.yview)
        self.reback_detail_text.configure(yscrollcommand=reback_detail_scroll.set)

        self.reback_detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        reback_detail_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        self.reback_detail_text.insert(tk.END, "请选择回补候选查看详细分析...")
        self.reback_detail_text.config(state=tk.DISABLED)

        # 绑定选择事件
        self.reback_tree.bind('<<TreeviewSelect>>', self.on_reback_candidate_select)

    def create_backtest_tab(self):
        """创建回测分析页"""
        backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(backtest_frame, text="📊 回测分析")

        # 回测配置
        config_frame = ttk.LabelFrame(backtest_frame, text="回测配置", padding=10)
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 配置选项
        config_row1 = ttk.Frame(config_frame)
        config_row1.pack(fill=tk.X, pady=2)

        ttk.Label(config_row1, text="回测期数:").pack(side=tk.LEFT)
        self.backtest_periods_var = tk.IntVar(value=100)
        ttk.Spinbox(
            config_row1,
            from_=10,
            to=500,
            textvariable=self.backtest_periods_var,
            width=10
        ).pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(config_row1, text="验证比例:").pack(side=tk.LEFT)
        self.validation_ratio_var = tk.DoubleVar(value=0.2)
        ttk.Spinbox(
            config_row1,
            from_=0.1,
            to=0.5,
            increment=0.1,
            textvariable=self.validation_ratio_var,
            width=10
        ).pack(side=tk.LEFT, padx=(5, 20))

        ttk.Button(config_row1, text="开始回测", command=self.start_backtest).pack(side=tk.RIGHT, padx=(10, 0))

        # 进度显示
        progress_frame = ttk.Frame(config_frame)
        progress_frame.pack(fill=tk.X, pady=5)

        self.backtest_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.backtest_progress.pack(fill=tk.X, padx=(0, 10), side=tk.LEFT)

        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.pack(side=tk.RIGHT)

        # 回测结果显示
        result_frame = ttk.LabelFrame(backtest_frame, text="回测结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 结果文本区域
        self.backtest_text = scrolledtext.ScrolledText(
            result_frame,
            height=20,
            font=("Consolas", 10)
        )
        self.backtest_text.pack(fill=tk.BOTH, expand=True)

        # 导出按钮框架
        export_frame = ttk.Frame(result_frame)
        export_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(export_frame, text="📄 导出TXT", command=self.export_backtest_txt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(export_frame, text="📊 导出CSV", command=self.export_backtest_csv).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(export_frame, text="📋 导出JSON", command=self.export_backtest_json).pack(side=tk.LEFT)

    def create_monitor_tab(self):
        """创建系统监控页"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="📈 系统监控")

        # 系统状态
        status_frame = ttk.LabelFrame(monitor_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 状态信息
        self.status_labels = {}
        status_items = [
            ("系统状态", "system_status"),
            ("数据状态", "data_status"),
            ("策略数量", "strategy_count"),
            ("模型状态", "model_status"),
            ("最后预测", "last_prediction"),
            ("运行时间", "uptime")
        ]

        for i, (label, key) in enumerate(status_items):
            row = i // 3
            col = i % 3

            ttk.Label(status_frame, text=f"{label}:").grid(
                row=row*2, column=col, sticky=tk.W, padx=10, pady=2
            )
            self.status_labels[key] = ttk.Label(
                status_frame,
                text="--",
                font=("Arial", 9, "bold")
            )
            self.status_labels[key].grid(
                row=row*2+1, column=col, sticky=tk.W, padx=10, pady=(0, 10)
            )

        # 日志显示
        log_frame = ttk.LabelFrame(monitor_frame, text="系统日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            font=("Consolas", 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 日志控制
        log_control = ttk.Frame(log_frame)
        log_control.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(log_control, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_control, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(log_control, text="刷新状态", command=self.refresh_status).pack(side=tk.RIGHT)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态标签
        self.status_label = ttk.Label(
            self.status_bar,
            text="系统就绪",
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)

        # 进度条
        self.progress = ttk.Progressbar(
            self.status_bar,
            mode='indeterminate',
            length=200
        )
        self.progress.pack(side=tk.RIGHT, padx=2, pady=2)

    def load_initial_data(self):
        """加载初始数据"""
        self.log_message("系统启动中...")
        self.update_status("正在加载数据...")

        try:
            # 加载策略配置
            self.refresh_strategy_list()

            # 加载历史数据
            self.refresh_history_data()

            # 初始化高级分析
            self.refresh_advanced_analysis()

            # 更新系统状态
            self.refresh_status()

            self.log_message("系统启动完成")
            self.update_status("系统就绪")

        except Exception as e:
            self.log_message(f"启动失败: {e}")
            self.update_status("启动失败")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", "100.0")

    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_confidence_label(self, value):
        """更新置信度标签"""
        self.conf_label.config(text=f"{float(value):.2f}")

    def update_weight_label(self, value):
        """更新权重标签"""
        self.weight_label.config(text=f"{float(value):.2f}")

    # ==================== 事件处理方法 ====================

    def start_prediction(self):
        """开始预测并应用三层筛选"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        # 获取目标期号
        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        # 检查是否启用三层筛选
        use_filter = self.use_filter_var.get()
        if use_filter and not self.filter_engine:
            messagebox.showwarning("警告", "三层筛选系统不可用，将使用标准预测")
            print("⚠️ 三层筛选系统不可用，使用标准预测")
            use_filter = False

        try:
            self.is_predicting = True
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"🎯 开始预测 - 目标期号: {target_period}\n\n")

            # 调用预测引擎
            prediction_result = self.prediction_engine.predict(target_period)

            if not prediction_result or 'numbers' not in prediction_result:
                raise Exception("预测引擎返回结果无效")

            # 记录原始预测结果
            original_numbers = prediction_result['numbers']
            self.result_text.insert(tk.END, f"📊 原始预测结果: {original_numbers}\n")
            self.result_text.insert(tk.END, f"🎯 置信度: {prediction_result.get('confidence', 0):.2%}\n\n")

            # 应用三层筛选
            if use_filter:
                self.result_text.insert(tk.END, "=== 三层筛选过程 ===\n")
                filtered_numbers = original_numbers

                # 1. 大筛子
                if self.large_filter_var.get():
                    self.result_text.insert(tk.END, "1️⃣ 应用大筛子(生肖五行)...\n")
                    filtered_numbers = self.filter_engine.large_filter(filtered_numbers)
                    self.result_text.insert(tk.END, f"   结果: {filtered_numbers}\n")

                # 2. 中筛子
                if self.medium_filter_var.get():
                    self.result_text.insert(tk.END, "2️⃣ 应用中筛子(号码特征)...\n")
                    filtered_numbers = self.filter_engine.medium_filter(filtered_numbers)
                    self.result_text.insert(tk.END, f"   结果: {filtered_numbers}\n")

                # 3. 小筛子
                if self.small_filter_var.get():
                    self.result_text.insert(tk.END, "3️⃣ 应用小筛子(尾数组合)...\n")
                    tail_groups = self.filter_engine.small_filter(filtered_numbers)
                    self.result_text.insert(tk.END, "   尾数组合结果:\n")
                    for label, numbers in tail_groups.items():
                        self.result_text.insert(tk.END, f"   {label}: {numbers}\n")

                # 获取筛选统计
                stats = self.filter_engine.get_filter_statistics(original_numbers)
                self.result_text.insert(tk.END, "\n=== 筛选统计 ===\n")
                self.result_text.insert(tk.END, f"初始数量: {stats['initial_count']}\n")
                self.result_text.insert(tk.END, f"大筛后: {stats['after_large_filter']}\n")
                self.result_text.insert(tk.END, f"中筛后: {stats['after_medium_filter']}\n")
                self.result_text.insert(tk.END, f"小筛组数: {stats['tail_group_count']}\n")

            self.result_text.see(tk.END)
            self.last_prediction_result = prediction_result

        except Exception as e:
            messagebox.showerror("错误", f"预测过程出错: {str(e)}")
            self.result_text.insert(tk.END, f"\n❌ 错误: {str(e)}\n")
        finally:
            self.is_predicting = False
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        target_period = self.period_var.get().strip()
        if not target_period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        self.is_predicting = True
        self.predict_btn.config(text="预测中...", state="disabled")
        self.progress.start()
        self.update_status("正在运行预测...")

        # 在后台线程中运行预测
        threading.Thread(target=self._run_prediction, args=(target_period,), daemon=True).start()

    def _run_prediction(self, target_period):
        """后台运行预测"""
        try:
            self.log_message(f"开始预测期号: {target_period}")

            # 强制重新加载最新的策略配置
            self.prediction_engine.dsl_parser.load_config()
            self.log_message("🎯 使用真实预测引擎进行分析...")

            # 运行预测（适配器内部调用真实预测引擎）
            result = self.prediction_engine.run_prediction(target_period)
            self.last_prediction_result = result

            # 在主线程中更新UI
            self.root.after(0, self._update_prediction_ui, result)

        except Exception as e:
            error_msg = f"预测失败: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("预测失败", error_msg))
        finally:
            self.root.after(0, self._prediction_finished)

    def _update_prediction_ui(self, result):
        """更新预测结果UI"""
        # 清空之前的号码显示
        for widget in self.numbers_display.winfo_children():
            widget.destroy()

        # 显示推荐号码
        for i, number in enumerate(result.final_numbers):
            number_label = tk.Label(
                self.numbers_display,
                text=f"{number:02d}",
                font=("Arial", 16, "bold"),
                bg="#4CAF50" if i < 6 else "#FF9800",
                fg="white",
                width=3,
                height=1,
                relief=tk.RAISED,
                bd=2
            )
            number_label.pack(side=tk.LEFT, padx=2, pady=2)

        # 更新预测信息
        confidence_str = "{:.2%}".format(result.confidence_score)
        self.info_labels["confidence"].config(text=confidence_str)
        self.info_labels["strategies"].config(text=str(result.total_strategies_used) + "个")
        self.info_labels["models"].config(text=f"{result.total_models_used}个")
        self.info_labels["time"].config(text=f"{result.execution_time:.2f}秒")
        self.info_labels["datetime"].config(text=result.prediction_date)

        # 更新详细结果树
        self.detail_tree.delete(*self.detail_tree.get_children())

        # 添加策略详情
        for strategy in result.strategy_details:
            numbers_str = ", ".join("{:02d}".format(n) for n in strategy.get('predicted_numbers', [])[:6])
            confidence_str = "{:.3f}".format(strategy.get('confidence', 0))
            weight_str = "{:.2f}".format(strategy.get('weight', 1.0))
            self.detail_tree.insert("", tk.END, values=(
                strategy.get('strategy_name', 'Unknown'),
                numbers_str + "...",
                confidence_str,
                weight_str
            ))

        # 添加模型详情
        for model in result.model_details:
            numbers_str = ", ".join("{:02d}".format(n) for n in model.get('predicted_numbers', [])[:6])
            model_name = "[ML] " + str(model.get('model_name', 'Unknown'))
            confidence_str = "{:.3f}".format(model.get('confidence', 0))
            self.detail_tree.insert("", tk.END, values=(
                model_name,
                numbers_str + "...",
                confidence_str,
                "ML"
            ))

        self.log_message(f"预测完成: {len(result.final_numbers)}个号码，置信度{result.confidence_score:.2%}")

    def _prediction_finished(self):
        """预测完成后的清理工作"""
        self.is_predicting = False
        self.predict_btn.config(text="🎯 开始预测", state="normal")
        self.progress.stop()
        self.update_status("预测完成")

    def refresh_strategy_list(self):
        """刷新策略列表"""
        self.strategy_tree.delete(*self.strategy_tree.get_children())

        try:
            # 注意：这里我们加载所有策略，而不仅仅是活动的，以便在UI中管理它们
            all_strategies = self.dsl_parser.strategies
            for strategy in all_strategies:
                self.strategy_tree.insert("", tk.END, values=(
                    strategy.name,
                    strategy.type,
                    "启用" if strategy.active else "禁用",
                    f"{strategy.weight:.2f}",
                    "N/A"  # 命中率需要从历史数据计算
                ))
        except Exception as e:
            self.log_message(f"刷新策略列表失败: {e}")

    def refresh_status(self):
        """刷新系统状态"""
        try:
            summary = self.prediction_engine.get_prediction_summary()

            self.status_labels["system_status"].config(text="正常", foreground="green")
            self.status_labels["data_status"].config(text="已加载", foreground="green")
            strategy_count = summary['available_strategies']
            model_count = summary['trained_models']
            self.status_labels["strategy_count"].config(text=str(strategy_count) + "个")
            self.status_labels["model_status"].config(text=str(model_count) + "个模型")

            if summary['last_prediction']:
                self.status_labels["last_prediction"].config(text=summary['last_prediction'])
            else:
                self.status_labels["last_prediction"].config(text="无")

            self.status_labels["uptime"].config(text="运行中")

        except Exception as e:
            self.log_message(f"刷新状态失败: {e}")

    def run_backtest(self):
        """运行回测"""
        self.notebook.select(2)  # 切换到回测页面
        self.start_backtest()

    def start_backtest(self):
        """开始回测"""
        if self.is_backtesting:
            messagebox.showwarning("警告", "回测正在进行中，请稍候...")
            return

        self.is_backtesting = True
        self.backtest_text.delete(1.0, tk.END)
        self.backtest_text.insert(tk.END, "正在准备回测分析...\n\n")
        self.update_status("正在运行回测...")

        # 重置进度条
        self.backtest_progress['value'] = 0
        self.progress_label.config(text="准备中...")

        # 在后台线程中运行回测
        threading.Thread(target=self._run_backtest, daemon=True).start()

    def _run_backtest(self):
        """后台运行回测"""
        try:
            # 进度回调函数
            def progress_callback(progress, message):
                self.root.after(0, self._update_backtest_progress, progress, message)

            # 获取回测参数
            periods = self.backtest_periods_var.get()

            self.root.after(0, lambda: self.backtest_text.insert(tk.END, f"开始回测分析 - 回测期数: {periods}\n"))

            # 运行回测（这里需要修改prediction_engine的run_backtest方法支持进度回调）
            result = self.prediction_engine.run_backtest()

            # 保存结果
            self.last_backtest_result = result

            self.root.after(0, self._update_backtest_ui, result)
        except Exception as e:
            error_msg = f"回测失败: {str(e)}"
            self.root.after(0, lambda: self.backtest_text.insert(tk.END, error_msg))
        finally:
            self.root.after(0, self._backtest_finished)

    def _update_backtest_ui(self, result):
        """更新回测结果UI"""
        if 'error' in result:
            self.backtest_text.insert(tk.END, f"回测错误: {result['error']}\n")
            return

        # 显示回测结果
        report = f"""
回测分析报告
{'='*50}

核心指标:
  命中率: {result.get('hit_rate', 0):.2%}
  命中次数: {result.get('hit_count', 0)}
  总期数: {result.get('total_periods', 0)}
  平均遗漏: {result.get('avg_miss_interval', 0):.2f}期
  最大遗漏: {result.get('max_miss_streak', 0)}期
  夏普比率: {result.get('sharpe_ratio', 0):.4f}

详细结果 (最近10期):
{'='*50}
"""
        self.backtest_text.insert(tk.END, report)

        # 显示详细结果
        if 'details' in result:
            for i, detail in enumerate(result['details'][:10], 1):
                period = detail.get('period', '')
                predicted = detail.get('predicted', [])
                actual = detail.get('actual', '')
                is_hit = detail.get('is_hit', False)

                pred_str = ", ".join("{:02d}".format(n) for n in predicted[:8])
                status = "✓ 命中" if is_hit else "✗ 未中"

                line = "{:2d}. 期号:{} 预测:[{}...] 实际:{:02d} {}\n".format(i, period, pred_str, actual, status)
                self.backtest_text.insert(tk.END, line)

        self.update_status("回测完成")
        self.log_message("回测分析完成")

    def _update_backtest_progress(self, progress, message):
        """更新回测进度"""
        self.backtest_progress['value'] = progress
        self.progress_label.config(text=message)
        self.backtest_text.insert(tk.END, f"{message}\n")
        self.backtest_text.see(tk.END)

    def _backtest_finished(self):
        """回测完成后的清理工作"""
        self.is_backtesting = False
        self.backtest_progress['value'] = 100
        self.progress_label.config(text="回测完成")
        self.update_status("回测完成")

    def generate_report(self):
        """生成报告"""
        if not self.last_prediction_result:
            messagebox.showwarning("警告", "请先运行预测")
            return

        try:
            report_file = self.report_generator.generate_prediction_report(self.last_prediction_result)
            messagebox.showinfo("成功", f"报告已生成: {report_file}")
            self.log_message(f"报告已生成: {report_file}")
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")

    def _update_strategy_status(self, strategy_name: str, active: bool):
        """更新策略状态的辅助函数"""
        try:
            config_path = 'strategy_config.yaml'
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            strategy_found = False
            for strategy in config.get('strategies', []):
                if strategy.get('name') == strategy_name:
                    strategy['active'] = active
                    strategy_found = True
                    break

            if not strategy_found:
                messagebox.showerror("错误", f"未在配置文件中找到策略: {strategy_name}")
                return

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, allow_unicode=True, sort_keys=False)

            self.log_message(f"策略 '{strategy_name}' 已被 {'启用' if active else '禁用'}")
            self.reload_strategies() # 重新加载并刷新UI

        except Exception as e:
            messagebox.showerror("错误", f"更新策略配置失败: {e}")
            self.log_message(f"更新策略配置失败: {e}")

    def enable_strategy(self):
        """启用策略"""
        selected_item = self.strategy_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择一个策略")
            return

        strategy_name = self.strategy_tree.item(selected_item[0])['values'][0]
        self._update_strategy_status(strategy_name, True)

    def disable_strategy(self):
        """禁用策略"""
        selected_item = self.strategy_tree.selection()
        if not selected_item:
            messagebox.showwarning("警告", "请选择一个策略")
            return

        strategy_name = self.strategy_tree.item(selected_item[0])['values'][0]
        self._update_strategy_status(strategy_name, False)

    def adjust_weight(self):
        """调整权重"""
        selected = self.strategy_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择一个策略")
            return

        # 实现权重调整逻辑
        messagebox.showinfo("提示", "权重调整功能开发中...")

    def apply_weight(self):
        """应用权重"""
        messagebox.showinfo("提示", "权重应用功能开发中...")

    def train_models(self):
        """训练模型"""
        self.update_status("正在训练模型...")
        self.log_message("开始训练机器学习模型...")

        # 在后台线程中训练模型
        threading.Thread(target=self._train_models, daemon=True).start()

    def _train_models(self):
        """后台训练模型"""
        try:
            history_data = self.prediction_engine.load_historical_data()
            results = self.prediction_engine.train_models(history_data)

            success_count = len([r for r in results.values() if 'error' not in r])
            message = f"模型训练完成，成功训练{success_count}个模型"

            self.root.after(0, lambda: self.log_message(message))
            self.root.after(0, lambda: self.update_status("模型训练完成"))

        except Exception as e:
            error_msg = f"模型训练失败: {e}"
            self.root.after(0, lambda: self.log_message(error_msg))

    def show_model_status(self):
        """显示模型状态"""
        messagebox.showinfo("模型状态", "模型状态查看功能开发中...")

    def open_strategy_config(self):
        """打开策略配置"""
        messagebox.showinfo("策略配置", "策略配置编辑器开发中...")

    def reload_strategies(self):
        """重新加载策略"""
        try:
            self.dsl_parser.load_config()
            self.refresh_strategy_list()
            self.log_message("策略配置已重新加载")
            messagebox.showinfo("成功", "策略配置已重新加载")
        except Exception as e:
            messagebox.showerror("错误", f"重新加载失败: {e}")

    def import_data(self):
        """导入数据"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if file_path:
            messagebox.showinfo("提示", f"数据导入功能开发中...\n选择的文件: {file_path}")

    def export_prediction(self):
        """导出预测结果"""
        if not self.last_prediction_result:
            messagebox.showwarning("警告", "没有预测结果可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存预测结果",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("JSON文件", "*.json"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                if file_path.endswith('.txt'):
                    exported_file = self.result_exporter.export_prediction_result(
                        self.last_prediction_result, 'txt', file_path[:-4]
                    )
                elif file_path.endswith('.json'):
                    exported_file = self.prediction_engine.export_prediction(
                        self.last_prediction_result, 'json', file_path[:-5]
                    )
                elif file_path.endswith('.csv'):
                    exported_file = self.prediction_engine.export_prediction(
                        self.last_prediction_result, 'csv', file_path[:-4]
                    )
                else:
                    # 默认使用TXT格式
                    exported_file = self.result_exporter.export_prediction_result(
                        self.last_prediction_result, 'txt', file_path
                    )

                messagebox.showinfo("成功", f"预测结果已导出到: {exported_file}")
                self.log_message(f"预测结果已导出: {exported_file}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    # ==================== 高级分析方法 ====================

    def refresh_advanced_analysis(self):
        """刷新高级分析"""
        try:
            self.update_status("正在运行高级分析...")
            self.log_message("开始高级Z-Score分析...")

            # 更新Z-Score阈值
            new_threshold = self.zscore_threshold_var.get()
            self.prediction_engine.advanced_zodiac_engine.z_score_threshold = new_threshold

            # 获取分析结果
            analysis_result = self.prediction_engine.get_advanced_zodiac_analysis()

            if 'error' in analysis_result:
                messagebox.showerror("错误", analysis_result['error'])
                return

            # 更新候选小组列表
            self.update_candidates_display(analysis_result['candidates'])

            # 更新系统状态
            self.update_advanced_status(analysis_result['system_status'])

            # 更新推荐号码
            self.update_advanced_numbers(analysis_result['top_recommendations'])

            # 更新能量分析
            self.update_energy_analysis(analysis_result['energy_analysis'])

            self.log_message(f"高级分析完成 - 发现 {len(analysis_result['candidates'])} 个候选小组")
            self.update_status("高级分析完成")

        except Exception as e:
            self.log_message(f"高级分析失败: {e}")
            messagebox.showerror("错误", f"高级分析失败: {e}")

    def update_candidates_display(self, candidates):
        """更新候选小组显示"""
        # 清空现有数据
        self.candidates_tree.delete(*self.candidates_tree.get_children())

        # 添加候选小组
        for candidate in candidates:
            members_str = ", ".join(candidate['members'])
            urgency_color = self.get_urgency_color(candidate['urgency_level'])

            z_score_str = "{:.2f}".format(candidate['z_score'])
            strength_str = "{:.1%}".format(candidate['recommendation_strength'])

            item = self.candidates_tree.insert("", tk.END, values=(
                candidate['rank'],
                members_str,
                z_score_str,
                candidate['current_miss'],
                candidate['urgency_level'],
                strength_str
            ))

            # 根据紧迫度设置颜色
            if candidate['urgency_level'] == "极高":
                self.candidates_tree.set(item, "紧迫度", "🔥 极高")
            elif candidate['urgency_level'] == "高":
                self.candidates_tree.set(item, "紧迫度", "⚡ 高")
            else:
                self.candidates_tree.set(item, "紧迫度", "💧 中等")

    def update_advanced_status(self, status):
        """更新高级分析状态"""
        total_groups = status.get('total_groups', 0)
        total_groups_str = "{:,}".format(total_groups)
        self.advanced_status_labels["total_groups"].config(text=total_groups_str)

        active_candidates = status.get('active_candidates', 0)
        self.advanced_status_labels["active_candidates"].config(text=str(active_candidates))

        avg_z_score = status.get('avg_z_score', 0)
        avg_z_score_str = "{:.3f}".format(avg_z_score)
        self.advanced_status_labels["avg_z_score"].config(text=avg_z_score_str)

        max_z_score = status.get('max_z_score', 0)
        max_z_score_str = "{:.3f}".format(max_z_score)
        self.advanced_status_labels["max_z_score"].config(text=max_z_score_str)

    def update_advanced_numbers(self, numbers):
        """更新高级推荐号码"""
        # 清空之前的号码显示
        for widget in self.advanced_numbers_display.winfo_children():
            widget.destroy()

        if not numbers:
            ttk.Label(self.advanced_numbers_display, text="暂无推荐", foreground="gray").pack()
            return

        # 显示推荐号码
        for i, number in enumerate(numbers):
            number_label = tk.Label(
                self.advanced_numbers_display,
                text=f"{number:02d}",
                font=("Arial", 14, "bold"),
                bg="#8e44ad",  # 紫色表示高级分析
                fg="white",
                width=3,
                height=1,
                relief=tk.RAISED,
                bd=2
            )
            number_label.pack(side=tk.LEFT, padx=2, pady=2)

    def update_energy_analysis(self, energy_data):
        """更新能量分析"""
        # 清空现有数据
        self.energy_tree.delete(*self.energy_tree.get_children())

        if not energy_data:
            return

        # 按压力值排序
        sorted_energy = sorted(energy_data.items(), key=lambda x: x[1], reverse=True)

        for zodiac, pressure in sorted_energy:
            # 确定能量等级
            if pressure >= 15:
                level = "🔥 极高"
            elif pressure >= 10:
                level = "⚡ 高"
            elif pressure >= 5:
                level = "💧 中等"
            else:
                level = "❄️ 低"

            self.energy_tree.insert("", tk.END, values=(
                zodiac,
                f"{pressure:.1f}",
                level
            ))

    def on_candidate_select(self, event):
        """候选小组选择事件"""
        selection = self.candidates_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.candidates_tree.item(item)['values']

        if values:
            rank = values[0]
            members = values[1]
            z_score = values[2]
            current_miss = values[3]
            urgency = values[4]

            # 在日志中显示详细信息
            self.log_message(f"选择小组 #{rank}: {members}, Z-Score: {z_score}")

            # 更新小组历史信息显示
            self.update_group_history_info(members, current_miss, z_score, urgency)

    def update_group_history_info(self, members_str, current_miss, z_score, urgency):
        """更新小组历史信息显示（使用通用分析器）"""
        try:
            # 解析成员列表
            members = [m.strip() for m in members_str.split(',')]

            # 只分析4肖组合
            if len(members) != 4:
                info_text = f"小组: {members_str}\n当前只支持4肖组合的详细历史分析"
            else:
                # 使用通用分析器获取详细信息
                analysis_data = self.zodiac_analyzer.analyze_group(members)

                if analysis_data:
                    # 使用分析器的格式化方法
                    info_text = self.zodiac_analyzer.format_analysis_text(analysis_data)

                    # 添加组内能量信息
                    info_text += "\n\n⚡ 组内能量分析:"
                    for zodiac, energy_info in analysis_data['internal_energy'].items():
                        icon = energy_info['pressure_icon']
                        level = energy_info['pressure_level']
                        miss_count = energy_info['miss_count']
                        info_text += f"\n   {icon} {zodiac}: {miss_count} 期 - {level}压力"

                    # 添加遗漏分布信息（如果有数据）
                    if 'miss_distribution' in analysis_data:
                        info_text += "\n\n📊 遗漏分布统计:"
                        for range_name, dist_info in analysis_data['miss_distribution'].items():
                            count = dist_info['count']
                            percentage = dist_info['percentage']
                            if count > 0:
                                info_text += f"\n   {range_name}: {count} 次 ({percentage:.1f}%)"
                else:
                    info_text = f"小组: {members_str}\n未找到匹配的4肖组合数据"

            # 更新显示
            self.group_info_text.config(state=tk.NORMAL)
            self.group_info_text.delete(1.0, tk.END)
            self.group_info_text.insert(tk.END, info_text)
            self.group_info_text.config(state=tk.DISABLED)

        except Exception as e:
            error_text = f"获取小组历史信息失败: {e}\n请检查系统状态或重新刷新分析"
            self.group_info_text.config(state=tk.NORMAL)
            self.group_info_text.delete(1.0, tk.END)
            self.group_info_text.insert(tk.END, error_text)
            self.group_info_text.config(state=tk.DISABLED)

    def show_most_urgent_groups(self):
        """显示最紧迫的4肖组合（优化版本）"""
        # 使用线程避免界面卡死
        def _show_urgent_groups_thread():
            try:
                self.log_message("🔥 正在查找最紧迫的4肖组合...")

                # 显示进度提示
                self.group_info_text.config(state=tk.NORMAL)
                self.group_info_text.delete(1.0, tk.END)
                self.group_info_text.insert(tk.END, "🔄 正在分析最紧迫小组，请稍候...\n\n这可能需要几秒钟时间")
                self.group_info_text.config(state=tk.DISABLED)

                # 强制更新界面
                self.root.update()

                # 获取最紧迫的小组
                urgent_groups = self.zodiac_analyzer.get_top_groups_by_urgency(10)

                # 在主线程中更新界面
                self.root.after(0, self._update_urgent_groups_display, urgent_groups)

            except Exception as e:
                # 在主线程中显示错误
                self.root.after(0, self._show_urgent_groups_error, str(e))

        # 启动后台线程
        threading.Thread(target=_show_urgent_groups_thread, daemon=True).start()

    def _update_urgent_groups_display(self, urgent_groups):
        """更新紧迫小组显示（主线程）"""
        try:
            if urgent_groups:
                info_text = "🔥 当前最紧迫的10个4肖组合 (按Z-Score排序):\n"
                info_text += "="*60 + "\n\n"

                for i, group_data in enumerate(urgent_groups, 1):
                    members_str = ", ".join(group_data['members'])
                    z_score = group_data['z_score']
                    current_miss = group_data['current_miss']
                    max_miss = group_data['max_miss_history']

                    # 安全获取紧迫度信息
                    urgency_assessment = group_data.get('urgency_assessment', {})
                    urgency_icon = urgency_assessment.get('icon', '💧')
                    urgency_level = urgency_assessment.get('level', '中等')

                    info_text += f"{i:2d}. {urgency_icon} {members_str}\n"
                    info_text += f"    Z-Score: {z_score:.2f} | 当前: {current_miss}期 | 历史最大: {max_miss}期 | 紧迫度: {urgency_level}\n\n"

                info_text += "💡 点击候选小组列表中的任意小组可查看详细分析"

                self.group_info_text.config(state=tk.NORMAL)
                self.group_info_text.delete(1.0, tk.END)
                self.group_info_text.insert(tk.END, info_text)
                self.group_info_text.config(state=tk.DISABLED)

                self.log_message(f"✅ 找到 {len(urgent_groups)} 个紧迫小组")
            else:
                info_text = "📊 当前无紧迫的4肖组合\n\n可能原因:\n- Z-Score阈值过高\n- 系统数据需要刷新\n- 当前无统计学异常小组"

                self.group_info_text.config(state=tk.NORMAL)
                self.group_info_text.delete(1.0, tk.END)
                self.group_info_text.insert(tk.END, info_text)
                self.group_info_text.config(state=tk.DISABLED)

                self.log_message("⚠️ 未找到紧迫小组")

        except Exception as e:
            self._show_urgent_groups_error(str(e))

    def _show_urgent_groups_error(self, error_msg):
        """显示紧迫小组错误（主线程）"""
        error_text = f"查找紧迫小组失败: {error_msg}\n请检查系统状态或重新刷新分析"

        self.group_info_text.config(state=tk.NORMAL)
        self.group_info_text.delete(1.0, tk.END)
        self.group_info_text.insert(tk.END, error_text)
        self.group_info_text.config(state=tk.DISABLED)

        self.log_message(f"❌ 查找紧迫小组失败: {error_msg}")

    def refresh_group_analyzer(self):
        """刷新小组分析器"""
        try:
            self.log_message("📊 正在刷新历史数据分析器...")

            # 重新初始化分析器
            self.zodiac_analyzer = ZodiacGroupAnalyzer()

            info_text = "✅ 历史数据分析器已刷新\n\n"
            info_text += f"📊 系统状态:\n"
            info_text += f"- 总小组数: {len(self.zodiac_analyzer.engine.all_groups):,}\n"
            info_text += f"- 数据时间范围: 2020-2025年\n"
            info_text += f"- 分析维度: 4肖组合历史遗漏\n\n"
            info_text += "💡 现在可以选择候选小组查看最新的历史分析数据"

            self.group_info_text.config(state=tk.NORMAL)
            self.group_info_text.delete(1.0, tk.END)
            self.group_info_text.insert(tk.END, info_text)
            self.group_info_text.config(state=tk.DISABLED)

            self.log_message("✅ 历史数据分析器刷新完成")

        except Exception as e:
            error_text = f"刷新分析器失败: {e}\n请重启系统或检查数据文件"
            self.group_info_text.config(state=tk.NORMAL)
            self.group_info_text.delete(1.0, tk.END)
            self.group_info_text.insert(tk.END, error_text)
            self.group_info_text.config(state=tk.DISABLED)
            self.log_message(f"❌ 刷新分析器失败: {e}")

    def refresh_reback_prediction(self):
        """刷新回补预测"""
        try:
            self.log_message("🔥 正在进行极限回补预测分析...")
            self.update_status("正在分析极限回补...")

            # 更新阈值
            new_threshold = self.reback_threshold_var.get()
            self.reback_engine.critical_threshold = new_threshold

            # 获取回补候选
            candidates = self.reback_engine.predict_reback_candidates(20)

            # 更新回补候选显示
            self.update_reback_candidates_display(candidates)

            # 更新状态显示
            self.update_reback_status(candidates)

            self.log_message(f"✅ 回补预测完成 - 发现 {len(candidates)} 个候选")
            self.update_status("回补预测完成")

        except Exception as e:
            self.log_message(f"❌ 回补预测失败: {e}")
            self.update_status("回补预测失败")

    def update_reback_candidates_display(self, candidates):
        """更新回补候选显示"""
        # 清空现有数据
        self.reback_tree.delete(*self.reback_tree.get_children())

        if not candidates:
            return

        for i, candidate in enumerate(candidates, 1):
            members_str = ", ".join(candidate.members)
            critical_ratio = f"{candidate.critical_ratio:.1%}"
            reback_score = f"{candidate.reback_score:.3f}"
            predicted_pattern = candidate.predicted_pattern

            # 推荐等级显示
            stars = "★" * candidate.recommendation_level + "☆" * (5 - candidate.recommendation_level)
            recommendation_display = f"{stars} ({candidate.recommendation_level}/5)"

            # 风险等级颜色
            risk_color = self.get_risk_color(candidate.risk_level)

            item = self.reback_tree.insert("", tk.END, values=(
                i,
                members_str,
                critical_ratio,
                reback_score,
                predicted_pattern,
                recommendation_display,
                candidate.risk_level
            ))

            # 设置行颜色
            if candidate.recommendation_level >= 4:
                self.reback_tree.set(item, "推荐等级", f"🔥 {recommendation_display}")
            elif candidate.recommendation_level >= 3:
                self.reback_tree.set(item, "推荐等级", f"⚡ {recommendation_display}")

    def update_reback_status(self, candidates):
        """更新回补预测状态"""
        if not candidates:
            status_text = "📊 当前无极限回补候选\n可能原因: 临界阈值过高或无接近极限的组合"
        else:
            high_priority = len([c for c in candidates if c.recommendation_level >= 4])
            medium_priority = len([c for c in candidates if c.recommendation_level == 3])
            low_risk = len([c for c in candidates if c.risk_level == "低风险"])

            status_text = f"""📊 回补预测状态:
候选总数: {len(candidates)} 个
🔥 高优先级: {high_priority} 个 (≥4星)
⚡ 中优先级: {medium_priority} 个 (3星)
✅ 低风险: {low_risk} 个
临界阈值: {self.reback_threshold_var.get():.0%}"""

        self.reback_status_text.config(state=tk.NORMAL)
        self.reback_status_text.delete(1.0, tk.END)
        self.reback_status_text.insert(tk.END, status_text)
        self.reback_status_text.config(state=tk.DISABLED)

    def on_reback_candidate_select(self, event):
        """回补候选选择事件"""
        selection = self.reback_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.reback_tree.item(item)['values']

        if values:
            rank = values[0]
            members_str = values[1]

            # 获取详细信息
            try:
                # 从回补引擎获取候选详情
                candidates = self.reback_engine.predict_reback_candidates(20)
                target_candidate = None

                for candidate in candidates:
                    if ", ".join(candidate.members) == members_str:
                        target_candidate = candidate
                        break

                if target_candidate:
                    detail_text = self.format_reback_candidate_detail(target_candidate)
                else:
                    detail_text = f"未找到候选 {members_str} 的详细信息"

                self.reback_detail_text.config(state=tk.NORMAL)
                self.reback_detail_text.delete(1.0, tk.END)
                self.reback_detail_text.insert(tk.END, detail_text)
                self.reback_detail_text.config(state=tk.DISABLED)

                self.log_message(f"选择回补候选 #{rank}: {members_str}")

            except Exception as e:
                error_text = f"获取候选详情失败: {e}"
                self.reback_detail_text.config(state=tk.NORMAL)
                self.reback_detail_text.delete(1.0, tk.END)
                self.reback_detail_text.insert(tk.END, error_text)
                self.reback_detail_text.config(state=tk.DISABLED)

    def format_reback_candidate_detail(self, candidate):
        """格式化回补候选详情"""
        members_str = ", ".join(candidate.members)
        stars = "★" * candidate.recommendation_level + "☆" * (5 - candidate.recommendation_level)

        detail_text = f"""🔥 极限回补候选详细分析
{'='*50}

📊 基本信息:
小组: {members_str}
当前遗漏: {candidate.current_miss} 期
历史极限: {candidate.max_miss_history} 期
临界比例: {candidate.critical_ratio:.1%}

🎯 预测评估:
回补评分: {candidate.reback_score:.3f}
推荐等级: {stars} ({candidate.recommendation_level}/5)
预测模式: {candidate.predicted_pattern}
风险等级: {candidate.risk_level}
建议跟踪: {candidate.tracking_periods} 期

📈 历史回补模式:"""

        if candidate.historical_patterns:
            for i, pattern in enumerate(candidate.historical_patterns, 1):
                detail_text += f"""
{i}. {pattern.pattern_type}
   触发遗漏: {pattern.trigger_miss} 期
   回补窗口: {pattern.reback_window} 期
   命中频率: {pattern.hit_frequency} 次
   成功率: {pattern.success_rate:.1%}
   平均回补期数: {pattern.avg_reback_period:.1f} 期"""
        else:
            detail_text += "\n暂无历史回补数据 (首次触及极限)"

        detail_text += f"""

💡 投注建议:
根据历史回补模式，建议在未来 {candidate.tracking_periods} 期内重点关注该组合。
{candidate.predicted_pattern}的特点是回补相对{'快速' if '快速' in candidate.predicted_pattern else '稳定'}。

⚠️ 风险提示:
本预测基于历史统计分析，{candidate.risk_level}意味着历史回补表现{'较好' if candidate.risk_level == '低风险' else '一般' if candidate.risk_level == '中等风险' else '较差'}。
请结合其他策略综合判断，控制投注风险。"""

        return detail_text

    def update_reback_threshold_label(self, value):
        """更新回补阈值标签"""
        self.reback_threshold_label.config(text=f"{float(value):.2f}")

    def get_risk_color(self, risk_level):
        """获取风险等级颜色"""
        colors = {
            "低风险": "#27ae60",
            "中等风险": "#f39c12",
            "高风险": "#e74c3c"
        }
        return colors.get(risk_level, "#95a5a6")

    def generate_reback_report(self):
        """生成回补预测报告"""
        try:
            candidates = self.reback_engine.predict_reback_candidates(20)
            report = self.reback_engine.generate_reback_report(candidates)

            # 创建报告窗口
            report_window = tk.Toplevel(self.root)
            report_window.title("极限回补预测报告")
            report_window.geometry("900x700")

            # 报告文本显示
            report_text = scrolledtext.ScrolledText(report_window, wrap=tk.WORD, font=("Consolas", 10))
            report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            report_text.insert(tk.END, report)
            report_text.config(state=tk.DISABLED)

            self.log_message("✅ 回补预测报告生成完成")

        except Exception as e:
            self.log_message(f"❌ 生成回补报告失败: {e}")
            messagebox.showerror("错误", f"生成回补报告失败: {e}")

    def export_reback_data(self):
        """导出回补预测数据"""
        try:
            candidates = self.reback_engine.predict_reback_candidates(50)

            if not candidates:
                messagebox.showwarning("警告", "无回补候选数据可导出")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="保存回补预测数据"
            )

            if filename:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入标题行
                    writer.writerow([
                        "排名", "小组成员", "当前遗漏", "历史极限", "临界比例",
                        "回补评分", "预测模式", "推荐等级", "风险等级", "跟踪期数"
                    ])

                    # 写入数据行
                    for i, candidate in enumerate(candidates, 1):
                        writer.writerow([
                            i,
                            ", ".join(candidate.members),
                            candidate.current_miss,
                            candidate.max_miss_history,
                            f"{candidate.critical_ratio:.1%}",
                            f"{candidate.reback_score:.3f}",
                            candidate.predicted_pattern,
                            candidate.recommendation_level,
                            candidate.risk_level,
                            candidate.tracking_periods
                        ])

                self.log_message(f"✅ 回补数据已导出到: {filename}")
                messagebox.showinfo("成功", f"回补数据已导出到:\n{filename}")

        except Exception as e:
            self.log_message(f"❌ 导出回补数据失败: {e}")
            messagebox.showerror("错误", f"导出回补数据失败: {e}")

    def update_threshold_label(self, value):
        """更新阈值标签"""
        self.threshold_label.config(text=f"{float(value):.2f}")

    def get_urgency_color(self, urgency_level):
        """获取紧迫度颜色"""
        colors = {
            "极高": "#e74c3c",
            "高": "#f39c12",
            "中等": "#3498db"
        }
        return colors.get(urgency_level, "#95a5a6")

    def generate_advanced_report(self):
        """生成高级分析报告"""
        try:
            report = self.prediction_engine.generate_advanced_report()

            # 创建报告窗口
            report_window = tk.Toplevel(self.root)
            report_window.title("高级Z-Score分析报告")
            report_window.geometry("800x600")

            # 报告文本区域
            report_text = scrolledtext.ScrolledText(
                report_window,
                font=("Consolas", 10),
                wrap=tk.WORD
            )
            report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 插入报告内容
            report_text.insert(tk.END, report)
            report_text.config(state=tk.DISABLED)

            self.log_message("高级分析报告已生成")

        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")

    def export_advanced_txt(self):
        """导出高级分析TXT"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存高级分析报告",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if file_path:
                report = self.prediction_engine.generate_advanced_report()

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report)

                messagebox.showinfo("成功", f"高级分析报告已导出到: {file_path}")
                self.log_message(f"高级分析报告已导出: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def open_advanced_analysis(self):
        """打开高级分析页面"""
        self.notebook.select(3)  # 切换到高级分析页面（索引3）
        self.refresh_advanced_analysis()

    def open_three_layer_filter(self):
        """打开三层筛选窗口"""
        if hasattr(self, 'three_layer_window') and self.three_layer_window.winfo_exists():
            self.three_layer_window.lift()
            return

        self.create_three_layer_filter_window()

    def create_three_layer_filter_window(self):
        """创建三层筛选窗口"""
        self.three_layer_window = tk.Toplevel(self.root)
        self.three_layer_window.title("🎯 三层筛选预测系统")
        self.three_layer_window.geometry("1000x700")
        self.three_layer_window.resizable(True, True)

        # 设置窗口图标
        try:
            self.three_layer_window.iconbitmap("icon.ico")
        except:
            pass

        # 创建主框架
        main_frame = ttk.Frame(self.three_layer_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(
            title_frame,
            text="🎯 三层筛选预测系统",
            font=("Arial", 16, "bold")
        )
        title_label.pack(side=tk.LEFT)

        # 说明文本
        desc_label = ttk.Label(
            title_frame,
            text="大筛 → 中筛 → 小筛 → 精选特码（12~16个）",
            font=("Arial", 10),
            foreground="gray"
        )
        desc_label.pack(side=tk.RIGHT)

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 预测页面
        self.create_three_layer_prediction_tab(notebook)

        # 配置页面
        self.create_three_layer_config_tab(notebook)

        # 结果页面
        self.create_three_layer_results_tab(notebook)

        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 运行预测按钮
        self.three_layer_predict_btn = ttk.Button(
            button_frame,
            text="🚀 运行三层筛选预测",
            command=self.run_three_layer_prediction_action,
            style="Accent.TButton"
        )
        self.three_layer_predict_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 导出结果按钮
        ttk.Button(
            button_frame,
            text="📄 导出结果",
            command=self.export_three_layer_results
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        ttk.Button(
            button_frame,
            text="❌ 关闭",
            command=self.three_layer_window.destroy
        ).pack(side=tk.RIGHT)

    def create_three_layer_prediction_tab(self, notebook):
        """创建预测页面"""
        pred_frame = ttk.Frame(notebook)
        notebook.add(pred_frame, text="🎯 预测")

        # 期号输入框架
        input_frame = ttk.LabelFrame(pred_frame, text="预测设置", padding=10)
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        # 期号输入
        ttk.Label(input_frame, text="目标期号:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.three_layer_period_var = tk.StringVar(value="2025210")
        period_entry = ttk.Entry(input_frame, textvariable=self.three_layer_period_var, width=15)
        period_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 预测模式选择
        ttk.Label(input_frame, text="预测模式:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.three_layer_mode_var = tk.StringVar(value="标准模式")
        mode_combo = ttk.Combobox(
            input_frame,
            textvariable=self.three_layer_mode_var,
            values=["标准模式", "激进模式", "保守模式"],
            state="readonly",
            width=12
        )
        mode_combo.grid(row=0, column=3, sticky=tk.W)

        # 筛选过程显示
        process_frame = ttk.LabelFrame(pred_frame, text="筛选过程", padding=10)
        process_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建三层显示区域
        layers_frame = ttk.Frame(process_frame)
        layers_frame.pack(fill=tk.BOTH, expand=True)

        # 第一层：大筛子
        layer1_frame = ttk.LabelFrame(layers_frame, text="🔍 第一层：大筛子（多维策略组）", padding=5)
        layer1_frame.pack(fill=tk.X, pady=(0, 5))

        self.layer1_text = tk.Text(layer1_frame, height=4, wrap=tk.WORD)
        layer1_scroll = ttk.Scrollbar(layer1_frame, orient=tk.VERTICAL, command=self.layer1_text.yview)
        self.layer1_text.configure(yscrollcommand=layer1_scroll.set)
        self.layer1_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        layer1_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 第二层：中筛子
        layer2_frame = ttk.LabelFrame(layers_frame, text="🎛️ 第二层：中筛子（48码四组分布）", padding=5)
        layer2_frame.pack(fill=tk.X, pady=(0, 5))

        self.layer2_text = tk.Text(layer2_frame, height=4, wrap=tk.WORD)
        layer2_scroll = ttk.Scrollbar(layer2_frame, orient=tk.VERTICAL, command=self.layer2_text.yview)
        self.layer2_text.configure(yscrollcommand=layer2_scroll.set)
        self.layer2_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        layer2_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 第三层：小筛子
        layer3_frame = ttk.LabelFrame(layers_frame, text="🔬 第三层：小筛子（交叉特征筛）", padding=5)
        layer3_frame.pack(fill=tk.X, pady=(0, 5))

        self.layer3_text = tk.Text(layer3_frame, height=4, wrap=tk.WORD)
        layer3_scroll = ttk.Scrollbar(layer3_frame, orient=tk.VERTICAL, command=self.layer3_text.yview)
        self.layer3_text.configure(yscrollcommand=layer3_scroll.set)
        self.layer3_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        layer3_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 最终结果
        result_frame = ttk.LabelFrame(layers_frame, text="🎯 最终结果", padding=5)
        result_frame.pack(fill=tk.X)

        self.three_layer_result_text = tk.Text(result_frame, height=3, wrap=tk.WORD, font=("Arial", 12, "bold"))
        self.three_layer_result_text.pack(fill=tk.BOTH, expand=True)

    def create_three_layer_config_tab(self, notebook):
        """创建配置页面"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 配置")

        # 配置说明
        desc_frame = ttk.LabelFrame(config_frame, text="配置说明", padding=10)
        desc_frame.pack(fill=tk.X, padx=10, pady=10)

        desc_text = """三层筛选策略配置：
• 大筛子：多维策略组筛选（生肖+号码+波色+五行组合）
• 中筛子：48码四组分布筛选（平衡分组，属性均衡）
• 小筛子：交叉特征筛选（极限追踪+平衡反向）
• 输出控制：精选12-16个特码"""

        ttk.Label(desc_frame, text=desc_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 配置参数
        params_frame = ttk.LabelFrame(config_frame, text="参数设置", padding=10)
        params_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 输出数量控制
        ttk.Label(params_frame, text="输出号码数量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=5)

        range_frame = ttk.Frame(params_frame)
        range_frame.grid(row=0, column=1, sticky=tk.W, pady=5)

        self.three_layer_min_var = tk.StringVar(value="12")
        ttk.Entry(range_frame, textvariable=self.three_layer_min_var, width=5).pack(side=tk.LEFT)
        ttk.Label(range_frame, text=" ~ ").pack(side=tk.LEFT)
        self.three_layer_max_var = tk.StringVar(value="16")
        ttk.Entry(range_frame, textvariable=self.three_layer_max_var, width=5).pack(side=tk.LEFT)
        ttk.Label(range_frame, text=" 个").pack(side=tk.LEFT)

        # 置信度阈值
        ttk.Label(params_frame, text="置信度阈值:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=5)
        self.three_layer_confidence_var = tk.StringVar(value="0.6")
        ttk.Entry(params_frame, textvariable=self.three_layer_confidence_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=5)

        # 极限追踪开关
        self.three_layer_extreme_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            params_frame,
            text="启用极限追踪",
            variable=self.three_layer_extreme_var
        ).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 平衡反推开关
        self.three_layer_balance_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            params_frame,
            text="启用平衡反推",
            variable=self.three_layer_balance_var
        ).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

    def create_three_layer_results_tab(self, notebook):
        """创建结果页面"""
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="📊 结果")

        # 结果统计
        stats_frame = ttk.LabelFrame(results_frame, text="预测统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        # 统计标签
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        # 创建统计标签
        self.three_layer_stats = {}
        stats_items = [
            ("总预测次数", "total_predictions"),
            ("平均置信度", "avg_confidence"),
            ("筛选效率", "filter_efficiency"),
            ("极限命中率", "extreme_hit_rate")
        ]

        for i, (label, key) in enumerate(stats_items):
            row, col = i // 2, (i % 2) * 2
            ttk.Label(stats_grid, text=label + ":").grid(row=row, column=col, sticky=tk.W, padx=(0, 10), pady=2)
            self.three_layer_stats[key] = ttk.Label(stats_grid, text="0", foreground="blue")
            self.three_layer_stats[key].grid(row=row, column=col+1, sticky=tk.W, padx=(0, 20), pady=2)

        # 历史结果
        history_frame = ttk.LabelFrame(results_frame, text="历史预测记录", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建树形控件
        columns = ("期号", "推荐号码", "置信度", "筛选路径", "预测时间")
        self.three_layer_history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        for col in columns:
            self.three_layer_history_tree.heading(col, text=col)
            if col == "推荐号码":
                self.three_layer_history_tree.column(col, width=200)
            elif col == "筛选路径":
                self.three_layer_history_tree.column(col, width=120)
            else:
                self.three_layer_history_tree.column(col, width=100)

        # 滚动条
        history_scroll = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.three_layer_history_tree.yview)
        self.three_layer_history_tree.configure(yscrollcommand=history_scroll.set)

        self.three_layer_history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def run_three_layer_prediction_action(self):
        """运行三层筛选预测"""
        try:
            # 获取期号
            target_period = self.three_layer_period_var.get().strip()
            if not target_period:
                messagebox.showerror("错误", "请输入目标期号")
                return

            # 禁用按钮
            self.three_layer_predict_btn.config(state="disabled", text="🔄 预测中...")
            self.three_layer_window.update()

            # 清空之前的结果
            self.clear_three_layer_display()

            # 检查是否有三层筛选功能
            if hasattr(self.prediction_engine, 'run_three_layer_prediction'):
                # 使用三层筛选预测
                result = self.prediction_engine.run_three_layer_prediction(target_period)
                self.display_three_layer_result(result, target_period)
            else:
                # 降级到标准预测
                result = self.prediction_engine.run_prediction(target_period)
                self.display_standard_as_three_layer(result, target_period)

            # 更新统计
            self.update_three_layer_stats()

            # 添加到历史记录
            self.add_three_layer_history(target_period, result)

        except Exception as e:
            messagebox.showerror("错误", f"三层筛选预测失败: {e}")
            self.log_message(f"三层筛选预测错误: {e}")

        finally:
            # 恢复按钮
            self.three_layer_predict_btn.config(state="normal", text="🚀 运行三层筛选预测")

    def clear_three_layer_display(self):
        """清空三层筛选显示"""
        self.layer1_text.delete(1.0, tk.END)
        self.layer2_text.delete(1.0, tk.END)
        self.layer3_text.delete(1.0, tk.END)
        self.three_layer_result_text.delete(1.0, tk.END)

    def display_three_layer_result(self, result, target_period):
        """显示三层筛选结果"""
        try:
            # 获取筛选详情
            metadata = getattr(result, 'metadata', {})
            filter_details = metadata.get('filter_details', {})

            # 显示第一层结果
            layer1_details = filter_details.get('layer1', {})
            layer1_info = f"""多维策略组筛选完成：
• 原始号码池: {layer1_details.get('original_count', 48)} 个
• 生肖组合筛选: {layer1_details.get('zodiac_filtered_count', 0)} 个
• 号码组合筛选: {layer1_details.get('number_filtered_count', 0)} 个
• 波色组合筛选: {layer1_details.get('color_filtered_count', 0)} 个
• 五行组合筛选: {layer1_details.get('final_count', 0)} 个"""

            self.layer1_text.insert(tk.END, layer1_info)

            # 显示第二层结果
            layer2_details = filter_details.get('layer2', {})
            layer2_info = f"""48码四组分布筛选完成：
• 生成分组数: {layer2_details.get('groups_generated', 4)} 个
• 选中分组数: {layer2_details.get('selected_groups_count', 2)} 个
• 筛选后号码: {layer2_details.get('final_count', 0)} 个
• 平衡性评分: 优秀"""

            self.layer2_text.insert(tk.END, layer2_info)

            # 显示第三层结果
            layer3_details = filter_details.get('layer3', {})
            layer3_info = f"""交叉特征精筛完成：
• 交叉特征筛选: {layer3_details.get('cross_filtered_count', 0)} 个
• 极限追踪筛选: {layer3_details.get('extreme_filtered_count', 0)} 个
• 平衡反推筛选: {layer3_details.get('balance_filtered_count', 0)} 个
• 最终精选号码: {layer3_details.get('final_count', 0)} 个"""

            self.layer3_text.insert(tk.END, layer3_info)

            # 显示最终结果
            final_numbers = result.final_numbers
            confidence = result.confidence_score

            # 格式化号码显示
            numbers_str = ", ".join(f"{num:02d}" for num in final_numbers)

            result_info = f"""🎯 三层筛选预测结果：
期号: {target_period}
推荐号码: [{numbers_str}]
号码数量: {len(final_numbers)} 个
预测置信度: {confidence:.2%}
筛选路径: {layer1_details.get('original_count', 48)} → {layer1_details.get('final_count', 0)} → {layer2_details.get('final_count', 0)} → {layer3_details.get('final_count', 0)}"""

            self.three_layer_result_text.insert(tk.END, result_info)

            # 设置结果文本颜色
            self.three_layer_result_text.tag_add("result", "1.0", tk.END)
            self.three_layer_result_text.tag_config("result", foreground="darkblue")

        except Exception as e:
            error_info = f"结果显示错误: {e}\n\n基本信息:\n期号: {target_period}\n推荐号码: {getattr(result, 'final_numbers', [])}\n置信度: {getattr(result, 'confidence_score', 0):.2%}"
            self.three_layer_result_text.insert(tk.END, error_info)

    def display_standard_as_three_layer(self, result, target_period):
        """将标准预测结果显示为三层筛选格式"""
        # 模拟三层筛选过程
        self.layer1_text.insert(tk.END, "使用标准预测引擎（三层筛选系统不可用）\n大筛子: 多维策略分析完成")
        self.layer2_text.insert(tk.END, "中筛子: 号码分组和平衡性分析完成")
        self.layer3_text.insert(tk.END, "小筛子: 最终筛选和优化完成")

        # 显示结果
        numbers_str = ", ".join(f"{num:02d}" for num in result.final_numbers)
        result_info = f"""🎯 预测结果（标准模式）：
期号: {target_period}
推荐号码: [{numbers_str}]
号码数量: {len(result.final_numbers)} 个
预测置信度: {result.confidence_score:.2%}
使用策略: {result.total_strategies_used} 个"""

        self.three_layer_result_text.insert(tk.END, result_info)

    def update_three_layer_stats(self):
        """更新三层筛选统计"""
        try:
            # 模拟统计数据（实际应该从数据库或文件读取）
            stats = {
                "total_predictions": getattr(self, '_three_layer_count', 0) + 1,
                "avg_confidence": "75.5%",
                "filter_efficiency": "85.2%",
                "extreme_hit_rate": "28.3%"
            }

            # 更新计数
            self._three_layer_count = stats["total_predictions"]

            # 更新显示
            for key, value in stats.items():
                if key in self.three_layer_stats:
                    self.three_layer_stats[key].config(text=str(value))

        except Exception as e:
            self.log_message(f"统计更新错误: {e}")

    def add_three_layer_history(self, target_period, result):
        """添加三层筛选历史记录"""
        try:
            # 格式化数据
            numbers_str = ", ".join(f"{num:02d}" for num in result.final_numbers[:6])
            if len(result.final_numbers) > 6:
                numbers_str += "..."

            confidence_str = f"{result.confidence_score:.1%}"

            # 获取筛选路径
            metadata = getattr(result, 'metadata', {})
            filter_details = metadata.get('filter_details', {})

            if filter_details:
                layer1_count = filter_details.get('layer1', {}).get('final_count', 0)
                layer2_count = filter_details.get('layer2', {}).get('final_count', 0)
                layer3_count = filter_details.get('layer3', {}).get('final_count', 0)
                filter_path = f"48→{layer1_count}→{layer2_count}→{layer3_count}"
            else:
                filter_path = "标准模式"

            prediction_time = datetime.now().strftime("%H:%M:%S")

            # 插入到树形控件
            self.three_layer_history_tree.insert(
                "", 0,  # 插入到顶部
                values=(target_period, numbers_str, confidence_str, filter_path, prediction_time)
            )

            # 限制历史记录数量
            items = self.three_layer_history_tree.get_children()
            if len(items) > 20:  # 保留最近20条记录
                self.three_layer_history_tree.delete(items[-1])

        except Exception as e:
            self.log_message(f"历史记录添加错误: {e}")

    def export_three_layer_results(self):
        """导出三层筛选结果"""
        try:
            from tkinter import filedialog

            # 选择保存文件
            file_path = filedialog.asksaveasfilename(
                title="导出三层筛选结果",
                defaultextension=".txt",
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("CSV文件", "*.csv"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return

            # 获取当前结果
            result_content = self.three_layer_result_text.get(1.0, tk.END).strip()

            if not result_content:
                messagebox.showwarning("警告", "没有可导出的结果")
                return

            # 生成完整报告
            report = f"""三层筛选预测系统导出报告
{'='*50}
导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{result_content}

筛选过程详情:
{'='*30}

第一层（大筛子）:
{self.layer1_text.get(1.0, tk.END).strip()}

第二层（中筛子）:
{self.layer2_text.get(1.0, tk.END).strip()}

第三层（小筛子）:
{self.layer3_text.get(1.0, tk.END).strip()}

历史记录:
{'='*30}
"""

            # 添加历史记录
            for item in self.three_layer_history_tree.get_children():
                values = self.three_layer_history_tree.item(item)['values']
                report += f"期号: {values[0]}, 号码: {values[1]}, 置信度: {values[2]}, 路径: {values[3]}, 时间: {values[4]}\n"

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report)

            messagebox.showinfo("成功", f"结果已导出到: {file_path}")
            self.log_message(f"三层筛选结果已导出: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")
            self.log_message(f"三层筛选导出错误: {e}")

    def quick_three_layer_prediction(self):
        """快捷三层筛选预测"""
        try:
            # 获取当前期号
            target_period = self.period_var.get().strip()
            if not target_period:
                messagebox.showerror("错误", "请先输入目标期号")
                return

            # 禁用按钮
            self.quick_three_layer_btn.config(state="disabled", text="🔄 筛选中...")
            self.root.update()

            # 检查是否有三层筛选功能
            if hasattr(self.prediction_engine, 'run_three_layer_prediction'):
                # 使用三层筛选预测
                result = self.prediction_engine.run_three_layer_prediction(target_period)

                # 在主界面显示结果
                self.display_prediction_result(result)

                # 显示三层筛选特有信息
                self.show_three_layer_info(result, target_period)

                self.log_message(f"✅ 三层筛选预测完成 - 期号: {target_period}")
            else:
                # 降级到标准预测
                messagebox.showinfo("提示", "三层筛选系统不可用，使用标准预测")
                self.start_prediction()

        except Exception as e:
            messagebox.showerror("错误", f"三层筛选预测失败: {e}")
            self.log_message(f"三层筛选预测错误: {e}")

        finally:
            # 恢复按钮
            self.quick_three_layer_btn.config(state="normal", text="🎯 三层筛选")

    def show_three_layer_info(self, result, target_period):
        """显示三层筛选特有信息"""
        try:
            # 获取筛选详情
            metadata = getattr(result, 'metadata', {})
            filter_details = metadata.get('filter_details', {})

            if filter_details:
                # 构建筛选路径信息
                layer1_count = filter_details.get('layer1', {}).get('final_count', 0)
                layer2_count = filter_details.get('layer2', {}).get('final_count', 0)
                layer3_count = filter_details.get('layer3', {}).get('final_count', 0)

                filter_path = f"48 → {layer1_count} → {layer2_count} → {layer3_count}"

                # 在日志中显示三层筛选信息
                self.log_message(f"🎯 三层筛选路径: {filter_path}")
                self.log_message(f"📊 最终精选: {len(result.final_numbers)} 个号码")
                self.log_message(f"🔥 预测置信度: {result.confidence_score:.2%}")

                # 显示极限分析信息
                extreme_analysis = metadata.get('extreme_analysis', {})
                if extreme_analysis:
                    critical_count = len(extreme_analysis.get('critical_combinations', []))
                    extreme_count = len(extreme_analysis.get('extreme_combinations', []))

                    if critical_count > 0 or extreme_count > 0:
                        self.log_message(f"⚠️ 极限预警: {critical_count} 个危险组合, {extreme_count} 个极限组合")

                # 弹出简要结果窗口
                self.show_three_layer_summary(result, target_period, filter_path)

        except Exception as e:
            self.log_message(f"三层筛选信息显示错误: {e}")

    def show_three_layer_summary(self, result, target_period, filter_path):
        """显示三层筛选摘要窗口"""
        try:
            # 创建摘要窗口
            summary_window = tk.Toplevel(self.root)
            summary_window.title("🎯 三层筛选结果摘要")
            summary_window.geometry("500x400")
            summary_window.resizable(False, False)

            # 居中显示
            summary_window.transient(self.root)
            summary_window.grab_set()

            # 主框架
            main_frame = ttk.Frame(summary_window, padding=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(
                main_frame,
                text="🎯 三层筛选预测结果",
                font=("Arial", 14, "bold")
            )
            title_label.pack(pady=(0, 20))

            # 结果信息
            info_frame = ttk.LabelFrame(main_frame, text="预测信息", padding=10)
            info_frame.pack(fill=tk.X, pady=(0, 10))

            # 期号
            ttk.Label(info_frame, text="目标期号:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
            ttk.Label(info_frame, text=target_period, foreground="blue").grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

            # 筛选路径
            ttk.Label(info_frame, text="筛选路径:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
            ttk.Label(info_frame, text=filter_path, foreground="green").grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

            # 置信度
            ttk.Label(info_frame, text="预测置信度:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=2)
            confidence_text = f"{result.confidence_score:.2%}"
            ttk.Label(info_frame, text=confidence_text, foreground="red").grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

            # 推荐号码
            numbers_frame = ttk.LabelFrame(main_frame, text="推荐号码", padding=10)
            numbers_frame.pack(fill=tk.X, pady=(0, 10))

            # 格式化号码显示
            numbers_text = tk.Text(numbers_frame, height=4, wrap=tk.WORD, font=("Arial", 12, "bold"))
            numbers_text.pack(fill=tk.X)

            numbers_str = ", ".join(f"{num:02d}" for num in result.final_numbers)
            numbers_text.insert(tk.END, f"[{numbers_str}]\n\n")
            numbers_text.insert(tk.END, f"共 {len(result.final_numbers)} 个号码")
            numbers_text.config(state=tk.DISABLED)

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 打开详细窗口按钮
            ttk.Button(
                button_frame,
                text="📊 查看详细分析",
                command=lambda: (summary_window.destroy(), self.open_three_layer_filter())
            ).pack(side=tk.LEFT, padx=(0, 10))

            # 关闭按钮
            ttk.Button(
                button_frame,
                text="✅ 确定",
                command=summary_window.destroy
            ).pack(side=tk.RIGHT)

        except Exception as e:
            self.log_message(f"摘要窗口显示错误: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.refresh_strategy_list()
        self.refresh_status()
        self.log_message("数据已刷新")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"日志已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def show_help(self):
        """显示帮助"""
        help_text = """
六合彩智能预测系统使用说明

1. 预测功能:
   - 在工具栏输入目标期号
   - 点击"开始预测"按钮
   - 查看预测结果和详细分析

2. 策略管理:
   - 在策略管理页面查看所有策略
   - 可以启用/禁用策略
   - 调整策略权重

3. 回测分析:
   - 运行历史回测验证策略效果
   - 查看命中率、遗漏等指标

4. 系统监控:
   - 查看系统运行状态
   - 监控日志信息

更多功能正在开发中...
        """
        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于"""
        about_text = """
六合彩智能预测系统 v1.0

基于多维策略融合与机器学习的智能预测系统

特性:
• 多维组合分析 (生肖、五行、波色)
• 极限统计追踪
• 机器学习模型融合
• 智能回测评估
• 可视化报告生成

开发: AI Assistant
技术: Python + tkinter + scikit-learn
        """
        messagebox.showinfo("关于", about_text)

    # ==================== 历史数据管理方法 ====================

    def refresh_history_data(self):
        """刷新历史数据显示"""
        try:
            # 清空现有数据
            self.data_tree.delete(*self.data_tree.get_children())

            # 重新加载数据管理器的数据
            self.data_manager.load_data()
            history_data = self.data_manager.get_all_records()

            if not history_data:
                self.log_message("没有找到历史数据")
                return

            # 限制显示数量
            limit = self.data_limit_var.get()
            display_data = history_data[:limit]

            # 填充数据到表格
            for record in display_data:
                period = record.get('period', '')
                date = record.get('draw_date', '')
                special_code = record.get('special_code', '')
                zodiac = record.get('zodiac', '')
                five_element = record.get('five_element', '')
                wave_color = record.get('wave_color', '')

                # 计算大小单双尾数
                if special_code:
                    big_small = "大" if special_code >= 25 else "小"
                    odd_even = "单" if special_code % 2 == 1 else "双"
                    tail = special_code % 10
                else:
                    big_small = odd_even = tail = ""

                self.data_tree.insert("", tk.END, values=(
                    period, date, special_code, zodiac, five_element,
                    wave_color, big_small, odd_even, tail
                ))

            # 更新数据信息
            self.update_data_info(history_data)

            self.log_message(f"已加载 {len(display_data)} 条历史记录")

        except Exception as e:
            self.log_message(f"刷新历史数据失败: {e}")
            messagebox.showerror("错误", f"加载历史数据失败: {e}")

    def update_data_info(self, history_data):
        """更新数据信息"""
        if not history_data:
            return

        try:
            total_records = len(history_data)
            latest_period = history_data[0].get('period', '') if history_data else ''
            earliest_period = history_data[-1].get('period', '') if history_data else ''

            # 检查数据完整性
            complete_records = sum(1 for record in history_data
                                 if record.get('special_code') and record.get('period'))
            integrity = f"{complete_records}/{total_records} ({complete_records/total_records*100:.1f}%)"

            # 更新标签
            self.data_info_labels["total_records"].config(text=str(total_records))
            self.data_info_labels["latest_period"].config(text=latest_period)
            self.data_info_labels["earliest_period"].config(text=earliest_period)
            self.data_info_labels["data_source"].config(text="CSV文件")
            self.data_info_labels["last_update"].config(text=datetime.now().strftime('%Y-%m-%d %H:%M'))
            self.data_info_labels["data_integrity"].config(text=integrity)

        except Exception as e:
            self.log_message(f"更新数据信息失败: {e}")

    def import_csv_data(self):
        """导入CSV数据"""
        file_path = filedialog.askopenfilename(
            title="选择CSV数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            self.update_status("正在导入数据...")
            self.log_message(f"开始导入数据: {file_path}")

            # 这里可以添加CSV导入逻辑
            # 暂时显示提示信息
            messagebox.showinfo("提示", f"CSV导入功能开发中...\n选择的文件: {os.path.basename(file_path)}")

            # 刷新数据显示
            self.refresh_history_data()

            self.update_status("数据导入完成")

        except Exception as e:
            self.log_message(f"导入数据失败: {e}")
            messagebox.showerror("错误", f"导入数据失败: {e}")

    def export_csv_data(self):
        """导出CSV数据"""
        file_path = filedialog.asksaveasfilename(
            title="保存CSV数据文件",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            self.update_status("正在导出数据...")
            self.log_message(f"开始导出数据: {file_path}")

            # 获取当前显示的数据
            data_to_export = []
            for item in self.data_tree.get_children():
                values = self.data_tree.item(item)['values']
                data_to_export.append(values)

            # 写入CSV文件
            import csv
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                # 写入表头
                writer.writerow(["期号", "开奖日期", "特码", "生肖", "五行", "波色", "大小", "单双", "尾数"])
                # 写入数据
                writer.writerows(data_to_export)

            messagebox.showinfo("成功", f"数据已导出到: {file_path}")
            self.log_message(f"数据导出完成: {len(data_to_export)} 条记录")
            self.update_status("数据导出完成")

        except Exception as e:
            self.log_message(f"导出数据失败: {e}")
            messagebox.showerror("错误", f"导出数据失败: {e}")

    def search_data(self, event=None):
        """搜索数据"""
        search_term = self.search_var.get().strip()

        if not search_term:
            self.refresh_history_data()
            return

        try:
            # 清空现有显示
            self.data_tree.delete(*self.data_tree.get_children())

            # 加载所有历史数据
            history_data = self.prediction_engine.load_historical_data()

            # 搜索匹配的记录
            matched_records = []
            for record in history_data:
                period = str(record.get('period', ''))
                if search_term.lower() in period.lower():
                    matched_records.append(record)

            # 显示搜索结果
            for record in matched_records[:100]:  # 限制显示100条
                period = record.get('period', '')
                date = record.get('draw_date', '')
                special_code = record.get('special_code', '')
                zodiac = record.get('zodiac', '')
                five_element = record.get('five_element', '')
                wave_color = record.get('wave_color', '')

                if special_code:
                    big_small = "大" if special_code >= 25 else "小"
                    odd_even = "单" if special_code % 2 == 1 else "双"
                    tail = special_code % 10
                else:
                    big_small = odd_even = tail = ""

                self.data_tree.insert("", tk.END, values=(
                    period, date, special_code, zodiac, five_element,
                    wave_color, big_small, odd_even, tail
                ))

            self.log_message(f"搜索 '{search_term}' 找到 {len(matched_records)} 条记录")

        except Exception as e:
            self.log_message(f"搜索失败: {e}")

    def clear_search(self):
        """清空搜索"""
        self.search_var.set("")
        self.refresh_history_data()

    def sort_data(self, column):
        """排序数据"""
        # 获取当前数据
        data = [(self.data_tree.set(item, column), item) for item in self.data_tree.get_children()]

        # 排序
        data.sort(reverse=False)

        # 重新排列
        for index, (val, item) in enumerate(data):
            self.data_tree.move(item, '', index)

        self.log_message(f"按 {column} 列排序完成")

    def on_data_double_click(self, event):
        """双击数据行事件"""
        selection = self.data_tree.selection()
        if not selection:
            return

        item = selection[0]
        values = self.data_tree.item(item)['values']

        if not values:
            return

        # 显示详细信息窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title("详细信息 - 期号 " + str(values[0]))
        detail_window.geometry("400x300")
        detail_window.resizable(False, False)

        # 详细信息内容
        detail_text = tk.Text(detail_window, font=("Consolas", 10))
        detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        detail_info = f"""
期号详细信息
{'='*30}

期号: {values[0]}
开奖日期: {values[1]}
特码: {values[2]}
生肖: {values[3]}
五行: {values[4]}
波色: {values[5]}
大小: {values[6]}
单双: {values[7]}
尾数: {values[8]}

数据分析:
• 号码范围: 1-49
• 大小分界: 25
• 波色分类: 红/蓝/绿
• 五行属性: 金/木/水/火/土
• 生肖周期: 12年一轮回
        """

        detail_text.insert(tk.END, detail_info)
        detail_text.config(state=tk.DISABLED)

    def show_data_statistics(self):
        """显示数据统计"""
        try:
            history_data = self.prediction_engine.load_historical_data()

            if not history_data:
                messagebox.showwarning("警告", "没有历史数据")
                return

            # 创建统计窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("数据统计分析")
            stats_window.geometry("600x500")

            stats_text = scrolledtext.ScrolledText(stats_window, font=("Consolas", 10))
            stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 计算统计信息
            total_records = len(history_data)
            special_codes = [r.get('special_code') for r in history_data if r.get('special_code')]

            if special_codes:
                avg_code = sum(special_codes) / len(special_codes)
                max_code = max(special_codes)
                min_code = min(special_codes)

                # 大小统计
                big_count = sum(1 for code in special_codes if code >= 25)
                small_count = len(special_codes) - big_count

                # 单双统计
                odd_count = sum(1 for code in special_codes if code % 2 == 1)
                even_count = len(special_codes) - odd_count

                # 尾数统计
                tail_stats = {}
                for code in special_codes:
                    tail = code % 10
                    tail_stats[tail] = tail_stats.get(tail, 0) + 1

            # 生成统计报告
            stats_report = f"""
历史数据统计分析报告
{'='*50}

基本信息:
  总记录数: {total_records}
  有效特码数: {len(special_codes)}
  数据完整率: {len(special_codes)/total_records*100:.1f}%

特码统计:
  平均值: {avg_code:.2f}
  最大值: {max_code}
  最小值: {min_code}

大小分布:
  大号 (25-49): {big_count} 次 ({big_count/len(special_codes)*100:.1f}%)
  小号 (1-24):  {small_count} 次 ({small_count/len(special_codes)*100:.1f}%)

单双分布:
  单号: {odd_count} 次 ({odd_count/len(special_codes)*100:.1f}%)
  双号: {even_count} 次 ({even_count/len(special_codes)*100:.1f}%)

尾数分布:
"""

            for tail in range(10):
                count = tail_stats.get(tail, 0)
                percentage = count / len(special_codes) * 100 if special_codes else 0
                stats_report += f"  尾数 {tail}: {count} 次 ({percentage:.1f}%)\n"

            stats_report += f"""

数据时间范围:
  最新期号: {history_data[0].get('period', '') if history_data else ''}
  最早期号: {history_data[-1].get('period', '') if history_data else ''}

统计生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            stats_text.insert(tk.END, stats_report)
            stats_text.config(state=tk.DISABLED)

            self.log_message("数据统计分析完成")

        except Exception as e:
            self.log_message(f"数据统计失败: {e}")
            messagebox.showerror("错误", f"数据统计失败: {e}")

    def manual_input_data(self):
        """手动输入数据"""
        input_window = tk.Toplevel(self.root)
        input_window.title("手动输入开奖数据")
        input_window.geometry("500x400")
        input_window.resizable(False, False)

        # 使窗口居中
        input_window.transient(self.root)
        input_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(input_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame,
            text="📝 手动输入开奖数据",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # 输入表单
        form_frame = ttk.LabelFrame(main_frame, text="开奖信息", padding=15)
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # 期号输入
        period_frame = ttk.Frame(form_frame)
        period_frame.pack(fill=tk.X, pady=5)
        ttk.Label(period_frame, text="期号:", width=10).pack(side=tk.LEFT)
        period_var = tk.StringVar()
        period_entry = ttk.Entry(period_frame, textvariable=period_var, font=("Arial", 11))
        period_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

        # 添加期号格式提示
        period_hint = ttk.Label(form_frame, text="格式: 2025001 (7位数字)", foreground="gray")
        period_hint.pack(anchor=tk.W, padx=(80, 0))

        # 日期输入
        date_frame = ttk.Frame(form_frame)
        date_frame.pack(fill=tk.X, pady=5)
        ttk.Label(date_frame, text="开奖日期:", width=10).pack(side=tk.LEFT)
        date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        date_entry = ttk.Entry(date_frame, textvariable=date_var, font=("Arial", 11))
        date_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

        # 添加日期格式提示
        date_hint = ttk.Label(form_frame, text="格式: 2025-07-18", foreground="gray")
        date_hint.pack(anchor=tk.W, padx=(80, 0))

        # 特码输入
        code_frame = ttk.Frame(form_frame)
        code_frame.pack(fill=tk.X, pady=5)
        ttk.Label(code_frame, text="特码:", width=10).pack(side=tk.LEFT)
        code_var = tk.StringVar()
        code_entry = ttk.Entry(code_frame, textvariable=code_var, font=("Arial", 11))
        code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

        # 添加特码范围提示
        code_hint = ttk.Label(form_frame, text="范围: 1-49", foreground="gray")
        code_hint.pack(anchor=tk.W, padx=(80, 0))

        # 属性预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="属性预览", padding=15)
        preview_frame.pack(fill=tk.X, pady=(0, 20))

        # 属性显示标签
        attr_labels = {}
        attr_items = [
            ("生肖", "zodiac"),
            ("五行", "five_element"),
            ("波色", "wave_color"),
            ("大小", "big_small"),
            ("单双", "odd_even"),
            ("尾数", "tail_number")
        ]

        for i, (label, key) in enumerate(attr_items):
            row = i // 3
            col = i % 3

            ttk.Label(preview_frame, text=f"{label}:").grid(
                row=row*2, column=col, sticky=tk.W, padx=10, pady=2
            )
            attr_labels[key] = ttk.Label(
                preview_frame,
                text="--",
                font=("Arial", 10, "bold"),
                foreground="blue"
            )
            attr_labels[key].grid(
                row=row*2+1, column=col, sticky=tk.W, padx=10, pady=(0, 10)
            )

        def update_preview(*args):
            """更新属性预览"""
            try:
                code_str = code_var.get().strip()
                if code_str and code_str.isdigit():
                    code = int(code_str)
                    if 1 <= code <= 49:
                        attrs = self.attribute_mapper.map_all_attributes(code)
                        for key, label in attr_labels.items():
                            label.config(text=str(attrs[key]), foreground="green")
                    else:
                        for label in attr_labels.values():
                            label.config(text="无效", foreground="red")
                else:
                    for label in attr_labels.values():
                        label.config(text="--", foreground="gray")
            except:
                for label in attr_labels.values():
                    label.config(text="错误", foreground="red")

        # 绑定特码输入事件
        code_var.trace('w', update_preview)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def save_data():
            """保存数据"""
            try:
                period = period_var.get().strip()
                date = date_var.get().strip()
                code_str = code_var.get().strip()

                if not period or not date or not code_str:
                    messagebox.showerror("错误", "请填写完整信息")
                    return

                if not code_str.isdigit():
                    messagebox.showerror("错误", "特码必须是数字")
                    return

                code = int(code_str)

                # 使用数据管理器添加/更新记录
                success, message = self.data_manager.add_or_update_record(period, date, code)

                if success:
                    # 保存到文件
                    if self.data_manager.save_data():
                        messagebox.showinfo("成功", message + "\n数据已保存到文件")
                        self.log_message(message)

                        # 刷新界面数据
                        self.refresh_history_data()

                        # 关闭窗口
                        input_window.destroy()
                    else:
                        messagebox.showerror("错误", "数据保存失败")
                else:
                    messagebox.showerror("错误", message)

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

        def check_existing():
            """检查现有数据"""
            period = period_var.get().strip()
            if not period:
                return

            existing = self.data_manager.get_record(period)
            if existing:
                info = f"""
期号 {period} 已存在数据:
开奖日期: {existing['draw_date']}
特码: {existing['special_code']}
生肖: {existing['zodiac']}
五行: {existing['five_element']}
波色: {existing['wave_color']}

如果继续保存，将更新现有数据。
                """
                messagebox.showinfo("数据已存在", info)

        # 按钮
        ttk.Button(
            button_frame,
            text="🔍 检查现有",
            command=check_existing
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            button_frame,
            text="💾 保存数据",
            command=save_data
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            button_frame,
            text="❌ 取消",
            command=input_window.destroy
        ).pack(side=tk.RIGHT)

        # 设置焦点
        period_entry.focus_set()

        # 绑定回车键
        def on_enter(event):
            if event.widget == period_entry:
                date_entry.focus_set()
            elif event.widget == date_entry:
                code_entry.focus_set()
            elif event.widget == code_entry:
                save_data()

        period_entry.bind('<Return>', on_enter)
        date_entry.bind('<Return>', on_enter)
        code_entry.bind('<Return>', on_enter)

    def export_backtest_txt(self):
        """导出回测结果为TXT"""
        if not self.last_backtest_result:
            messagebox.showwarning("警告", "没有回测结果可导出")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存回测结果",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if file_path:
                filename = file_path[:-4] if file_path.endswith('.txt') else file_path
                exported_file = self.result_exporter.export_backtest_result(
                    self.last_backtest_result, 'txt', filename
                )
                messagebox.showinfo("成功", f"回测结果已导出到: {exported_file}")
                self.log_message(f"回测结果已导出: {exported_file}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def export_backtest_csv(self):
        """导出回测结果为CSV"""
        if not self.last_backtest_result:
            messagebox.showwarning("警告", "没有回测结果可导出")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存回测结果",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
            if file_path:
                filename = file_path[:-4] if file_path.endswith('.csv') else file_path
                exported_file = self.result_exporter.export_backtest_result(
                    self.last_backtest_result, 'csv', filename
                )
                messagebox.showinfo("成功", f"回测结果已导出到: {exported_file}")
                self.log_message(f"回测结果已导出: {exported_file}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def export_backtest_json(self):
        """导出回测结果为JSON"""
        if not self.last_backtest_result:
            messagebox.showwarning("警告", "没有回测结果可导出")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存回测结果",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            if file_path:
                filename = file_path[:-5] if file_path.endswith('.json') else file_path
                exported_file = self.result_exporter.export_backtest_result(
                    self.last_backtest_result, 'json', filename
                )
                messagebox.showinfo("成功", f"回测结果已导出到: {exported_file}")
                self.log_message(f"回测结果已导出: {exported_file}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = LotteryPredictionGUI(root)
    root.mainloop()
