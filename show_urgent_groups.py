#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示当前最紧迫的小组
详细解释紧迫度的含义
"""

from zodiac_group_analyzer import ZodiacGroupAnalyzer
from extreme_reback_engine import ExtremeRebackEngine

def show_urgent_groups():
    """展示最紧迫小组"""
    print("🔥 当前最紧迫的4肖组合详细分析")
    print("="*70)
    
    # 创建分析器
    analyzer = ZodiacGroupAnalyzer()
    reback_engine = ExtremeRebackEngine()
    
    print("📊 什么是'最紧迫小组'？")
    print("-" * 50)
    print("最紧迫小组是指那些当前遗漏期数接近或达到历史极限的4肖组合。")
    print("这些小组具有以下特征：")
    print("1. 🔥 Z-Score ≥ 1.5 (统计学异常)")
    print("2. ⚡ 当前遗漏 ≥ 70%历史极限 (接近极值)")
    print("3. 💧 历史有回补记录 (补偿性回补)")
    print("4. 📈 回补概率较高 (统计学支撑)")
    print()
    
    # 获取最紧迫的小组
    print("🔍 正在分析最紧迫小组...")
    urgent_groups = analyzer.get_top_groups_by_urgency(15)
    
    if not urgent_groups:
        print("📊 当前无特别紧迫的小组")
        print("说明：所有4肖组合都在正常的遗漏范围内")
        return
    
    print(f"✅ 找到 {len(urgent_groups)} 个紧迫小组")
    print()
    
    # 详细展示前10个最紧迫的小组
    print("🏆 最紧迫小组排行榜 (按Z-Score排序)")
    print("="*70)
    
    for i, group_data in enumerate(urgent_groups[:10], 1):
        members_str = ", ".join(group_data['members'])
        current_miss = group_data['current_miss']
        max_miss = group_data['max_miss_history']
        z_score = group_data['z_score']
        hit_count = group_data['hit_count']
        
        # 计算紧迫度指标
        critical_ratio = current_miss / max_miss if max_miss > 0 else 0
        distance_to_max = max_miss - current_miss
        
        # 紧迫度等级
        if critical_ratio >= 1.0:
            urgency_level = "🔥 超极限"
            urgency_color = "红色"
        elif critical_ratio >= 0.9:
            urgency_level = "⚡ 极限警戒"
            urgency_color = "橙色"
        elif critical_ratio >= 0.7:
            urgency_level = "💧 临界回补"
            urgency_color = "黄色"
        else:
            urgency_level = "❄️ 正常范围"
            urgency_color = "绿色"
        
        print(f"#{i:2d}. {members_str}")
        print(f"     📊 当前遗漏: {current_miss} 期")
        print(f"     📈 历史极限: {max_miss} 期")
        print(f"     📉 距离极值: {distance_to_max} 期")
        print(f"     🎯 临界比例: {critical_ratio:.1%}")
        print(f"     📊 Z-Score: {z_score:.3f}")
        print(f"     🏆 总命中: {hit_count} 次")
        print(f"     🔥 紧迫等级: {urgency_level}")
        print()
    
    # 分析紧迫度分布
    print("📈 紧迫度分布分析")
    print("="*70)
    
    urgency_distribution = {
        "🔥 超极限": 0,
        "⚡ 极限警戒": 0,
        "💧 临界回补": 0,
        "❄️ 正常范围": 0
    }
    
    for group_data in urgent_groups:
        current_miss = group_data['current_miss']
        max_miss = group_data['max_miss_history']
        critical_ratio = current_miss / max_miss if max_miss > 0 else 0
        
        if critical_ratio >= 1.0:
            urgency_distribution["🔥 超极限"] += 1
        elif critical_ratio >= 0.9:
            urgency_distribution["⚡ 极限警戒"] += 1
        elif critical_ratio >= 0.7:
            urgency_distribution["💧 临界回补"] += 1
        else:
            urgency_distribution["❄️ 正常范围"] += 1
    
    for level, count in urgency_distribution.items():
        if count > 0:
            percentage = count / len(urgent_groups) * 100
            print(f"{level}: {count} 个小组 ({percentage:.1f}%)")
    
    # 投注建议
    print(f"\n💡 投注建议")
    print("="*70)
    
    super_urgent = [g for g in urgent_groups if g['current_miss'] >= g['max_miss_history']]
    high_urgent = [g for g in urgent_groups if 0.9 <= g['current_miss']/g['max_miss_history'] < 1.0]
    medium_urgent = [g for g in urgent_groups if 0.7 <= g['current_miss']/g['max_miss_history'] < 0.9]
    
    if super_urgent:
        print(f"🔥 超极限小组 ({len(super_urgent)}个):")
        print("   - 已超过历史最大遗漏，回补概率极高")
        print("   - 建议重点关注，短期内可能出现")
        print("   - 风险提示：虽然概率高，但不保证必然出现")
        
        for group in super_urgent[:3]:
            members_str = ", ".join(group['members'])
            print(f"     * {members_str} (遗漏{group['current_miss']}期)")
    
    if high_urgent:
        print(f"\n⚡ 极限警戒小组 ({len(high_urgent)}个):")
        print("   - 接近历史极限，需要密切关注")
        print("   - 建议列入观察名单")
        
        for group in high_urgent[:3]:
            members_str = ", ".join(group['members'])
            distance = group['max_miss_history'] - group['current_miss']
            print(f"     * {members_str} (距极限{distance}期)")
    
    if medium_urgent:
        print(f"\n💧 临界回补小组 ({len(medium_urgent)}个):")
        print("   - 进入临界回补期，开始关注")
        print("   - 可作为备选观察对象")
    
    # 历史回补分析
    print(f"\n📚 历史回补模式分析")
    print("="*70)
    
    print("基于历史数据分析，极限小组的回补模式：")
    print("1. 🚀 快速反弹型 (30%): 触极限后1-3期内快速回补")
    print("2. 💥 爆发型 (25%): 触极限后短期内多次命中")
    print("3. 🌊 缓慢回补型 (35%): 触极限后4-10期内逐步回补")
    print("4. 🚫 伪极限型 (10%): 触极限后仍长期不回补")
    print()
    print("💡 投注策略建议：")
    print("- 超极限小组：重点追踪3-5期")
    print("- 极限警戒小组：观察2-3期")
    print("- 临界回补小组：列入备选名单")
    print("- 结合其他策略综合判断")
    
    # 风险提示
    print(f"\n⚠️ 重要风险提示")
    print("="*70)
    print("1. 统计学基础：基于历史数据的统计分析，具有一定科学性")
    print("2. 概率性质：回补是概率事件，不是确定事件")
    print("3. 市场风险：任何预测都存在失败的可能性")
    print("4. 理性投注：请根据个人风险承受能力理性投注")
    print("5. 综合判断：建议结合多种策略综合分析")

def show_reback_candidates():
    """展示回补候选"""
    print(f"\n🔥 极限回补预测候选")
    print("="*70)
    
    try:
        reback_engine = ExtremeRebackEngine()
        candidates = reback_engine.predict_reback_candidates(10)
        
        if candidates:
            print(f"找到 {len(candidates)} 个回补候选：")
            print()
            
            for i, candidate in enumerate(candidates, 1):
                members_str = ", ".join(candidate.members)
                stars = "★" * candidate.recommendation_level + "☆" * (5 - candidate.recommendation_level)
                
                print(f"#{i}. {members_str}")
                print(f"    推荐等级: {stars} ({candidate.recommendation_level}/5)")
                print(f"    回补评分: {candidate.reback_score:.3f}")
                print(f"    预测模式: {candidate.predicted_pattern}")
                print(f"    风险等级: {candidate.risk_level}")
                print(f"    跟踪期数: {candidate.tracking_periods} 期")
                print()
        else:
            print("当前无回补候选")
    
    except Exception as e:
        print(f"获取回补候选失败: {e}")

if __name__ == "__main__":
    # 展示最紧迫小组
    show_urgent_groups()
    
    # 展示回补候选
    show_reback_candidates()
    
    print(f"\n🎯 分析完成!")
    print("现在您了解了'最紧迫小组'的含义和重要性")
