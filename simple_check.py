"""
简化系统检查工具 - 只使用Python标准库
"""

import os
import sqlite3
import json
import sys
from datetime import datetime


def check_files():
    """检查核心文件"""
    print("📁 检查核心文件...")
    
    core_files = [
        'main.py',
        'combo_generator.py', 
        'extreme_stat_tracker.py',
        'strategy_scorer.py',
        'lottery_data.db',
        'lottery_data_20250717.csv'
    ]
    
    results = {}
    for file in core_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            results[file] = {'exists': True, 'size': size}
            status = "✅" if size > 0 else "⚠️"
            print(f"  {status} {file}: {size} bytes")
        else:
            results[file] = {'exists': False, 'size': 0}
            print(f"  ❌ {file}: 文件不存在")
    
    return results


def check_database():
    """检查数据库"""
    print("\n🗄️ 检查数据库...")
    
    try:
        conn = sqlite3.connect('lottery_data.db')
        cursor = conn.cursor()
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"  ✅ 数据库连接正常")
        print(f"  📊 发现 {len(tables)} 个表:")
        
        table_data = {}
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_data[table] = count
                print(f"    - {table}: {count} 条记录")
            except Exception as e:
                table_data[table] = f"ERROR: {e}"
                print(f"    - {table}: 查询失败")
        
        # 检查关键数据
        if 'lottery_records' in tables:
            cursor.execute("SELECT COUNT(*) FROM lottery_records")
            record_count = cursor.fetchone()[0]
            
            if record_count > 0:
                cursor.execute("SELECT period_number, draw_date FROM lottery_records ORDER BY period_number DESC LIMIT 1")
                latest = cursor.fetchone()
                print(f"  📅 最新记录: {latest[0]} ({latest[1]})")
        
        conn.close()
        return {'status': 'OK', 'tables': tables, 'table_data': table_data}
        
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return {'status': 'ERROR', 'error': str(e)}


def check_modules():
    """检查模块导入"""
    print("\n📦 检查模块导入...")
    
    modules_to_test = [
        'combo_generator',
        'extreme_stat_tracker',
        'strategy_scorer'
    ]
    
    results = {}
    for module_name in modules_to_test:
        try:
            # 尝试导入模块
            module = __import__(module_name)
            results[module_name] = 'OK'
            print(f"  ✅ {module_name}: 导入成功")
            
            # 简单测试
            if module_name == 'combo_generator':
                generator = module.ComboGenerator()
                combos = generator.generate_shengxiao_4()
                print(f"    - 生成 {len(combos)} 个生肖4组合")
                
        except Exception as e:
            results[module_name] = f'ERROR: {e}'
            print(f"  ❌ {module_name}: 导入失败 - {e}")
    
    return results


def check_python_version():
    """检查Python版本"""
    print("🐍 Python环境信息:")
    print(f"  版本: {sys.version}")
    print(f"  路径: {sys.executable}")
    
    # 检查关键标准库
    standard_libs = ['sqlite3', 'json', 'os', 'datetime', 'csv']
    print("  标准库检查:")
    for lib in standard_libs:
        try:
            __import__(lib)
            print(f"    ✅ {lib}")
        except ImportError:
            print(f"    ❌ {lib}")


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        # 测试组合生成
        from combo_generator import ComboGenerator
        generator = ComboGenerator()
        
        shengxiao_combos = generator.generate_shengxiao_4()
        wuxing_combos = generator.generate_wuxing_2()
        
        print(f"  ✅ 组合生成: 生肖4组合 {len(shengxiao_combos)} 个, 五行2组合 {len(wuxing_combos)} 个")
        
        # 测试数据库读取
        conn = sqlite3.connect('lottery_data.db')
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        count = cursor.fetchone()[0]
        conn.close()
        
        print(f"  ✅ 数据库读取: {count} 条历史记录")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 简化系统状态检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查Python环境
    check_python_version()
    
    # 检查文件
    file_results = check_files()
    
    # 检查数据库
    db_results = check_database()
    
    # 检查模块
    module_results = check_modules()
    
    # 测试基本功能
    basic_test = test_basic_functionality()
    
    # 生成总结
    print("\n📋 检查总结")
    print("=" * 30)
    
    file_ok = sum(1 for f in file_results.values() if f['exists'] and f['size'] > 0)
    file_total = len(file_results)
    
    module_ok = sum(1 for m in module_results.values() if m == 'OK')
    module_total = len(module_results)
    
    db_ok = db_results.get('status') == 'OK'
    
    print(f"📁 文件状态: {file_ok}/{file_total} 正常")
    print(f"🗄️ 数据库: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"📦 模块导入: {module_ok}/{module_total} 成功")
    print(f"🧪 基本功能: {'✅ 正常' if basic_test else '❌ 异常'}")
    
    # 总体评估
    if file_ok == file_total and db_ok and module_ok == module_total and basic_test:
        print(f"\n🎉 总体状态: 优秀 - 系统完全正常")
    elif file_ok >= file_total * 0.8 and (db_ok or basic_test):
        print(f"\n✅ 总体状态: 良好 - 主要功能正常")
    else:
        print(f"\n⚠️ 总体状态: 需要修复 - 存在问题")
    
    # 保存结果
    results = {
        'timestamp': datetime.now().isoformat(),
        'files': file_results,
        'database': db_results,
        'modules': module_results,
        'basic_test': basic_test
    }
    
    with open('simple_check_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: simple_check_results.json")


if __name__ == "__main__":
    main()
