export function get_numbers_by_tail(numbers: number[], tail: number): number[] {
    return numbers.filter(number => number % 10 === tail);
}

export function apply_tail_group_filter(numbers: number[], tails: number[]): number[] {
    let matchedNumbers: number[] = [];
    tails.forEach(tail => {
        matchedNumbers = matchedNumbers.concat(get_numbers_by_tail(numbers, tail));
    });
    return [...new Set(matchedNumbers)];
}