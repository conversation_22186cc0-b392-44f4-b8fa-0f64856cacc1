# 三层筛选预测策略配置文件
# 版本: 1.0
# 描述: 采用大筛→中筛→小筛三层机制，融合多维属性分析

strategy_name: "多维组合极限筛选策略"
version: "1.0"
description: "三层筛选机制：大筛(多维策略组) → 中筛(48码四组分布) → 小筛(交叉特征筛) → 精选12-16个特码"
author: "六合彩真实预测引擎"
created_date: "2025-01-23"

# ==================== 基础配置 ====================
base_pool:
  type: "filtered_range"
  range: [1, 49]  # 六合彩号码范围
  exclude: []     # 排除的号码
  
# ==================== 第一层：大筛子配置 ====================
layer1_big_filter:
  name: "多维策略组筛选"
  description: "生肖+号码+波色+五行组合策略"
  
  # 多维生肖组合
  zodiac_combinations:
    enabled: true
    group_size: 6  # 每组6个生肖
    strategies:
      - name: "日夜平衡组合"
        day_zodiacs: 3    # 日肖数量
        night_zodiacs: 3  # 夜肖数量
        weight: 1.2
      
      - name: "吉凶平衡组合"
        lucky_zodiacs: 4   # 吉肖数量
        unlucky_zodiacs: 2 # 凶肖数量
        weight: 1.0
      
      - name: "家野平衡组合"
        domestic_zodiacs: 3  # 家肖数量
        wild_zodiacs: 3      # 野肖数量
        weight: 0.9

  # 多维号码组合
  number_combinations:
    enabled: true
    target_count: 24  # 目标筛选出24-25个号码
    strategies:
      - name: "大小平衡"
        big_numbers: 12   # 大号(25-49)数量
        small_numbers: 12 # 小号(1-24)数量
        weight: 1.1
      
      - name: "单双平衡"
        odd_numbers: 12   # 单数数量
        even_numbers: 12  # 双数数量
        weight: 1.0

  # 波色组合
  color_combinations:
    enabled: true
    strategies:
      - combo: ["红", "绿"]
        weight: 1.2
        min_count: 2  # 每种波色最少号码数
      
      - combo: ["红", "蓝"]
        weight: 1.1
        min_count: 2
      
      - combo: ["蓝", "绿"]
        weight: 1.0
        min_count: 2

  # 五行组合
  wuxing_combinations:
    enabled: true
    strategies:
      - combo: ["金", "木"]
        weight: 1.3
        balance_ratio: 0.6  # 金木比例
      
      - combo: ["水", "火"]
        weight: 1.2
        balance_ratio: 0.5
      
      - combo: ["土", "金"]
        weight: 1.1
        balance_ratio: 0.7
      
      - combo: ["木", "火"]
        weight: 1.0
        balance_ratio: 0.6

# ==================== 第二层：中筛子配置 ====================
layer2_medium_filter:
  name: "48码四组分布筛选"
  description: "将48个号码分成4组，每组12个，保持属性均衡"
  
  group_strategy:
    method: "balanced_split"  # 平衡分割方法
    group_count: 4
    numbers_per_group: 12
    
    # 每组必须满足的平衡条件
    balance_requirements:
      big_small_ratio: [0.4, 0.6]    # 大小号比例范围
      odd_even_ratio: [0.4, 0.6]     # 单双比例范围
      zodiac_diversity: 8             # 至少包含8个不同生肖
      color_diversity: 3              # 至少包含3种波色
      wuxing_diversity: 4             # 至少包含4种五行

  # 组选择策略
  group_selection:
    method: "multi_criteria"  # 多标准选择
    criteria:
      - name: "历史命中率"
        weight: 0.3
        source: "historical_hit_rate"
      
      - name: "极限遗漏度"
        weight: 0.4
        source: "extreme_miss_tracker"
      
      - name: "平衡控制"
        weight: 0.3
        source: "balance_controller"
    
    select_groups: [1, 2]  # 选择1-2个最优组

# ==================== 第三层：小筛子配置 ====================
layer3_fine_filter:
  name: "交叉特征精筛"
  description: "多维属性交叉+极限追踪+平衡反向筛选"
  
  # 交叉特征筛选
  cross_features:
    # 波色配合策略
    color_strategy:
      enabled: true
      method: "least_recent"  # 最近3期出现最少的波色优先
      recent_periods: 3
      weight: 1.2
    
    # 单双比策略
    odd_even_strategy:
      enabled: true
      preferred_ratio: "2_odd_2_even"  # 倾向于2单+2双的4号组合
      tolerance: 1  # 允许±1的偏差
      weight: 1.1
    
    # 五行匹配策略
    wuxing_strategy:
      enabled: true
      method: "complement_balance"  # 主组+互补组
      main_wuxing_count: 2    # 主五行数量
      complement_count: 1     # 互补五行数量
      weight: 1.3
    
    # 生肖属性策略
    zodiac_strategy:
      enabled: true
      combination_type: "lucky2_unlucky1_special1"  # 吉肖2+凶肖1+独肖1
      weight: 1.0

  # 极限追踪策略
  extreme_tracking:
    enabled: true
    strategies:
      - name: "接近极限优先"
        threshold_ratio: 0.8  # 接近极限80%时优先
        weight: 1.5
      
      - name: "极限回补预警"
        max_miss_multiplier: 1.2  # 超过平均遗漏1.2倍时预警
        weight: 1.3

  # 平衡反推策略
  balance_reverse:
    enabled: true
    strategies:
      - name: "最近命中回避"
        avoid_recent_periods: 5  # 最近5期中过的组合不参与
        weight: 0.7
      
      - name: "回补策略倾向"
        favor_comeback: true     # 偏向回补策略
        comeback_threshold: 10   # 遗漏10期以上优先
        weight: 1.4

# ==================== 输出控制 ====================
output_control:
  target_range: [12, 16]  # 最终输出12-16个号码
  
  # 排序策略
  sort_criteria:
    - field: "extreme_gap"      # 极限遗漏差距
      weight: 0.4
      order: "desc"
    
    - field: "recent_hotness"   # 最近热度
      weight: 0.3
      order: "desc"
    
    - field: "balance_score"    # 平衡评分
      weight: 0.2
      order: "desc"
    
    - field: "cross_feature_score"  # 交叉特征评分
      weight: 0.1
      order: "desc"

  # 质量控制
  quality_control:
    min_confidence: 0.6      # 最低置信度
    max_risk_level: 0.3      # 最大风险等级
    diversity_check: true    # 多样性检查
    
# ==================== 回测配置 ====================
backtest_config:
  enabled: true
  test_periods: 100        # 回测期数
  
  # 评估指标
  metrics:
    - "hit_rate"           # 命中率
    - "avg_miss_interval"  # 平均遗漏间隔
    - "max_miss_streak"    # 最大连续遗漏
    - "roi"                # 投资回报率
    - "sharpe_ratio"       # 夏普比率
    - "stability_score"    # 稳定性评分

  # 报告输出
  report_output:
    formats: ["html", "txt", "json"]
    include_charts: true
    detail_level: "full"

# ==================== 高级配置 ====================
advanced_settings:
  # 动态调整
  dynamic_adjustment:
    enabled: true
    adjustment_period: 20    # 每20期调整一次权重
    learning_rate: 0.1       # 学习率
  
  # 风险控制
  risk_management:
    max_consecutive_miss: 15  # 最大连续遗漏
    stop_loss_threshold: 0.2  # 止损阈值
    
  # 性能优化
  performance:
    cache_enabled: true
    parallel_processing: true
    max_threads: 4
