#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证特定小组：牛蛇羊猴
分析2025197期开34猴的命中情况
"""

from advanced_zodiac_engine import AdvancedZodiacEngine
from prediction_engine import PredictionEngine
from data_attributes import DataAttributeMapper

def verify_group_analysis():
    """验证牛蛇羊猴小组的分析"""
    print("🔍 验证特定小组分析：牛蛇羊猴")
    print("="*60)
    
    # 初始化系统
    print("📊 初始化系统...")
    engine = AdvancedZodiacEngine()
    pred_engine = PredictionEngine()
    mapper = DataAttributeMapper()
    
    # 查找包含牛蛇羊猴的小组
    target_members = ["牛", "蛇", "羊", "猴"]
    target_group = None
    
    print(f"🔍 查找包含 {target_members} 的小组...")
    
    for group_id, group in engine.all_groups.items():
        if set(group.members) == set(target_members):
            target_group = group
            print(f"✅ 找到目标小组: {group_id}")
            break
    
    if not target_group:
        print("❌ 未找到完全匹配的小组")
        # 查找包含这些生肖的小组
        print("🔍 查找包含部分生肖的小组...")
        for group_id, group in list(engine.all_groups.items())[:10]:
            overlap = set(group.members) & set(target_members)
            if len(overlap) >= 3:
                print(f"   部分匹配: {group_id} - {group.members} (重叠: {list(overlap)})")
        return
    
    # 验证34号对应的生肖
    print(f"\n🔢 验证34号的生肖映射:")
    attrs_34 = mapper.map_all_attributes(34)
    print(f"   34号 → 生肖: {attrs_34['zodiac']}")
    print(f"   34号 → 五行: {attrs_34['five_element']}")
    print(f"   34号 → 波色: {attrs_34['wave_color']}")
    
    # 加载历史数据进行校准
    print(f"\n📈 加载历史数据进行校准...")
    history_data = pred_engine.load_historical_data()
    
    # 找到2025197期的数据
    period_2025197 = None
    for record in history_data:
        if record.get('period') == '2025197':
            period_2025197 = record
            break
    
    if period_2025197:
        print(f"✅ 找到2025197期数据:")
        print(f"   期号: {period_2025197.get('period')}")
        print(f"   特码: {period_2025197.get('special_code')}")
        print(f"   生肖: {period_2025197.get('zodiac')}")
        print(f"   日期: {period_2025197.get('draw_date')}")
    else:
        print("❌ 未找到2025197期数据")
        return
    
    # 模拟处理到2025197期之前的状态
    print(f"\n🔄 模拟处理到2025197期之前...")
    
    # 重置小组状态
    target_group.current_miss = 0
    target_group.miss_history = []
    target_group.max_miss = 0
    target_group.internal_misses = {member: 0 for member in target_group.members}
    
    # 处理2025197期之前的历史数据
    processed_periods = 0
    for record in history_data:
        period = record.get('period', '')
        if period >= '2025197':  # 只处理到2025197期之前
            continue
            
        zodiac = record.get('zodiac', '')
        if not zodiac:
            continue
        
        processed_periods += 1
        
        # 更新小组状态
        if zodiac in target_group.members:
            # 命中
            if target_group.current_miss > 0:
                target_group.miss_history.append(target_group.current_miss)
                target_group.max_miss = max(target_group.max_miss, target_group.current_miss)
            
            target_group.current_miss = 0
            target_group.internal_misses[zodiac] = 0
        else:
            # 未命中
            target_group.current_miss += 1
        
        # 更新组内其他生肖遗漏
        for member in target_group.members:
            if member != zodiac:
                target_group.internal_misses[member] += 1
    
    print(f"   处理了 {processed_periods} 期历史数据")
    
    # 显示2025197期之前的状态
    print(f"\n📊 2025197期开奖前的小组状态:")
    print(f"   小组: {target_group.members}")
    print(f"   当前遗漏: {target_group.current_miss} 期")
    print(f"   最大遗漏: {target_group.max_miss} 期")
    print(f"   历史命中: {len(target_group.miss_history)} 次")
    print(f"   历史遗漏: {target_group.miss_history[-10:] if target_group.miss_history else []}")
    
    # 计算统计指标
    if target_group.miss_history:
        import numpy as np
        miss_mean = np.mean(target_group.miss_history)
        miss_std = np.std(target_group.miss_history)
        
        if miss_std > 0:
            z_score = (target_group.current_miss - miss_mean) / miss_std
        else:
            z_score = 0
        
        print(f"   平均遗漏: {miss_mean:.2f} 期")
        print(f"   遗漏标准差: {miss_std:.2f} 期")
        print(f"   当前Z-Score: {z_score:.3f}")
        
        # 判断是否为候选
        if z_score >= 2.0:
            print(f"   🔥 状态: 候选小组 (Z-Score >= 2.0)")
        elif z_score >= 1.5:
            print(f"   ⚡ 状态: 关注小组 (Z-Score >= 1.5)")
        else:
            print(f"   💧 状态: 正常小组")
    
    # 显示组内能量分析
    print(f"\n⚡ 组内能量分析:")
    for member in target_group.members:
        miss_count = target_group.internal_misses[member]
        if miss_count >= 15:
            level = "🔥 极高压力"
        elif miss_count >= 10:
            level = "⚡ 高压力"
        elif miss_count >= 5:
            level = "💧 中等压力"
        else:
            level = "❄️ 低压力"
        
        print(f"   {member}: {miss_count} 期 - {level}")
    
    # 验证2025197期开猴的影响
    print(f"\n🎯 验证2025197期开34猴的影响:")
    
    if period_2025197.get('zodiac') == '猴':
        print(f"   ✅ 确认: 2025197期开奖生肖为猴")
        print(f"   ✅ 猴在小组 {target_group.members} 中")
        print(f"   📊 命中效果:")
        print(f"      - 遗漏期数: {target_group.current_miss} → 0")
        print(f"      - 记录到历史: miss_history.append({target_group.current_miss})")
        print(f"      - 更新最大遗漏: max_miss = max({target_group.max_miss}, {target_group.current_miss})")
        print(f"      - 猴的组内遗漏: {target_group.internal_misses['猴']} → 0")
        
        # 模拟命中后的状态
        new_miss_history = target_group.miss_history + [target_group.current_miss]
        new_max_miss = max(target_group.max_miss, target_group.current_miss)
        
        if new_miss_history:
            import numpy as np
            new_mean = np.mean(new_miss_history)
            new_std = np.std(new_miss_history)
            new_z_score = (0 - new_mean) / new_std if new_std > 0 else 0
            
            print(f"   📈 命中后统计:")
            print(f"      - 新的平均遗漏: {new_mean:.2f} 期")
            print(f"      - 新的Z-Score: {new_z_score:.3f}")
            print(f"      - 总命中次数: {len(new_miss_history)} 次")
    else:
        print(f"   ❌ 生肖不匹配: 期望猴，实际 {period_2025197.get('zodiac')}")
    
    # 验证您在GUI中看到的遗漏期数
    print(f"\n🔍 验证GUI显示的遗漏期数:")
    print(f"   GUI显示: 10期遗漏")
    print(f"   计算结果: {target_group.current_miss}期遗漏")
    
    if target_group.current_miss == 10:
        print(f"   ✅ 完全匹配!")
    else:
        print(f"   ⚠️ 存在差异，可能原因:")
        print(f"      - 数据处理顺序不同")
        print(f"      - 历史数据范围不同")
        print(f"      - 小组定义略有差异")

def check_all_monkey_groups():
    """检查所有包含猴的小组"""
    print(f"\n🐒 检查所有包含猴的小组:")
    print("="*60)
    
    engine = AdvancedZodiacEngine()
    
    monkey_groups = []
    for group_id, group in engine.all_groups.items():
        if "猴" in group.members:
            monkey_groups.append((group_id, group))
    
    print(f"找到 {len(monkey_groups)} 个包含猴的小组")
    
    # 显示前10个
    for i, (group_id, group) in enumerate(monkey_groups[:10], 1):
        print(f"   {i:2d}. {group_id}: {group.members}")

if __name__ == "__main__":
    # 运行验证
    verify_group_analysis()
    
    # 检查所有包含猴的小组
    check_all_monkey_groups()
    
    print(f"\n🎯 验证完成!")
