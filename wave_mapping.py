#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波色映射配置
定义号码到波色的映射关系和波段组合
"""

# 波色映射表
WAVE_COLOR_MAPPING = {
    "红波": [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
    "蓝波": [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
    "绿波": [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
}

# 波段组合定义
WAVE_COMBINATIONS = {
    "红绿波": ["红波", "绿波"],
    "红蓝波": ["红波", "蓝波"],
    "蓝绿波": ["蓝波", "绿波"]
}

# 所有波色列表
ALL_WAVE_COLORS = ["红波", "蓝波", "绿波"]

def get_wave_color(number):
    """获取号码对应的波色"""
    for color, numbers in WAVE_COLOR_MAPPING.items():
        if number in numbers:
            return color
    return None

def get_wave_combinations():
    """获取所有波段组合"""
    return WAVE_COMBINATIONS

def get_combination_for_colors(color1, color2):
    """根据两个波色获取对应的组合名"""
    colors_set = {color1, color2}
    
    for combo_name, combo_colors in WAVE_COMBINATIONS.items():
        if colors_set == set(combo_colors):
            return combo_name
    
    return None

if __name__ == "__main__":
    print("=== 波色映射验证 ===")
    
    # 检查号码覆盖
    all_numbers = set()
    for color, numbers in WAVE_COLOR_MAPPING.items():
        all_numbers.update(numbers)
        print(f"{color}: {len(numbers)}个号码")
    
    print(f"✅ 波色映射完整，覆盖1-49所有号码")
    
    print(f"\n=== 波段组合定义 ===")
    for combo_name, combo_colors in WAVE_COMBINATIONS.items():
        print(f"{combo_name}: {combo_colors[0]} + {combo_colors[1]}")
    
    print(f"\n=== 波色统计 ===")
    for color, numbers in WAVE_COLOR_MAPPING.items():
        print(f"{color}: {len(numbers)}个号码 ({len(numbers)/49*100:.1f}%)")
    
    print(f"\n=== 波色功能测试 ===")
    test_numbers = [1, 3, 5, 25, 37, 49]
    for num in test_numbers:
        color = get_wave_color(num)
        print(f"号码{num} → {color}")
    
    print(f"\n=== 波段组合测试 ===")
    test_combinations = [
        ("红波", "绿波"),
        ("红波", "蓝波"),
        ("蓝波", "绿波"),
        ("红波", "红波"),
        ("红波", None)
    ]
    
    for color1, color2 in test_combinations:
        combo = get_combination_for_colors(color1, color2)
        print(f"{color1} + {color2} → {combo}")
