#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级生肖预测引擎
基于白皮书理论：从静态极值到动态能量的核心构建
"""

import numpy as np
from typing import Dict, List, Tuple, Any
from itertools import combinations
from dataclasses import dataclass
from datetime import datetime
from dynamic_mapping_system import DynamicMappingSystem

@dataclass
class ZodiacGroup:
    """生肖小组数据结构"""
    group_id: str
    members: List[str]  # 4个生肖成员
    current_miss: int = 0
    max_miss: int = 0
    miss_history: List[int] = None
    internal_misses: Dict[str, int] = None  # 组内能量字典
    miss_mean: float = 0.0
    miss_std_dev: float = 0.0
    z_score: float = 0.0
    
    def __post_init__(self):
        if self.miss_history is None:
            self.miss_history = []
        if self.internal_misses is None:
            self.internal_misses = {member: 0 for member in self.members}

@dataclass
class PredictionCandidate:
    """预测候选者"""
    group: ZodiacGroup
    urgency_level: str  # "极高", "高", "中等"
    energy_analysis: List[Tuple[str, int]]  # (生肖, 遗漏期数)
    recommendation_strength: float

class AdvancedZodiacEngine:
    """高级生肖预测引擎"""
    
    def __init__(self):
        self.zodiac_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        self.all_groups: Dict[str, ZodiacGroup] = {}
        self.z_score_threshold = 2.0  # Z-Score阈值
        self.min_history_length = 20  # 最小历史数据长度
        self.dynamic_mapper = DynamicMappingSystem()  # 动态映射系统

        # 生成全策略空间
        self._generate_all_4_4_4_partitions()
    
    def _generate_all_4_4_4_partitions(self):
        """生成所有5,775个4-4-4不重叠分区"""
        print("🔧 生成全策略空间...")
        
        # 生成所有4个生肖的组合
        all_4_combinations = list(combinations(self.zodiac_list, 4))
        
        partition_count = 0
        
        # 生成所有可能的4-4-4分区
        for i, group_a in enumerate(all_4_combinations):
            remaining_after_a = [z for z in self.zodiac_list if z not in group_a]
            
            # 从剩余8个生肖中选择4个作为group_b
            for group_b in combinations(remaining_after_a, 4):
                # 剩余4个自动成为group_c
                group_c = tuple(z for z in remaining_after_a if z not in group_b)
                
                # 创建大组ID
                partition_id = f"P{partition_count:04d}"
                
                # 创建三个小组
                for group_idx, group_members in enumerate([group_a, group_b, group_c]):
                    group_id = f"{partition_id}-{chr(65+group_idx)}"  # A, B, C
                    
                    self.all_groups[group_id] = ZodiacGroup(
                        group_id=group_id,
                        members=list(group_members)
                    )
                
                partition_count += 1
        
        print(f"   ✅ 生成了 {partition_count} 个分区，{len(self.all_groups)} 个小组")
    
    def calibrate_with_history(self, history_data: List[Dict]):
        """使用历史数据校准系统"""
        print("📊 开始历史数据校准...")
        
        # 第一步：双层回测
        self._backtest_on_history(history_data)
        
        # 第二步：统计建模
        self._calculate_statistical_metrics()
        
        print("   ✅ 历史校准完成")
    
    def _backtest_on_history(self, history_data: List[Dict]):
        """双层回测：更新小组和个体遗漏（使用动态映射）"""
        for period_data in history_data:
            special_code = period_data.get('special_code')
            period = period_data.get('period', '')
            if not special_code or not period:
                continue

            # 使用动态映射获取开奖生肖
            zodiac = self.dynamic_mapper.get_zodiac_for_number(special_code, period)
            if not zodiac or zodiac == "未知":
                continue
            
            # 更新所有小组
            for group_id, group in self.all_groups.items():
                if zodiac in group.members:
                    # 命中：记录当前遗漏到历史，然后清零
                    if group.current_miss > 0:
                        group.miss_history.append(group.current_miss)
                        group.max_miss = max(group.max_miss, group.current_miss)
                    
                    group.current_miss = 0
                    
                    # 清零命中生肖的个体遗漏
                    group.internal_misses[zodiac] = 0
                else:
                    # 未命中：遗漏+1
                    group.current_miss += 1
                
                # 更新组内其他生肖的遗漏
                for member in group.members:
                    if member != zodiac:
                        group.internal_misses[member] += 1
    
    def _calculate_statistical_metrics(self):
        """计算统计指标：均值、标准差"""
        for group in self.all_groups.values():
            if len(group.miss_history) >= self.min_history_length:
                group.miss_mean = np.mean(group.miss_history)
                group.miss_std_dev = np.std(group.miss_history)
            else:
                # 数据不足，使用默认值
                group.miss_mean = 5.0  # 假设平均5期
                group.miss_std_dev = 2.0  # 假设标准差2期
    
    def find_prediction_candidates(self) -> List[PredictionCandidate]:
        """寻找预测候选者"""
        candidates = []
        
        for group in self.all_groups.values():
            # 计算Z-Score
            if group.miss_std_dev > 0:
                z_score = (group.current_miss - group.miss_mean) / group.miss_std_dev
                group.z_score = z_score
            else:
                z_score = 0
            
            # 筛选候选者
            if z_score >= self.z_score_threshold:
                # 组内能量分析
                energy_analysis = sorted(
                    group.internal_misses.items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                
                # 确定紧迫程度
                if z_score >= 3.0:
                    urgency = "极高"
                    strength = 0.9
                elif z_score >= 2.5:
                    urgency = "高"
                    strength = 0.7
                else:
                    urgency = "中等"
                    strength = 0.5
                
                candidates.append(PredictionCandidate(
                    group=group,
                    urgency_level=urgency,
                    energy_analysis=energy_analysis,
                    recommendation_strength=strength
                ))
        
        # 按Z-Score排序
        candidates.sort(key=lambda x: x.group.z_score, reverse=True)
        
        return candidates
    
    def generate_advanced_report(self, candidates: List[PredictionCandidate]) -> str:
        """生成高级预测报告"""
        if not candidates:
            return "📊 当前无显著异常信号"
        
        report = f"""
🎯 高级生肖预测报告
{'='*60}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析方法: Z-Score动态统计 + 组内能量分析
候选数量: {len(candidates)} 个

"""
        
        for i, candidate in enumerate(candidates[:10], 1):  # 显示前10个
            group = candidate.group
            
            report += f"""
▶ 候选小组 #{i}: {group.members}
  📊 紧迫指标 (Z-Score): {group.z_score:.2f} ({candidate.urgency_level})
  📈 当前遗漏: {group.current_miss} 期
  📉 历史均值: {group.miss_mean:.1f} 期
  📏 标准差: {group.miss_std_dev:.1f} 期
  🎯 推荐强度: {candidate.recommendation_strength:.1%}
  
  --- 组内能量分析 ---
"""
            
            for zodiac, miss_count in candidate.energy_analysis:
                pressure_level = "🔥" if miss_count >= 15 else "⚡" if miss_count >= 10 else "💧"
                report += f"  {pressure_level} {zodiac}: {miss_count} 期遗漏\n"
            
            report += "\n"
        
        # 添加统计摘要
        high_urgency = len([c for c in candidates if c.urgency_level == "极高"])
        medium_urgency = len([c for c in candidates if c.urgency_level == "高"])
        
        report += f"""
📋 统计摘要:
  🔥 极高紧迫: {high_urgency} 个小组
  ⚡ 高紧迫: {medium_urgency} 个小组
  💡 建议关注: {candidates[0].group.members if candidates else []} (最高Z-Score)

⚠️ 风险提示:
本报告基于统计学分析，仅供参考。
Z-Score > 2.0 表示统计学异常，但不保证必然出现。
        """
        
        return report
    
    def update_with_new_result(self, special_code: int, period: str):
        """更新最新开奖结果（使用动态映射）"""
        # 使用动态映射获取生肖
        zodiac = self.dynamic_mapper.get_zodiac_for_number(special_code, period)
        if not zodiac or zodiac == "未知":
            return

        for group in self.all_groups.values():
            if zodiac in group.members:
                # 命中：记录遗漏历史，清零当前遗漏
                if group.current_miss > 0:
                    group.miss_history.append(group.current_miss)
                    group.max_miss = max(group.max_miss, group.current_miss)

                group.current_miss = 0
                group.internal_misses[zodiac] = 0

                # 重新计算统计指标
                if len(group.miss_history) >= self.min_history_length:
                    group.miss_mean = np.mean(group.miss_history)
                    group.miss_std_dev = np.std(group.miss_history)
            else:
                # 未命中：遗漏+1
                group.current_miss += 1

            # 更新组内其他生肖遗漏
            for member in group.members:
                if member != zodiac:
                    group.internal_misses[member] += 1
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        total_groups = len(self.all_groups)
        active_candidates = len([g for g in self.all_groups.values() if g.z_score >= self.z_score_threshold])
        
        avg_z_score = np.mean([g.z_score for g in self.all_groups.values()])
        max_z_score = max([g.z_score for g in self.all_groups.values()])
        
        return {
            "total_groups": total_groups,
            "active_candidates": active_candidates,
            "avg_z_score": avg_z_score,
            "max_z_score": max_z_score,
            "threshold": self.z_score_threshold,
            "calibrated": all(len(g.miss_history) > 0 for g in list(self.all_groups.values())[:100])
        }

if __name__ == "__main__":
    # 测试高级生肖引擎
    print("🧪 测试高级生肖预测引擎...")
    
    engine = AdvancedZodiacEngine()
    
    # 模拟历史数据
    mock_history = [
        {"special_code": 1, "zodiac": "猪"},
        {"special_code": 13, "zodiac": "猪"},
        {"special_code": 25, "zodiac": "猪"},
        {"special_code": 2, "zodiac": "狗"},
        {"special_code": 14, "zodiac": "狗"},
    ] * 20  # 重复20次模拟100期数据
    
    # 校准系统
    engine.calibrate_with_history(mock_history)
    
    # 查找候选者
    candidates = engine.find_prediction_candidates()
    
    # 生成报告
    report = engine.generate_advanced_report(candidates)
    print(report)
    
    # 显示系统状态
    status = engine.get_system_status()
    print(f"\n📊 系统状态: {status}")
