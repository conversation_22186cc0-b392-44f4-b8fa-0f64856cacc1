#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试格式化错误 - 精确定位问题所在
"""

import traceback
import sys

def debug_advanced_analysis():
    """调试高级分析错误"""
    print("🔍 调试高级分析格式化错误")
    print("=" * 60)
    
    try:
        print("1️⃣ 导入适配器...")
        from prediction_engine_adapter import PredictionEngineAdapter
        print("✅ 适配器导入成功")
        
        print("\n2️⃣ 初始化适配器...")
        adapter = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        print("\n3️⃣ 模拟GUI设置阈值...")
        # 模拟GUI中的操作
        adapter.advanced_zodiac_engine.z_score_threshold = 2.0
        print("✅ 阈值设置成功")
        
        print("\n4️⃣ 调用高级分析...")
        analysis_result = adapter.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 高级分析返回错误:", analysis_result['error'])
            return False
        
        print("✅ 高级分析调用成功")
        
        print("\n5️⃣ 检查返回数据...")
        candidates = analysis_result.get('candidates', [])
        print("   候选组合数量:", len(candidates))
        
        if candidates:
            print("   检查第一个候选组合...")
            first_candidate = candidates[0]
            print("   候选组合键:", list(first_candidate.keys()))
            
            # 逐个检查每个字段
            for key, value in first_candidate.items():
                print("     ", key, ":", type(value), "=", repr(value))
        
        print("\n6️⃣ 测试格式化操作...")
        
        # 测试可能有问题的格式化
        if candidates:
            candidate = candidates[0]
            
            try:
                # 测试各种格式化
                z_score = candidate.get('z_score', 0)
                z_score_str = "{:.2f}".format(z_score)
                print("   Z-Score格式化:", z_score_str)
                
                strength = candidate.get('recommendation_strength', 0)
                strength_str = "{:.1%}".format(strength)
                print("   推荐强度格式化:", strength_str)
                
                members = candidate.get('members', [])
                members_str = ", ".join(members)
                print("   成员格式化:", members_str)
                
            except Exception as e:
                print("   ❌ 格式化测试失败:", str(e))
                traceback.print_exc()
                return False
        
        print("\n✅ 所有测试通过")
        return True
        
    except Exception as e:
        print("❌ 调试过程中出现错误:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        print("\n详细错误堆栈:")
        traceback.print_exc()
        return False

def test_gui_simulation():
    """模拟GUI的完整调用流程"""
    print("\n🎯 模拟GUI完整调用流程")
    print("-" * 40)
    
    try:
        # 模拟GUI初始化
        from prediction_engine_adapter import PredictionEngineAdapter
        prediction_engine = PredictionEngineAdapter()
        
        # 模拟GUI中的zscore_threshold_var
        class MockVar:
            def get(self):
                return 2.0
        
        zscore_threshold_var = MockVar()
        
        print("1️⃣ 模拟GUI设置阈值...")
        new_threshold = zscore_threshold_var.get()
        prediction_engine.advanced_zodiac_engine.z_score_threshold = new_threshold
        print("   阈值设置为:", new_threshold)
        
        print("\n2️⃣ 模拟GUI调用分析...")
        analysis_result = prediction_engine.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 分析失败:", analysis_result['error'])
            return False
        
        print("✅ 分析成功")
        
        print("\n3️⃣ 模拟GUI数据处理...")
        candidates = analysis_result['candidates']
        
        # 模拟update_candidates_display中的操作
        for i, candidate in enumerate(candidates[:3]):
            try:
                members_str = ", ".join(candidate['members'])
                z_score_str = "{:.2f}".format(candidate['z_score'])
                strength_str = "{:.1%}".format(candidate['recommendation_strength'])
                
                print("   候选组合", i+1, ":")
                print("     成员:", members_str)
                print("     Z-Score:", z_score_str)
                print("     推荐强度:", strength_str)
                
            except Exception as e:
                print("   ❌ 候选组合", i+1, "处理失败:", str(e))
                traceback.print_exc()
                return False
        
        print("\n✅ GUI模拟测试通过")
        return True
        
    except Exception as e:
        print("❌ GUI模拟测试失败:", str(e))
        traceback.print_exc()
        return False

def check_string_formatting():
    """检查字符串格式化问题"""
    print("\n🔧 检查字符串格式化问题")
    print("-" * 40)
    
    # 测试各种可能有问题的格式化
    test_cases = [
        ("简单数字", 1.234, "{:.2f}"),
        ("百分比", 0.123, "{:.1%}"),
        ("整数", 42, "{:,}"),
        ("字符串列表", ["a", "b", "c"], ", ".join),
    ]
    
    for name, value, formatter in test_cases:
        try:
            if callable(formatter):
                result = formatter(value)
            else:
                result = formatter.format(value)
            print("✅", name, ":", result)
        except Exception as e:
            print("❌", name, "失败:", str(e))
            return False
    
    # 测试字典格式化（可能的问题源）
    test_dict = {'key': 'value', 'number': 123}
    
    try:
        # 这种格式化可能有问题
        # result = f"{test_dict}"  # 这个应该没问题
        
        # 但这种可能有问题
        # result = "{test_dict}".format(test_dict=test_dict)  # 这个可能有问题
        
        print("✅ 字典格式化测试通过")
        return True
        
    except Exception as e:
        print("❌ 字典格式化失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动格式化错误调试...")
    
    # 运行所有测试
    tests = [
        ("高级分析调试", debug_advanced_analysis),
        ("GUI模拟测试", test_gui_simulation),
        ("字符串格式化检查", check_string_formatting)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("\n" + "="*60)
        print("🧪", test_name)
        print("="*60)
        
        success = test_func()
        results.append((test_name, success))
    
    # 总结结果
    print("\n" + "="*60)
    print("📋 调试结果总结")
    print("="*60)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(test_name + ":", status)
        if not success:
            all_passed = False
    
    print("\n🎯 总体结果:", "✅ 所有测试通过" if all_passed else "❌ 发现问题")
    
    if not all_passed:
        print("\n💡 建议:")
        print("1. 检查上述失败的测试")
        print("2. 查看详细的错误堆栈")
        print("3. 重点关注格式化相关的代码")
    
    return all_passed

if __name__ == "__main__":
    main()
