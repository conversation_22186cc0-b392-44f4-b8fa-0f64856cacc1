#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行测试 - 验证预测系统实际运行情况
"""

import os
import csv
import sqlite3
import json
from datetime import datetime

def test_execution():
    """测试系统执行"""
    print("🔧 六合彩预测系统执行测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'tests': {},
        'overall_status': 'UNKNOWN'
    }
    
    # 测试1: 文件存在性
    print("\n📁 测试1: 核心文件检查")
    files_test = test_core_files()
    results['tests']['files'] = files_test
    
    # 测试2: 数据库连接
    print("\n🗄️ 测试2: 数据库连接")
    db_test = test_database_connection()
    results['tests']['database'] = db_test
    
    # 测试3: CSV数据读取
    print("\n📊 测试3: CSV数据读取")
    csv_test = test_csv_data()
    results['tests']['csv_data'] = csv_test
    
    # 测试4: 模块功能
    print("\n🔧 测试4: 模块功能测试")
    module_test = test_module_functionality()
    results['tests']['modules'] = module_test
    
    # 测试5: 完整流程模拟
    print("\n🚀 测试5: 完整流程模拟")
    workflow_test = test_complete_workflow()
    results['tests']['workflow'] = workflow_test
    
    # 计算总体状态
    passed_tests = sum(1 for test in results['tests'].values() if test.get('status') == 'PASS')
    total_tests = len(results['tests'])
    
    if passed_tests == total_tests:
        results['overall_status'] = 'EXCELLENT'
        status_emoji = "🎉"
        status_text = "优秀"
    elif passed_tests >= total_tests * 0.8:
        results['overall_status'] = 'GOOD'
        status_emoji = "✅"
        status_text = "良好"
    else:
        results['overall_status'] = 'NEEDS_ATTENTION'
        status_emoji = "⚠️"
        status_text = "需要关注"
    
    # 生成总结
    print(f"\n📋 测试总结")
    print("=" * 30)
    print(f"通过测试: {passed_tests}/{total_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    print(f"总体状态: {status_emoji} {status_text}")
    
    # 保存结果
    with open('execution_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存: execution_test_results.json")
    
    return results

def test_core_files():
    """测试核心文件"""
    files_to_check = [
        'main.py',
        'combo_generator.py',
        'extreme_stat_tracker.py', 
        'strategy_scorer.py',
        'lottery_data.db',
        'lottery_data_20250717.csv'
    ]
    
    results = {'status': 'PASS', 'details': {}}
    
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file)
            results['details'][file] = {'exists': True, 'size': size}
            print(f"  ✅ {file}: {size} bytes")
        else:
            results['details'][file] = {'exists': False, 'size': 0}
            results['status'] = 'FAIL'
            print(f"  ❌ {file}: 不存在")
    
    return results

def test_database_connection():
    """测试数据库连接"""
    try:
        conn = sqlite3.connect('lottery_data.db')
        cursor = conn.cursor()
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"  ✅ 数据库连接成功")
        print(f"  📊 发现表: {len(tables)} 个")
        
        # 检查主要表的数据
        table_info = {}
        for table in ['lottery_records', 'lottery_records_extended']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_info[table] = count
                print(f"    - {table}: {count} 条记录")
        
        conn.close()
        
        return {
            'status': 'PASS',
            'tables': tables,
            'table_info': table_info
        }
        
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return {
            'status': 'FAIL',
            'error': str(e)
        }

def test_csv_data():
    """测试CSV数据读取"""
    try:
        with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        if not rows:
            print(f"  ❌ CSV文件为空")
            return {'status': 'FAIL', 'error': 'Empty file'}
        
        # 检查字段
        first_row = rows[0]
        expected_fields = ['period_number', 'draw_date', 'special_code', 'zodiac', 'five_element']
        
        # 处理BOM字符
        actual_fields = list(first_row.keys())
        if '\ufeffperiod_number' in actual_fields:
            actual_fields = [f.replace('\ufeff', '') for f in actual_fields]
        
        missing_fields = [f for f in expected_fields if f not in [field.replace('\ufeff', '') for field in first_row.keys()]]
        
        if missing_fields:
            print(f"  ❌ 缺少字段: {missing_fields}")
            return {'status': 'FAIL', 'missing_fields': missing_fields}
        
        print(f"  ✅ CSV数据读取成功")
        print(f"  📊 记录数量: {len(rows)}")
        print(f"  📅 数据范围: {rows[0].get('period_number', rows[0].get('\ufeffperiod_number'))} 到 {rows[-1]['period_number']}")
        
        return {
            'status': 'PASS',
            'record_count': len(rows),
            'fields': list(first_row.keys())
        }
        
    except Exception as e:
        print(f"  ❌ CSV读取失败: {e}")
        return {'status': 'FAIL', 'error': str(e)}

def test_module_functionality():
    """测试模块功能"""
    try:
        # 测试组合生成器
        print("  🔧 测试组合生成器...")
        from combo_generator import ComboGenerator
        
        gen = ComboGenerator()
        shengxiao_4 = gen.generate_shengxiao_4()
        wuxing_2 = gen.generate_wuxing_2()
        
        print(f"    ✅ 生肖4组合: {len(shengxiao_4)} 个")
        print(f"    ✅ 五行2组合: {len(wuxing_2)} 个")
        
        # 测试统计追踪器
        print("  📈 测试统计追踪器...")
        from extreme_stat_tracker import ExtremeStatTracker
        
        # 创建测试数据
        test_combos = shengxiao_4[:3]  # 只测试3个组合
        test_history = [
            {'period': '2025001', 'zodiac': '鼠', 'five_element': '金'},
            {'period': '2025002', 'zodiac': '牛', 'five_element': '木'}
        ]
        
        tracker = ExtremeStatTracker(None, test_combos, test_history)
        tracker.run_tracking()
        print(f"    ✅ 统计追踪完成: {len(test_combos)} 个组合")
        
        # 测试策略评分器
        print("  🎯 测试策略评分器...")
        from strategy_scorer import StrategyScorerAndFusionEngine
        
        test_strategies = {
            "test_strategy": {
                "hit_count": 5,
                "avg_omit_before_hit": 8,
                "predicted_numbers": [1, 2, 3, 4, 5]
            }
        }
        
        scorer = StrategyScorerAndFusionEngine(test_strategies, test_history)
        scores = scorer.calculate_scores()
        print(f"    ✅ 策略评分完成: {len(scores)} 个策略")
        
        return {
            'status': 'PASS',
            'combo_count': len(shengxiao_4) + len(wuxing_2),
            'strategy_count': len(scores)
        }
        
    except Exception as e:
        print(f"  ❌ 模块测试失败: {e}")
        return {'status': 'FAIL', 'error': str(e)}

def test_complete_workflow():
    """测试完整工作流程"""
    try:
        print("  🚀 执行完整预测流程...")
        
        # 模拟main.py的执行
        from combo_generator import ComboGenerator
        from extreme_stat_tracker import ExtremeStatTracker
        from strategy_scorer import StrategyScorerAndFusionEngine
        
        # 1. 生成组合
        combo_gen = ComboGenerator()
        shengxiao_4_combos = combo_gen.generate_shengxiao_4()
        wuxing_2_combos = combo_gen.generate_wuxing_2()
        all_combos = shengxiao_4_combos + wuxing_2_combos
        print(f"    ✅ 组合生成: {len(all_combos)} 个")
        
        # 2. 加载历史数据
        history = []
        with open('lottery_data_20250717.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                period_key = 'period_number'
                if '\ufeffperiod_number' in row:
                    period_key = '\ufeffperiod_number'
                
                history.append({
                    'period': row[period_key],
                    'zodiac': row['zodiac'],
                    'five_element': row['five_element']
                })
        
        history_draws = list(reversed(history))
        print(f"    ✅ 数据加载: {len(history_draws)} 条记录")
        
        # 3. 统计追踪 (只处理前5个组合以节省时间)
        test_combos = all_combos[:5]
        tracker = ExtremeStatTracker(None, test_combos, history_draws)
        tracker.run_tracking()
        print(f"    ✅ 统计追踪: {len(test_combos)} 个组合")
        
        # 4. 策略评分
        strategies = {
            "shengxiao_4_extreme": {
                "hit_count": 5,
                "avg_omit_before_hit": 8,
                "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
            },
            "wuxing_2_hot": {
                "hit_count": 10,
                "avg_omit_before_hit": 4,
                "predicted_numbers": [5, 10, 15, 20, 25, 30, 35]
            }
        }
        
        scorer = StrategyScorerAndFusionEngine(strategies, history_draws)
        strategy_scores = scorer.calculate_scores()
        print(f"    ✅ 策略评分: {len(strategy_scores)} 个策略")
        
        # 5. 预测融合
        predictions_to_fuse = [
            {
                "strategy_id": strat_id,
                "weight": strategy_scores[strat_id]["score"],
                "numbers": strat_data["predicted_numbers"]
            }
            for strat_id, strat_data in strategies.items()
        ]
        
        final_prediction = scorer.fuse_predictions(predictions_to_fuse, method='weighted_union', top_n=12)
        print(f"    ✅ 预测融合: {len(final_prediction)} 个号码")
        print(f"    🎯 预测结果: {sorted(final_prediction)}")
        
        return {
            'status': 'PASS',
            'final_prediction': sorted(final_prediction),
            'strategy_scores': strategy_scores
        }
        
    except Exception as e:
        print(f"  ❌ 完整流程测试失败: {e}")
        return {'status': 'FAIL', 'error': str(e)}

if __name__ == "__main__":
    test_execution()
