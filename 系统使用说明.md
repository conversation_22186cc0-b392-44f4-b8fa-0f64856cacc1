# 六合彩预测系统使用说明

## 📋 系统状态概览

**当前状态**: ✅ **基础功能可用**  
**检查时间**: 2025-07-23  
**Python版本**: 3.10 (从__pycache__推断)

## 🗂️ 核心文件结构

```
六合彩预测软件系统/
├── main.py                    # 主程序入口 ✅
├── combo_generator.py         # 组合生成器 ✅
├── extreme_stat_tracker.py    # 极值统计追踪 ✅
├── strategy_scorer.py         # 策略评分融合 ✅
├── lottery_data.db           # SQLite数据库 ✅
├── lottery_data_20250717.csv # 历史数据CSV ✅
├── gui_main.py               # GUI界面 ⚠️(需依赖)
├── ml_models.py              # 机器学习模型 ⚠️(需依赖)
└── requirements.txt          # 依赖列表 ✅
```

## 🚀 快速开始

### 1. 基础功能测试
```bash
# 运行系统测试
python test_basic_system.py

# 快速状态检查
python quick_status.py

# 运行主预测程序
python main.py
```

### 2. 安装完整依赖
```bash
# 方法1: 使用pip
pip install -r requirements.txt

# 方法2: 手动安装核心包
pip install numpy pandas scikit-learn PyYAML openpyxl

# 方法3: 运行安装脚本
install_dependencies.bat    # Windows批处理
# 或
install_dependencies.ps1   # PowerShell脚本
```

### 3. 启动完整系统
```bash
# 启动GUI界面
python gui_main.py

# 运行完整系统检查
python system_status_check.py
```

## 🔧 核心功能说明

### 1. 组合生成 (combo_generator.py)
- **生肖4组合**: 从12生肖中选择4个的所有组合
- **五行2组合**: 从5个五行中选择2个的所有组合  
- **色波组合**: 红绿波、红蓝波、蓝绿波组合
- **多维生肖组合**: 基于生肖标签的复合组合

```python
from combo_generator import ComboGenerator

gen = ComboGenerator()
shengxiao_4 = gen.generate_shengxiao_4()  # 495个组合
wuxing_2 = gen.generate_wuxing_2()        # 10个组合
```

### 2. 统计追踪 (extreme_stat_tracker.py)
- **极值统计**: 追踪各组合的遗漏期数
- **命中分析**: 计算命中次数和频率
- **趋势判断**: 识别接近极值的组合

```python
from extreme_stat_tracker import ExtremeStatTracker

tracker = ExtremeStatTracker(db_conn, combos, history)
tracker.run_tracking()
```

### 3. 策略评分 (strategy_scorer.py)
- **性能评分**: 基于命中率和平均遗漏计算策略分数
- **预测融合**: 多策略预测结果的加权融合
- **号码筛选**: 从融合结果中选择最优号码

```python
from strategy_scorer import StrategyScorerAndFusionEngine

scorer = StrategyScorerAndFusionEngine(strategies, history)
scores = scorer.calculate_scores()
final_prediction = scorer.fuse_predictions(predictions)
```

## 📊 数据文件说明

### 1. 历史数据 (lottery_data_20250717.csv)
```csv
period_number,draw_date,special_code,zodiac,five_element
2020067,2020-03-07,1,鼠,土
2020068,2020-03-08,9,龙,水
...
```
- **记录数量**: 1942条
- **时间范围**: 2020-03-07 至最新
- **字段说明**: 期号、开奖日期、特码、生肖、五行

### 2. 数据库 (lottery_data.db)
- **lottery_records**: 基础历史记录
- **lottery_records_extended**: 扩展属性记录
- **zodiac_number_mapping**: 生肖号码映射
- **five_element_year_mapping**: 五行年份映射

## 🎯 预测流程

### 标准预测流程
1. **数据加载**: 从CSV或数据库加载历史数据
2. **组合生成**: 生成各种类型的号码组合
3. **统计分析**: 计算各组合的极值统计
4. **策略定义**: 基于统计结果定义预测策略
5. **策略评分**: 评估各策略的历史表现
6. **预测融合**: 融合多个策略的预测结果
7. **结果输出**: 生成最终的预测号码

### 示例代码
```python
# 完整预测示例
from combo_generator import ComboGenerator
from extreme_stat_tracker import ExtremeStatTracker
from strategy_scorer import StrategyScorerAndFusionEngine

# 1. 生成组合
gen = ComboGenerator()
combos = gen.generate_shengxiao_4() + gen.generate_wuxing_2()

# 2. 加载历史数据
history = load_historical_data('lottery_data_20250717.csv')

# 3. 运行统计追踪
tracker = ExtremeStatTracker(None, combos, history)
tracker.run_tracking()

# 4. 定义和评分策略
strategies = {
    "strategy_1": {
        "hit_count": 5,
        "avg_omit_before_hit": 8,
        "predicted_numbers": [3, 10, 17, 24, 31, 38, 45]
    }
}

scorer = StrategyScorerAndFusionEngine(strategies, history)
scores = scorer.calculate_scores()

# 5. 融合预测
predictions = [
    {
        "strategy_id": "strategy_1",
        "weight": scores["strategy_1"]["score"],
        "numbers": strategies["strategy_1"]["predicted_numbers"]
    }
]

final_prediction = scorer.fuse_predictions(predictions, top_n=12)
print(f"最终预测: {sorted(final_prediction)}")
```

## ⚠️ 注意事项

### 依赖要求
- **Python**: 3.7+ (推荐3.10+)
- **必需包**: 无 (核心功能只使用标准库)
- **可选包**: numpy, pandas, scikit-learn, PyYAML, openpyxl

### 性能考虑
- **组合数量**: 生肖4组合有495个，计算量较大
- **历史数据**: 1942条记录，处理速度较快
- **内存使用**: 基础功能内存占用较小

### 使用建议
1. **首次使用**: 先运行`test_basic_system.py`确认基础功能
2. **数据更新**: 定期更新`lottery_data_20250717.csv`文件
3. **策略调优**: 根据回测结果调整策略参数
4. **结果验证**: 对预测结果进行历史回测验证

## 🔍 故障排除

### 常见问题
1. **模块导入失败**: 确认文件在同一目录下
2. **数据文件缺失**: 检查CSV和数据库文件是否存在
3. **编码问题**: 确保CSV文件使用UTF-8编码
4. **依赖缺失**: 安装requirements.txt中的包

### 调试方法
```bash
# 检查文件完整性
python quick_status.py

# 测试基础功能
python test_basic_system.py

# 详细系统检查
python system_status_check.py
```

## 📈 扩展功能

### 可用扩展
- **GUI界面**: `gui_main.py` (需要tkinter)
- **机器学习**: `ml_models.py` (需要scikit-learn)
- **报告生成**: `report_generator.py` (需要openpyxl)
- **回测引擎**: `backtest_engine.py`

### 开发建议
1. **模块化设计**: 保持各模块独立性
2. **配置管理**: 使用YAML配置文件
3. **日志记录**: 添加详细的运行日志
4. **单元测试**: 为核心功能编写测试用例

---

**最后更新**: 2025-07-23  
**系统版本**: 基础版本 v1.0  
**维护状态**: 活跃开发中
