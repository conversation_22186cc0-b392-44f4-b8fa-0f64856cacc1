import sqlite3
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import json
from pathlib import Path
import logging
import threading
import queue
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class LotteryDataManager:
    """
    彩票数据管理类
    负责数据导入、更新和缓存管理
    """
    def __init__(self, db_path: str = "lottery_data.db", config_path: str = "data_config.json"):
        """
        初始化数据管理器
        Args:
            db_path: 数据库文件路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = config_path
        self.conn = self._init_database()
        self.config = self._load_config()
        self.data_cache = {}
        self.cache_lock = threading.Lock()
        self.update_queue = queue.Queue()

        # 启动数据更新监视器
        self._start_file_monitor()

    def _init_database(self) -> sqlite3.Connection:
        """初始化数据库连接和表结构"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        cursor = conn.cursor()

        # 创建开奖记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lottery_draws (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            draw_date DATE NOT NULL,
            period TEXT NOT NULL UNIQUE,
            numbers TEXT NOT NULL,
            special_number INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            source TEXT,
            status TEXT DEFAULT 'active',
            UNIQUE(period)
        )
        ''')

        # 创建数据源配置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS data_sources (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL,
            path TEXT NOT NULL,
            update_frequency INTEGER DEFAULT 0,
            last_update TIMESTAMP,
            status TEXT DEFAULT 'active',
            config TEXT,
            UNIQUE(name)
        )
        ''')

        # 创建更新日志表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS update_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_id INTEGER,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT,
            details TEXT,
            FOREIGN KEY(source_id) REFERENCES data_sources(id)
        )
        ''')

        conn.commit()
        return conn

    def _load_config(self) -> Dict:
        """加载数据配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 创建默认配置
            default_config = {
                "data_sources": [],
                "update_settings": {
                    "auto_update": True,
                    "update_interval": 300,  # 5分钟
                    "retry_attempts": 3,
                    "cache_duration": 3600   # 1小时
                },
                "import_settings": {
                    "batch_size": 1000,
                    "validate_data": True,
                    "backup_before_import": True
                }
            }
            self._save_config(default_config)
            return default_config

    def _save_config(self, config: Dict) -> None:
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)

    def add_data_source(self, name: str, source_type: str, path: str,
                       update_freq: int = 0, config: Dict = None) -> None:
        """
        添加新的数据源
        Args:
            name: 数据源名称
            source_type: 数据源类型(csv/excel/api等)
            path: 数据源路径
            update_freq: 更新频率(秒)
            config: 数据源特定配置
        """
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO data_sources (name, type, path, update_frequency, config)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, source_type, path, update_freq, json.dumps(config or {})))
        self.conn.commit()

    def import_from_csv(self, file_path: str, source_name: str = None) -> Tuple[int, int]:
        """
        从CSV文件导入数据
        Args:
            file_path: CSV文件路径
            source_name: 数据源名称
        Returns:
            Tuple[int, int]: (导入记录数, 更新记录数)
        """
        try:
            df = pd.read_csv(file_path)

            # 数据验证和清洗
            df = self._validate_and_clean_data(df)

            cursor = self.conn.cursor()
            inserted = 0
            updated = 0

            for _, row in df.iterrows():
                try:
                    cursor.execute('''
                        INSERT INTO lottery_draws
                        (draw_date, period, numbers, special_number, source)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        row['draw_date'],
                        row['period'],
                        ','.join(map(str, sorted(map(int, row['numbers'].split(','))))),
                        int(row['special_number']),
                        source_name
                    ))
                    inserted += 1
                except sqlite3.IntegrityError:
                    # 记录已存在，尝试更新
                    cursor.execute('''
                        UPDATE lottery_draws
                        SET numbers = ?, special_number = ?, source = ?
                        WHERE period = ?
                    ''', (
                        ','.join(map(str, sorted(map(int, row['numbers'].split(','))))),
                        int(row['special_number']),
                        source_name,
                        row['period']
                    ))
                    updated += 1

            self.conn.commit()
            self._log_update(source_name, "success", f"Inserted: {inserted}, Updated: {updated}")
            return inserted, updated

        except Exception as e:
            self._log_update(source_name, "error", str(e))
            raise

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        验证和清洗数据
        Args:
            df: 原始数据框
        Returns:
            pd.DataFrame: 清洗后的数据框
        """
        required_columns = ['draw_date', 'period', 'numbers', 'special_number']

        # 检查必要列
        missing_cols = set(required_columns) - set(df.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")

        # 转换日期格式
        df['draw_date'] = pd.to_datetime(df['draw_date']).dt.date

        # 验证期号格式
        if not df['period'].str.match(r'^\d{6,8}$').all():
            raise ValueError("Invalid period format")

        # 验证号码格式
        def validate_numbers(numbers_str: str) -> bool:
            try:
                nums = list(map(int, numbers_str.split(',')))
                return all(1 <= n <= 49 for n in nums)
            except:
                return False

        invalid_numbers = ~df['numbers'].apply(validate_numbers)
        if invalid_numbers.any():
            raise ValueError(f"Invalid numbers found in rows: {df.index[invalid_numbers].tolist()}")

        return df

    def _log_update(self, source_name: str, status: str, details: str) -> None:
        """记录更新日志"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO update_logs (source_id, status, details)
            SELECT id, ?, ?
            FROM data_sources
            WHERE name = ?
        ''', (status, details, source_name))
        self.conn.commit()

    def get_latest_draw(self) -> Optional[Dict]:
        """获取最新一期开奖数据"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT draw_date, period, numbers, special_number
            FROM lottery_draws
            ORDER BY draw_date DESC, period DESC
            LIMIT 1
        ''')
        row = cursor.fetchone()

        if row:
            return {
                'draw_date': row[0],
                'period': row[1],
                'numbers': list(map(int, row[2].split(','))),
                'special_number': row[3]
            }
        return None

    def get_history_data(self, start_date: str = None,
                        end_date: str = None) -> List[Dict]:
        """获取历史数据"""
        cursor = self.conn.cursor()
        query = '''
            SELECT draw_date, period, numbers, special_number
            FROM lottery_draws
            WHERE 1=1
        '''
        params = []

        if start_date:
            query += ' AND draw_date >= ?'
            params.append(start_date)
        if end_date:
            query += ' AND draw_date <= ?'
            params.append(end_date)

        query += ' ORDER BY draw_date DESC, period DESC'

        cursor.execute(query, params)
        return [
            {
                'draw_date': row[0],
                'period': row[1],
                'numbers': list(map(int, row[2].split(','))),
                'special_number': row[3]
            }
            for row in cursor.fetchall()
        ]

    def _start_file_monitor(self) -> None:
        """启动文件监视器"""
        class DataSourceHandler(FileSystemEventHandler):
            def __init__(self, manager):
                self.manager = manager

            def on_modified(self, event):
                if not event.is_directory:
                    self.manager.update_queue.put(event.src_path)

        self.observer = Observer()

        # 监视所有CSV数据源
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT path FROM data_sources
            WHERE type = 'csv' AND status = 'active'
        ''')

        for (path,) in cursor.fetchall():
            file_path = Path(path)
            if file_path.exists():
                self.observer.schedule(
                    DataSourceHandler(self),
                    str(file_path.parent),
                    recursive=False
                )

        self.observer.start()

        # 启动更新处理线程
        threading.Thread(target=self._process_updates, daemon=True).start()

    def _process_updates(self) -> None:
        """处理更新队列"""
        while True:
            try:
                file_path = self.update_queue.get(timeout=1)
                cursor = self.conn.cursor()

                # 查找对应的数据源
                cursor.execute('''
                    SELECT name FROM data_sources
                    WHERE path = ? AND status = 'active'
                ''', (file_path,))

                row = cursor.fetchone()
                if row:
                    source_name = row[0]
                    try:
                        self.import_from_csv(file_path, source_name)
                        logging.info(f"Successfully updated data from {source_name}")
                    except Exception as e:
                        logging.error(f"Error updating data from {source_name}: {e}")

            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error in update processor: {e}")

            time.sleep(1)  # 避免过于频繁的检查

    def clear_cache(self) -> None:
        """清除缓存数据"""
        with self.cache_lock:
            self.data_cache.clear()

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'observer'):
            self.observer.stop()
            self.observer.join()
        if hasattr(self, 'conn'):
            self.conn.close()
