# 📝 手动输入历史数据功能说明

## 🎉 **功能已完成！**

您的六合彩智能预测系统现在支持完整的手动输入历史开奖数据功能，包括自动属性映射和数据更新。

---

## ✨ **新增功能特性**

### 🔧 **核心功能**
- ✅ **手动输入开奖数据** - 期号、日期、特码
- ✅ **自动属性映射** - 生肖、五行、波色、大小、单双、尾数
- ✅ **数据更新机制** - 同期号数据自动更新
- ✅ **数据验证** - 格式检查和范围验证
- ✅ **实时预览** - 输入时即时显示属性
- ✅ **文件保存** - 自动保存到CSV文件

### 📊 **属性映射规则**

**生肖映射** (按号码循环):
- 1,13,25,37,49 → 猪
- 2,14,26,38 → 狗  
- 3,15,27,39 → 鸡
- 4,16,28,40 → 猴
- 5,17,29,41 → 羊
- 6,18,30,42 → 马
- 7,19,31,43 → 蛇
- 8,20,32,44 → 龙
- 9,21,33,45 → 兔
- 10,22,34,46 → 虎
- 11,23,35,47 → 牛
- 12,24,36,48 → 鼠

**五行映射**:
- 金: 4,5,12,13,20,21,28,29,36,37,44,45
- 木: 1,2,9,10,17,18,25,26,33,34,41,42
- 水: 6,7,14,15,22,23,30,31,38,39,46,47
- 火: 3,8,11,16,19,24,27,32,35,40,43,48
- 土: 49

**波色映射**:
- 红波: 1,2,7,8,12,13,18,19,23,24,29,30,34,35,40,45,46
- 蓝波: 3,4,9,10,14,15,20,25,26,31,36,37,41,42,47,48
- 绿波: 5,6,11,16,17,21,22,27,28,32,33,38,39,43,44,49

**其他属性**:
- 大小: ≥25为大，<25为小
- 单双: 奇数为单，偶数为双
- 尾数: 号码的个位数

---

## 🖥️ **GUI界面使用方法**

### **方式1: 完整GUI界面**
1. 运行 `python gui_main.py` 或 `python start_gui.py`
2. 切换到"📊 历史数据"页面
3. 点击"➕ 手动输入"按钮
4. 在弹出窗口中输入数据

### **方式2: 简化GUI界面**
1. 运行 `python start_gui.py`
2. 点击"➕ 手动输入"按钮
3. 在弹出窗口中输入数据

### **方式3: 独立测试界面**
1. 运行 `python test_manual_input.py`
2. 直接在测试界面中输入数据

---

## 📝 **输入界面详解**

### **输入字段**
1. **期号** - 格式: 2025001 (7位数字)
   - 年份: 2020-2030
   - 期数: 001-365

2. **开奖日期** - 格式: 2025-07-18 (YYYY-MM-DD)
   - 自动填充当前日期
   - 支持手动修改

3. **特码** - 范围: 1-49
   - 必须是整数
   - 自动验证范围

### **实时预览功能**
- 输入特码后立即显示:
  - 生肖、五行、波色
  - 大小、单双、尾数
- 颜色提示:
  - 绿色 = 有效数据
  - 红色 = 无效数据
  - 灰色 = 未输入

### **操作按钮**
- **🔍 检查现有** - 查看期号是否已存在数据
- **💾 保存数据** - 保存到内存和文件
- **❌ 取消** - 关闭输入窗口

---

## 🔄 **数据更新机制**

### **新增数据**
- 输入不存在的期号 → 添加新记录
- 提示: "期号 XXXXXXX 的数据已添加"

### **更新数据**
- 输入已存在的期号 → 更新现有记录
- 提示: "期号 XXXXXXX 的数据已更新"
- 后输入的数据会覆盖前面的数据

### **数据验证**
- 期号格式检查 (7位数字)
- 日期格式检查 (YYYY-MM-DD)
- 特码范围检查 (1-49)
- 失败时显示具体错误信息

---

## 💾 **数据存储**

### **内存管理**
- 使用 `HistoryDataManager` 类管理数据
- 数据缓存在内存中，支持快速访问
- 按期号自动排序 (最新在前)

### **文件保存**
- 自动保存到 `lottery_data_20250717.csv`
- CSV格式，包含所有字段
- 支持Excel打开编辑

### **数据完整性**
- 自动检查数据完整性
- 统计有效记录数量
- 显示数据完整率

---

## 🧪 **测试功能**

### **独立测试脚本**
```bash
python test_manual_input.py
```

**测试功能包括**:
- 数据输入测试
- 属性映射验证
- 文件保存测试
- 数据更新测试
- 统计信息显示

### **测试数据示例**
```
期号: 2025201
日期: 2025-07-18  
特码: 25
→ 生肖: 猪, 五行: 木, 波色: 蓝波, 大小: 大, 单双: 单, 尾数: 5
```

---

## 🔧 **技术实现**

### **核心模块**
- `data_attributes.py` - 数据属性映射和管理
- `DataAttributeMapper` - 属性映射器类
- `HistoryDataManager` - 历史数据管理器类

### **GUI集成**
- 完整GUI: `gui_main.py` 中的 `manual_input_data()` 方法
- 简化GUI: `start_gui.py` 中的 `manual_input_data()` 函数
- 独立测试: `test_manual_input.py`

### **数据流程**
1. 用户输入 → 格式验证
2. 特码映射 → 属性计算  
3. 数据保存 → 内存缓存
4. 文件写入 → CSV格式
5. 界面刷新 → 显示更新

---

## 🎯 **使用场景**

### **日常使用**
- 手动录入最新开奖结果
- 补充缺失的历史数据
- 修正错误的开奖记录

### **数据维护**
- 批量更新历史数据
- 验证数据完整性
- 备份重要开奖记录

### **系统集成**
- 与预测引擎无缝集成
- 支持实时数据更新
- 自动刷新分析结果

---

## ⚠️ **注意事项**

### **数据格式要求**
- 期号必须是7位数字
- 日期必须是YYYY-MM-DD格式
- 特码必须在1-49范围内

### **数据更新规则**
- 相同期号的数据会被覆盖
- 建议在更新前检查现有数据
- 重要数据请及时备份

### **文件安全**
- 系统会自动备份原文件
- 建议定期导出数据备份
- 避免同时多个程序操作同一文件

---

## 🎉 **功能完成总结**

### ✅ **已实现功能**
- ✅ 手动输入界面 (完整版 + 简化版)
- ✅ 自动属性映射 (生肖、五行、波色等)
- ✅ 数据更新机制 (同期号覆盖)
- ✅ 实时预览功能
- ✅ 数据验证检查
- ✅ 文件自动保存
- ✅ 统计信息显示
- ✅ 错误处理机制

### 🚀 **系统状态**
**完全就绪！** 您的六合彩智能预测系统现在拥有完整的数据管理功能:

- 📊 历史数据查看
- ➕ 手动数据输入  
- 🔄 自动属性映射
- 💾 数据保存管理
- 📈 统计分析功能
- 🎯 预测引擎集成

**🎲 开始使用您的完整预测系统吧！** ✨
