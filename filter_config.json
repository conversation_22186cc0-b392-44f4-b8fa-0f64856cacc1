{"filter_config": {"large_filter": {"zodiac_weights": {"rat": 1.2, "ox": 1.0, "tiger": 1.1, "rabbit": 0.9, "dragon": 1.1, "snake": 1.0, "horse": 0.9, "sheep": 1.0, "monkey": 1.1, "rooster": 1.0, "dog": 0.9, "pig": 1.0}, "element_weights": {"metal": 1.1, "wood": 1.0, "water": 1.2, "fire": 0.9, "earth": 1.0}}, "medium_filter": {"odd_even_ratio": 0.6, "size_threshold": 25, "prime_weight": 0.4}}, "tail_group_settings": {"min_group_size": 3, "max_group_size": 6, "min_tail_value": 0, "max_tail_value": 9, "hot_group_threshold": 5, "analysis_window": 30}, "database_settings": {"backup_interval": 24, "max_history_records": 1000}, "filter_settings": {"enable_performance_logging": true, "cache_results": true, "parallel_processing": true}, "groups": [{"label": "01369", "tails": [0, 1, 3, 6, 9], "description": "Popular tail combination"}, {"label": "2478", "tails": [2, 4, 7, 8], "description": "Alternate tail combination"}, {"label": "0369", "tails": [0, 3, 6, 9], "description": "Three interval tail combination"}, {"label": "1478", "tails": [1, 4, 7, 8], "description": "Mixed tail combination"}]}