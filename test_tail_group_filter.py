import unittest
from tail_group_filter import TailGroupFilter
import sqlite3
from pathlib import Path
import tempfile
import os
import json

class TestTailGroupFilter(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        # 使用临时数据库文件
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()

        # 使用临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
        test_config = {
            "groups": [
                {
                    "label": "测试组合1",
                    "tails": [1, 2, 3],
                    "description": "测试用组合1",
                    "type": "basic"
                },
                {
                    "label": "测试组合2",
                    "tails": [4, 5, 6],
                    "description": "测试用组合2",
                    "type": "basic"
                }
            ]
        }
        with open(self.temp_config.name, 'w', encoding='utf-8') as f:
            json.dump(test_config, f)

        # 初始化过滤器
        self.filter = TailGroupFilter(
            db_path=self.temp_db.name,
            config_file=self.temp_config.name
        ).__enter__()

        # 创建测试数据表
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_results (
                    draw_date TEXT,
                    numbers TEXT,
                    special_number INTEGER
                )
            ''')
            # 插入一些测试数据
            test_data = [
                ('2025-07-01', '1,11,21,31,41,2', 12),
                ('2025-07-02', '3,13,23,33,43,4', 14),
                ('2025-07-03', '5,15,25,35,45,6', 16)
            ]
            cursor.executemany(
                'INSERT INTO lottery_results VALUES (?, ?, ?)',
                test_data
            )
            conn.commit()

    def tearDown(self):
        """清理测试环境"""
        # 关闭过滤器
        self.filter.__exit__(None, None, None)

        # 删除临时文件
        try:
            os.unlink(self.temp_db.name)
            os.unlink(self.temp_config.name)
        except (OSError, IOError):
            pass  # Ignore deletion errors

    def test_get_numbers_by_tail(self):
        """测试根据尾数获取号码"""
        tails = {1, 2}
        expected = {1, 2, 11, 12, 21, 22, 31, 32, 41, 42}
        result = self.filter.get_numbers_by_tail(tails)
        self.assertEqual(result, expected)

    def test_filter_candidates(self):
        """测试候选号码筛选"""
        candidates = [1, 2, 11, 15, 21, 25, 31, 35]
        result = self.filter.filter_candidates(candidates)
        self.assertTrue(all(isinstance(v, list) for v in result.values()))
        self.assertTrue(all(c in candidates for group in result.values() for c in group))

    def test_analyze_history(self):
        """测试历史数据分析"""
        result = self.filter.analyze_history(days=30)
        self.assertTrue(all('hit_rate' in stat for stat in result.values()))
        self.assertTrue(all('stability_score' in stat for stat in result.values()))

    def test_get_hot_groups(self):
        """测试获取热门组合"""
        # 先添加一些测试数据
        test_data = [
            ('2025-07-01', '1,11,21,31,41', 12),
            ('2025-07-02', '2,12,22,32,42', 14),
            ('2025-07-03', '3,13,23,33,43', 16)
        ]
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.executemany(
                'INSERT INTO lottery_results VALUES (?, ?, ?)',
                test_data
            )
            conn.commit()

        # 获取热门组合并验证
        hot_groups = self.filter.get_hot_groups(min_hit_rate=0.1)
        self.assertTrue(isinstance(hot_groups, list))
        for group in hot_groups:
            self.assertIn('label', group)
            self.assertIn('hit_rate', group)
            self.assertIn('stability_score', group)
            self.assertIn('prediction_confidence', group)
            self.assertTrue(group['hit_rate'] >= 0.1)

    def test_validate_group(self):
        """测试组合验证"""
        valid_group = {
            "label": "测试组合",
            "tails": [1, 2, 3],
            "type": "basic"
        }
        self.assertTrue(self.filter.validate_group(valid_group))

        invalid_group = {
            "label": "无效组合",
            "tails": [1, 2, 10],  # 尾数不能大于9
            "type": "basic"
        }
        with self.assertRaises(ValueError):
            self.filter.validate_group(invalid_group)

if __name__ == '__main__':
    unittest.main()
