#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析回测分析采用的所有模组
详细展示系统架构和模组调用关系
"""

from prediction_engine import PredictionEngine
from dsl_strategy_parser import DSLStrategyParser
from ml_models import MLModelManager
from optimized_zodiac_engine import OptimizedZodiacEngine
from backtest_engine import BacktestEngine
from data_attributes import DataAttributeMapper
import inspect

def analyze_backtest_modules():
    """分析回测分析模组"""
    print("🔍 分析回测分析采用的所有模组")
    print("="*70)
    
    # 1. 核心预测引擎分析
    print("🎯 1. 核心预测引擎 (PredictionEngine)")
    print("-" * 50)
    
    engine = PredictionEngine()
    
    # 检查预测引擎的组件
    components = {
        'combo_generator': '组合生成器',
        'dsl_parser': 'DSL策略解析器', 
        'ml_manager': '机器学习模型管理器',
        'advanced_zodiac_engine': '高级生肖引擎',
        'backtest_engine': '回测引擎'
    }
    
    for attr, desc in components.items():
        if hasattr(engine, attr):
            component = getattr(engine, attr)
            print(f"   ✅ {desc}: {type(component).__name__}")
        else:
            print(f"   ❌ {desc}: 未找到")
    
    # 2. DSL策略解析器分析
    print(f"\n📋 2. DSL策略解析器 (DSLStrategyParser)")
    print("-" * 50)
    
    dsl_parser = engine.dsl_parser
    strategy_summary = dsl_parser.get_strategy_summary()
    
    print(f"   策略总数: {strategy_summary['total_strategies']}")
    print(f"   激活策略: {strategy_summary['active_strategies']}")
    print(f"   融合方法: {strategy_summary['fusion_method']}")
    
    print(f"\n   📊 策略详情:")
    for strategy in strategy_summary['strategies']:
        status = "✅ 启用" if strategy['active'] else "❌ 禁用"
        print(f"     - {strategy['name']}: {status} (权重: {strategy['weight']})")
        print(f"       类型: {strategy.get('type', 'N/A')}, 描述: {strategy.get('description', 'N/A')}")
    
    # 3. 机器学习模型分析
    print(f"\n🤖 3. 机器学习模型管理器 (MLModelManager)")
    print("-" * 50)
    
    ml_manager = engine.ml_manager
    model_configs = ml_manager.model_configs
    
    print(f"   配置模型数: {len(model_configs)}")
    print(f"   已训练模型: {len(ml_manager.trained_models)}")
    
    print(f"\n   📊 模型配置:")
    for config in model_configs:
        status = "✅ 启用" if config.enabled else "❌ 禁用"
        print(f"     - {config.name}: {status}")
        print(f"       类型: {config.model_type}")
        print(f"       特征: {config.feature_columns}")
        print(f"       参数: {config.params}")
        print()
    
    # 4. 高级生肖引擎分析
    print(f"🧠 4. 高级生肖引擎 (OptimizedZodiacEngine)")
    print("-" * 50)
    
    advanced_engine = engine.advanced_zodiac_engine
    status = advanced_engine.get_system_status()
    
    print(f"   总小组数: {status['total_groups']:,}")
    print(f"   活跃候选: {status['active_candidates']}")
    print(f"   Z-Score阈值: {status['threshold']}")
    print(f"   最后处理期号: {status['last_processed']}")
    print(f"   缓存状态: {'可用' if status['cache_available'] else '不可用'}")
    
    # 5. 回测流程分析
    print(f"\n🔄 5. 回测流程分析")
    print("-" * 50)
    
    print("   回测调用链:")
    print("   PredictionEngine.run_backtest()")
    print("   ├── 加载历史数据 (load_historical_data)")
    print("   ├── 创建回测引擎 (BacktestEngine)")
    print("   ├── 遍历历史数据")
    print("   │   ├── 调用预测 (run_prediction)")
    print("   │   │   ├── DSL策略解析")
    print("   │   │   ├── 机器学习预测")
    print("   │   │   ├── 高级生肖分析")
    print("   │   │   └── 结果融合")
    print("   │   └── 验证预测结果")
    print("   └── 生成回测报告")
    
    # 6. 预测方法详细分析
    print(f"\n🎯 6. 预测方法详细分析 (run_prediction)")
    print("-" * 50)
    
    # 检查run_prediction方法的实现
    prediction_method = getattr(engine, 'run_prediction', None)
    if prediction_method:
        print("   预测流程:")
        print("   1. 数据预处理")
        print("      ├── 期号验证")
        print("      ├── 历史数据加载")
        print("      └── 属性映射 (DataAttributeMapper)")
        
        print("   2. 传统策略分析")
        print("      ├── DSL策略筛选")
        print("      ├── 组合生成 (ComboGenerator)")
        print("      ├── 极限遗漏分析")
        print("      └── 策略评分")
        
        print("   3. 机器学习预测")
        print("      ├── 特征工程")
        print("      ├── 模型预测")
        print("      ├── 概率计算")
        print("      └── 置信度评估")
        
        print("   4. 高级分析")
        print("      ├── Z-Score分析")
        print("      ├── 候选小组筛选")
        print("      ├── 能量分析")
        print("      └── 动态映射")
        
        print("   5. 结果融合")
        print("      ├── 权重分配")
        print("      ├── 号码合并")
        print("      ├── 去重排序")
        print("      └── 置信度计算")
    
    # 7. 数据流分析
    print(f"\n📊 7. 数据流分析")
    print("-" * 50)
    
    print("   数据来源:")
    print("   ├── 历史开奖数据 (HistoryDataManager)")
    print("   ├── 策略配置 (strategy_config.yaml)")
    print("   ├── 模型缓存 (zodiac_cache.pkl)")
    print("   └── 用户输入 (目标期号)")
    
    print("\n   数据处理:")
    print("   ├── 属性映射 (生肖、五行、波色)")
    print("   ├── 特征工程 (时间、统计特征)")
    print("   ├── 策略计算 (遗漏、频率分析)")
    print("   └── 模型推理 (概率预测)")
    
    print("\n   输出结果:")
    print("   ├── 推荐号码列表")
    print("   ├── 策略详情")
    print("   ├── 模型预测")
    print("   ├── 置信度评分")
    print("   └── 分析报告")
    
    # 8. 性能优化分析
    print(f"\n⚡ 8. 性能优化分析")
    print("-" * 50)
    
    print("   优化技术:")
    print("   ├── 缓存机制 (预计算结果)")
    print("   ├── 增量更新 (只处理新数据)")
    print("   ├── 并行计算 (多策略并行)")
    print("   ├── 智能筛选 (阈值过滤)")
    print("   └── 内存优化 (按需加载)")
    
    print("\n   性能指标:")
    print("   ├── 初始化: 秒级")
    print("   ├── 预测: 毫秒级")
    print("   ├── 回测: 分钟级")
    print("   └── 内存: <100MB")

def analyze_module_dependencies():
    """分析模组依赖关系"""
    print(f"\n🔗 模组依赖关系分析")
    print("="*70)
    
    dependencies = {
        'PredictionEngine': [
            'ComboGenerator',
            'DSLStrategyParser', 
            'MLModelManager',
            'OptimizedZodiacEngine',
            'BacktestEngine',
            'DataAttributeMapper'
        ],
        'DSLStrategyParser': [
            'StrategyConfig',
            'FusionConfig',
            'YAML配置文件'
        ],
        'MLModelManager': [
            'FeatureEngineer',
            'RandomForestClassifier',
            'GradientBoostingClassifier',
            'KNeighborsClassifier',
            'SVC',
            'StandardScaler',
            'PCA'
        ],
        'OptimizedZodiacEngine': [
            'DynamicMappingSystem',
            'ZodiacGroup',
            'Pickle缓存'
        ],
        'BacktestEngine': [
            'StrategyResult',
            'BacktestResult',
            '历史数据'
        ]
    }
    
    for module, deps in dependencies.items():
        print(f"\n📦 {module}:")
        for dep in deps:
            print(f"   └── {dep}")

def show_integration_summary():
    """显示集成总结"""
    print(f"\n📋 系统集成总结")
    print("="*70)
    
    summary = """
🎯 核心架构:
   ├── 统一预测引擎 (PredictionEngine) - 总控制器
   ├── 多策略分析层 - 传统策略 + 机器学习 + 高级分析
   ├── 数据处理层 - 历史数据 + 属性映射 + 特征工程
   └── 结果输出层 - 预测结果 + 回测报告 + 可视化

🔄 回测流程:
   1. 数据准备 → 2. 策略执行 → 3. 结果验证 → 4. 性能评估

📊 分析维度:
   ├── 传统分析: 生肖组合、五行平衡、波色分布
   ├── 统计分析: Z-Score异常检测、遗漏分析
   ├── 机器学习: 随机森林、梯度提升、KNN、SVM
   └── 高级分析: 动态映射、能量分析、候选排行

⚡ 性能特点:
   ├── 高速响应: 毫秒级预测
   ├── 大规模处理: 103,950个小组并行分析
   ├── 智能缓存: 增量更新机制
   └── 可扩展性: 模块化设计

🎲 输出结果:
   ├── 14个推荐号码
   ├── 多维度分析报告
   ├── 置信度评估
   └── 历史回测验证
"""
    
    print(summary)

if __name__ == "__main__":
    # 运行完整分析
    analyze_backtest_modules()
    
    # 分析依赖关系
    analyze_module_dependencies()
    
    # 显示集成总结
    show_integration_summary()
    
    print(f"\n🎯 分析完成!")
    print("系统采用多层次、多维度的综合分析架构")
