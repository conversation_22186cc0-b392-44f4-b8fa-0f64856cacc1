#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复高级分析数据问题
强制重建缓存并处理历史数据
"""

import os
from optimized_zodiac_engine import OptimizedZodiacEngine
from prediction_engine import PredictionEngine

def fix_advanced_analysis():
    """修复高级分析数据问题"""
    print("🔧 修复高级分析数据问题")
    print("="*60)
    
    # 删除旧缓存
    cache_file = "zodiac_cache.pkl"
    if os.path.exists(cache_file):
        print(f"🗑️ 删除旧缓存文件: {cache_file}")
        os.remove(cache_file)
    
    # 重新初始化引擎
    print("📊 重新初始化高级生肖引擎...")
    engine = OptimizedZodiacEngine(cache_file)
    
    # 加载历史数据
    print("📈 加载历史数据...")
    pred_engine = PredictionEngine()
    history_data = pred_engine.load_historical_data()
    
    print(f"✅ 加载了 {len(history_data)} 期历史数据")
    
    if not history_data:
        print("❌ 无历史数据，无法进行分析")
        return
    
    # 强制完全重建
    print("🔄 强制完全重建缓存...")
    engine.force_full_rebuild(history_data)
    
    # 验证修复结果
    print("\n🔍 验证修复结果...")
    
    # 检查小组状态
    groups_with_history = 0
    groups_with_zscore = 0
    max_zscore = 0
    sample_groups = []
    
    for group_id, group in list(engine.all_groups.items())[:10]:
        if len(group.miss_history) > 0:
            groups_with_history += 1
            
        if group.z_score > 0:
            groups_with_zscore += 1
            max_zscore = max(max_zscore, group.z_score)
        
        sample_groups.append({
            'id': group_id,
            'members': group.members,
            'history_count': len(group.miss_history),
            'current_miss': group.current_miss,
            'z_score': group.z_score,
            'hit_count': group.hit_count
        })
    
    print(f"   有历史数据的小组: {groups_with_history} / 10 (样本)")
    print(f"   有Z-Score的小组: {groups_with_zscore} / 10 (样本)")
    print(f"   最大Z-Score: {max_zscore:.3f}")
    
    # 显示样本小组
    print(f"\n📋 样本小组状态:")
    for group in sample_groups:
        print(f"   {group['id']}: {group['members']}")
        print(f"     历史数据: {group['history_count']} 期")
        print(f"     当前遗漏: {group['current_miss']} 期")
        print(f"     Z-Score: {group['z_score']:.3f}")
        print(f"     命中次数: {group['hit_count']}")
        print()
    
    # 测试候选查找
    print("🔍 测试候选查找...")
    candidates = engine.find_candidates_fast(1.0)  # 降低阈值
    print(f"   阈值1.0的候选数: {len(candidates)}")
    
    if candidates:
        print(f"   前3个候选:")
        for i, candidate in enumerate(candidates[:3], 1):
            members_str = ", ".join(candidate['members'])
            print(f"     {i}. {members_str} - Z-Score: {candidate['z_score']:.3f}")
    
    # 再次测试更低阈值
    candidates_low = engine.find_candidates_fast(0.1)
    print(f"   阈值0.1的候选数: {len(candidates_low)}")
    
    # 测试预测引擎集成
    print(f"\n🧠 测试预测引擎集成...")
    pred_engine.advanced_zodiac_engine = engine  # 更新引擎
    
    analysis_result = pred_engine.get_advanced_zodiac_analysis()
    
    if 'error' in analysis_result:
        print(f"❌ 集成测试失败: {analysis_result['error']}")
    else:
        candidates_count = len(analysis_result.get('candidates', []))
        recommendations_count = len(analysis_result.get('top_recommendations', []))
        energy_count = len(analysis_result.get('energy_analysis', {}))
        
        print(f"✅ 集成测试成功:")
        print(f"   候选小组: {candidates_count}")
        print(f"   推荐号码: {recommendations_count}")
        print(f"   能量分析: {energy_count} 个生肖")
    
    print(f"\n🎯 修复完成!")
    return engine

if __name__ == "__main__":
    fix_advanced_analysis()
