from typing import List, Dict, Set, Any
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import csv
import os
import shutil

class TailGroupFilter:
    """
    Tail number combination filter engine.
    Generates and analyzes number combination patterns.
    """
    def __init__(self, db_path: str, config_file: str):
        """
        Initialize tail filter
        Args:
            db_path: Database path
            config_file: Config file path
        """
        self.db_path = db_path
        self.config_file = config_file
        self._init_database()
        self.config = self._load_config()
        self.cache = {}

    def _init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_results (
                    draw_date TEXT,
                    numbers TEXT,
                    special_number INTEGER
                )
            ''')
            conn.commit()

    def _load_config(self) -> dict:
        """Load configuration file"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def reload_config(self):
        """Reload configuration file"""
        self.config = self._load_config()

    def get_numbers_by_tail(self, tails: Set[int]) -> Set[int]:
        """Get numbers matching given tail digits"""
        numbers = set()
        for i in range(1, 50):
            if i % 10 in tails:
                numbers.add(i)
        return numbers

    def filter_candidates(self, candidates: List[int]) -> Dict[str, List[int]]:
        """Filter candidate numbers by tail combinations"""
        result = {}
        for group in self.config['groups']:
            group_numbers = []
            group_tails = set(group['tails'])
            for num in candidates:
                if num % 10 in group_tails:
                    group_numbers.append(num)
            result[group['label']] = group_numbers
        return result

    def analyze_history(self, days: int = 30) -> Dict[str, Dict]:
        """Analyze historical data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            date_limit = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            cursor.execute('''
                SELECT numbers FROM lottery_results
                WHERE draw_date >= ?
                ORDER BY draw_date DESC
            ''', (date_limit,))

            results = cursor.fetchall()

        stats = {}
        for group in self.config['groups']:
            hits = 0
            total_matches = 0
            group_tails = set(group['tails'])

            for row in results:
                numbers = [int(x) for x in row[0].split(',')]
                matched = sum(1 for n in numbers if n % 10 in group_tails)
                if matched > 0:
                    hits += 1
                total_matches += matched

            if results:
                hit_rate = hits / len(results)
                stability = total_matches / (len(results) * len(group_tails))
            else:
                hit_rate = stability = 0

            stats[group['label']] = {
                'hit_rate': hit_rate,
                'stability_score': stability
            }

        return stats

    def get_hot_groups(self, top_n: int = 5) -> List[Dict]:
        """Get hot tail combinations"""
        stats = self.analyze_history()
        sorted_groups = sorted(
            [{'label': k, **v} for k, v in stats.items()],
            key=lambda x: x['hit_rate'],
            reverse=True
        )
        return sorted_groups[:top_n]

    def validate_group(self, group: Dict) -> bool:
        """Validate group configuration"""
        if not all(field in group for field in ['label', 'tails', 'type']):
            raise ValueError("Missing required fields")

        if not all(isinstance(t, int) and 0 <= t <= 9 for t in group['tails']):
            raise ValueError("Tails must be integers between 0 and 9")

        if not group['type'] in {'basic', 'advanced'}:
            raise ValueError("Type must be 'basic' or 'advanced'")

        return True

    def import_from_csv(self, csv_path: str) -> int:
        """Import data from CSV file"""
        imported_count = 0
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    numbers = row['numbers'].replace(';', ',')
                    cursor.execute(
                        'INSERT INTO lottery_results VALUES (?, ?, ?)',
                        (row['draw_date'], numbers, int(row['special_number']))
                    )
                    imported_count += 1
            conn.commit()
        return imported_count

    def export_analysis(self, analysis: Dict[str, Any], output_file: str):
        """Export analysis results"""
        export_data = {
            'analysis_date': datetime.now().isoformat(),
            'groups': analysis
        }
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

    def create_backup(self) -> str:
        """Create database backup"""
        backup_dir = Path(self.db_path).parent / 'backups'
        backup_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = backup_dir / f'lottery_data_{timestamp}.db'

        shutil.copy2(self.db_path, backup_path)
        return str(backup_path)

    def restore_from_backup(self, backup_path: str):
        """Restore from backup"""
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup file not found: {backup_path}")

        shutil.copy2(backup_path, self.db_path)
