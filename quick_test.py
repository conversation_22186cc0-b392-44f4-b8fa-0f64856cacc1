#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速功能测试脚本
验证核心功能是否正常工作
"""

import json
from datetime import datetime

def test_core_functions():
    """测试核心功能"""
    print("🚀 快速功能测试开始...")
    print("=" * 50)
    
    # 1. 测试DSL策略解析器
    print("\n1. 测试DSL策略解析器")
    try:
        from dsl_strategy_parser import DSLStrategyParser
        parser = DSLStrategyParser()
        summary = parser.get_strategy_summary()
        print(f"✅ DSL解析器正常 - {summary['total_strategies']}个策略")
    except Exception as e:
        print(f"❌ DSL解析器错误: {e}")
        return False
    
    # 2. 测试统一预测引擎
    print("\n2. 测试统一预测引擎")
    try:
        from prediction_engine import PredictionEngine
        engine = PredictionEngine()
        
        # 运行预测
        prediction_result = engine.run_prediction("2025200")
        print(f"✅ 预测引擎正常")
        print(f"   推荐号码: {prediction_result.final_numbers}")
        print(f"   置信度: {prediction_result.confidence_score:.4f}")

        # 测试导出功能
        json_file = engine.export_prediction(prediction_result, 'json', 'test_prediction')
        print(f"   JSON导出: {json_file}")
        
    except Exception as e:
        print(f"❌ 预测引擎错误: {e}")
        return False
    
    # 3. 测试回测引擎
    print("\n3. 测试回测引擎")
    try:
        from backtest_engine import BacktestEngine
        
        # 模拟数据
        test_history = [
            {'period': '2025190', 'special_code': 25},
            {'period': '2025191', 'special_code': 13},
            {'period': '2025192', 'special_code': 8}
        ]
        
        test_predictions = [
            {'period': '2025190', 'numbers': [25, 13, 8, 35, 42]},
            {'period': '2025191', 'numbers': [13, 25, 8, 35, 42]},
            {'period': '2025192', 'numbers': [8, 25, 13, 35, 42]}
        ]
        
        backtest = BacktestEngine(test_history)
        result = backtest.run_strategy_backtest('test', '测试策略', test_predictions)
        
        print(f"✅ 回测引擎正常")
        print(f"   命中率: {result.hit_rate:.2%}")
        print(f"   命中次数: {result.hit_count}/{result.total_periods}")
        
    except Exception as e:
        print(f"❌ 回测引擎错误: {e}")
        return False
    
    # 4. 测试报告生成器
    print("\n4. 测试报告生成器")
    try:
        from report_generator import ReportGenerator
        generator = ReportGenerator()

        # 生成预测报告（使用预测引擎的结果）
        report_file = generator.generate_prediction_report(prediction_result)
        print(f"✅ 报告生成器正常")
        print(f"   报告文件: {report_file}")

    except Exception as e:
        print(f"❌ 报告生成器错误: {e}")
        return False
    
    # 5. 测试原有模块兼容性
    print("\n5. 测试原有模块兼容性")
    try:
        from combo_generator import ComboGenerator
        from extreme_stat_tracker import ExtremeStatTracker
        from strategy_scorer import StrategyScorerAndFusionEngine
        
        combo_gen = ComboGenerator()
        combos = combo_gen.generate_shengxiao_4()
        print(f"✅ 原有模块正常")
        print(f"   生肖4组合数: {len(combos)}")
        
    except Exception as e:
        print(f"❌ 原有模块错误: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 快速功能测试全部通过！")
    
    # 显示系统状态
    print("\n📊 系统状态总结:")
    print("✅ DSL策略解析器 - 正常运行")
    print("✅ 统一预测引擎 - 正常运行")
    print("✅ 回测评估系统 - 正常运行")
    print("✅ 报告生成器 - 正常运行")
    print("✅ 原有模块兼容 - 正常运行")
    
    print("\n🎯 系统已准备就绪，可以投入使用！")
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("=" * 50)
    
    examples = [
        "# 运行预测",
        "from prediction_engine import PredictionEngine",
        "engine = PredictionEngine()",
        "result = engine.run_prediction('2025201')",
        "print(f'推荐号码: {result.final_numbers}')",
        "",
        "# 运行回测",
        "backtest_result = engine.run_backtest()",
        "print(f'命中率: {backtest_result[\"hit_rate\"]:.2%}')",
        "",
        "# 生成报告",
        "from report_generator import ReportGenerator",
        "generator = ReportGenerator()",
        "report_file = generator.generate_prediction_report(result)",
        "",
        "# 配置策略",
        "from dsl_strategy_parser import DSLStrategyParser",
        "parser = DSLStrategyParser()",
        "parser.update_strategy_weight('shengxiao_4_extreme', 0.9)",
        "parser.save_config()"
    ]
    
    for line in examples:
        print(line)

if __name__ == "__main__":
    print("🎲 六合彩智能预测系统 - 快速功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_core_functions()
    
    if success:
        show_usage_examples()
    
    print(f"\n测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
