import sys
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QDialog, QVBoxLayout, QTextEdit, QPushButton
from prediction_manager import PredictionManager

class ContextViewer(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('预测系统上下文分析')
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout()
        
        # 创建文本显示区域
        self.text_area = QTextEdit()
        self.text_area.setReadOnly(True)
        layout.addWidget(self.text_area)
        
        # 创建刷新按钮
        refresh_btn = QPushButton('刷新统计')
        refresh_btn.clicked.connect(self.refresh_stats)
        layout.addWidget(refresh_btn)
        
        self.setLayout(layout)
        
        # 显示初始统计
        self.refresh_stats()
        
    def refresh_stats(self):
        # 获取统计信息
        manager = PredictionManager()
        report = manager.context_tracker.format_context_report()
        self.text_area.setText(report)

def main():
    app = QApplication(sys.argv)
    viewer = ContextViewer()
    viewer.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
