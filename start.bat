@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 设置标题
title 尾数过滤分析系统启动器

:: 显示启动信息
echo ==================================
echo    尾数过滤分析系统启动器
echo ==================================
echo.

:: 检查Python是否安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python环境
    echo 请安装Python 3.8或更高版本
    echo 您可以从 https://www.python.org/downloads/ 下载
    pause
    exit /b 1
)

:: 检查pip是否可用
pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到pip包管理器
    echo 请确保Python安装包含pip
    pause
    exit /b 1
)

:: 显示环境信息
echo [信息] 检测到Python环境:
python --version
echo.

:: 检查并创建虚拟环境
if not exist "venv" (
    echo [信息] 正在创建虚拟环境...
    python -m venv venv
    if !errorlevel! neq 0 (
        echo [错误] 创建虚拟环境失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [错误] 激活虚拟环境失败
    pause
    exit /b 1
)

:: 安装依赖
echo [信息] 正在检查依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [警告] 部分依赖可能安装失败，但将继续尝试启动程序
)

:: 检查数据库
if not exist "lottery_data.db" (
    echo [信息] 初始化数据库...
    python -c "from tail_group_filter import TailGroupFilter; TailGroupFilter('lottery_data.db', 'config.json')"
)

:: 启动主程序
echo.
echo [信息] 正在启动系统...
echo.
python gui_main.py
if %errorlevel% neq 0 (
    echo.
    echo [错误] 程序运行出错，错误代码: %errorlevel%
    echo 请查看上方错误信息
    pause
    exit /b 1
)

:: 退出
echo.
echo [信息] 程序已结束
deactivate
pause
