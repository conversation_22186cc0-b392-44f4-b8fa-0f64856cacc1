#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断多维生肖预测号码生成问题
"""

from multi_dimensional_zodiac_predictor import MultiDimensionalZodiacPredictor
from dynamic_mapping_system import DynamicMappingSystem
import traceback

def diagnose_number_generation():
    """诊断号码生成问题"""
    print("🔍 诊断多维生肖预测号码生成问题")
    print("="*60)
    
    try:
        # 1. 创建预测器
        predictor = MultiDimensionalZodiacPredictor()
        
        # 2. 运行预测并跟踪过程
        print("📊 运行预测并跟踪过程...")
        result = predictor.predict_next_period("2025199")
        
        print(f"✅ 预测完成")
        print(f"📊 预测号码: {result['predicted_numbers']}")
        print(f"📊 置信度: {result['confidence_score']:.1%}")
        
        # 3. 分析预测生肖
        print(f"\n🐲 分析预测生肖:")
        final_prediction = result['final_prediction']
        top_zodiacs = final_prediction['top_zodiacs']
        
        print(f"   前6个生肖:")
        for i, (zodiac, score) in enumerate(top_zodiacs, 1):
            print(f"     {i}. {zodiac}: {score:.3f}")
        
        # 4. 检查生肖到号码的映射
        print(f"\n🔗 检查生肖到号码的映射:")
        mapping_system = DynamicMappingSystem()
        
        for zodiac, score in top_zodiacs[:4]:  # 检查前4个生肖
            print(f"\n   生肖: {zodiac} (评分: {score:.3f})")
            
            try:
                # 检查映射系统是否有get_numbers_for_zodiac方法
                if hasattr(mapping_system, 'get_numbers_for_zodiac'):
                    numbers = mapping_system.get_numbers_for_zodiac(zodiac, "2025199")
                    print(f"     对应号码: {numbers}")
                else:
                    print(f"     ❌ mapping_system没有get_numbers_for_zodiac方法")
                    
                    # 尝试其他方法
                    if hasattr(mapping_system, 'get_zodiac_for_number'):
                        print(f"     🔍 尝试反向查找...")
                        zodiac_numbers = []
                        for num in range(1, 50):
                            try:
                                num_zodiac = mapping_system.get_zodiac_for_number(num, "2025199")
                                if num_zodiac == zodiac:
                                    zodiac_numbers.append(num)
                            except:
                                continue
                        
                        print(f"     反向查找结果: {zodiac_numbers[:10]}...")  # 显示前10个
                    else:
                        print(f"     ❌ mapping_system没有相关方法")
                        
            except Exception as e:
                print(f"     ❌ 映射失败: {e}")
        
        # 5. 检查号码生成逻辑
        print(f"\n🔧 检查号码生成逻辑:")
        
        # 查看_generate_numbers_from_zodiacs方法的实现
        import inspect
        source = inspect.getsource(predictor._generate_numbers_from_zodiacs)
        
        print(f"   _generate_numbers_from_zodiacs方法源码:")
        lines = source.split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip():
                print(f"     {i:2d}: {line}")
        
        return result
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        traceback.print_exc()
        return None

def test_mapping_system():
    """测试映射系统"""
    print(f"\n🧪 测试映射系统")
    print("="*60)
    
    try:
        mapping_system = DynamicMappingSystem()
        
        # 检查可用方法
        print(f"📋 映射系统可用方法:")
        methods = [method for method in dir(mapping_system) if not method.startswith('_')]
        for method in methods:
            print(f"   - {method}")
        
        # 测试正向映射
        print(f"\n🔍 测试正向映射 (号码→生肖):")
        test_numbers = [1, 2, 3, 4, 5, 6, 7, 8]
        
        for num in test_numbers:
            try:
                zodiac = mapping_system.get_zodiac_for_number(num, "2025199")
                print(f"   号码{num:2d} → {zodiac}")
            except Exception as e:
                print(f"   号码{num:2d} → ❌ {e}")
        
        # 测试反向映射
        print(f"\n🔄 测试反向映射 (生肖→号码):")
        test_zodiacs = ["鼠", "牛", "虎", "兔", "龙", "蛇"]
        
        for zodiac in test_zodiacs:
            print(f"   生肖{zodiac}:")
            zodiac_numbers = []
            
            for num in range(1, 50):
                try:
                    num_zodiac = mapping_system.get_zodiac_for_number(num, "2025199")
                    if num_zodiac == zodiac:
                        zodiac_numbers.append(num)
                except:
                    continue
            
            print(f"     对应号码: {zodiac_numbers}")
        
    except Exception as e:
        print(f"❌ 映射系统测试失败: {e}")

def analyze_problem_source():
    """分析问题根源"""
    print(f"\n🎯 分析问题根源")
    print("="*60)
    
    print("🔍 可能的问题原因:")
    
    print("\n1. 📊 生肖映射问题:")
    print("   - mapping_system可能没有get_numbers_for_zodiac方法")
    print("   - 反向查找效率低且可能不准确")
    print("   - 生肖到号码的映射逻辑有问题")
    
    print("\n2. 🔧 号码生成逻辑问题:")
    print("   - _generate_numbers_from_zodiacs方法实现有缺陷")
    print("   - 可能使用了固定的补充号码逻辑")
    print("   - 没有正确处理生肖映射失败的情况")
    
    print("\n3. 🎲 备用号码问题:")
    print("   - 当生肖映射失败时，可能使用了range(1,50)的前8个")
    print("   - 这解释了为什么得到[1,2,3,4,5,6,7,8]")
    
    print("\n4. 🔄 算法逻辑问题:")
    print("   - 多维生肖分析可能正确")
    print("   - 但最后的号码生成环节有问题")
    print("   - 需要重新设计生肖到号码的转换逻辑")

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案")
    print("="*60)
    
    solutions = """
🔧 解决方案:

1. 🏗️ 修复映射系统:
   - 为DynamicMappingSystem添加get_numbers_for_zodiac方法
   - 实现高效的生肖→号码映射
   - 确保映射的准确性和完整性

2. 🎯 重新设计号码生成:
   - 修改_generate_numbers_from_zodiacs方法
   - 使用正确的生肖映射逻辑
   - 添加错误处理和备用方案

3. 📊 优化生肖分布:
   - 确保每个生肖有合理数量的号码
   - 平衡不同生肖的号码分布
   - 避免某些生肖号码过少

4. 🧪 增强测试验证:
   - 添加生肖映射的单元测试
   - 验证号码生成的合理性
   - 确保预测结果的多样性

5. 🎲 改进备用机制:
   - 当映射失败时，使用智能备用号码
   - 基于历史统计选择备用号码
   - 避免使用连续号码作为备用
"""
    
    print(solutions)

def main():
    """主诊断流程"""
    print("🔍 多维生肖预测号码生成问题诊断")
    print("="*70)
    
    # 诊断号码生成
    result = diagnose_number_generation()
    
    # 测试映射系统
    test_mapping_system()
    
    # 分析问题根源
    analyze_problem_source()
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n🎯 诊断结论:")
    if result and result['predicted_numbers'] == [1, 2, 3, 4, 5, 6, 7, 8]:
        print("❌ 确认问题：预测号码为连续的1-8")
        print("🔍 根本原因：生肖到号码的映射逻辑有问题")
        print("💡 需要修复映射系统和号码生成逻辑")
    else:
        print("✅ 问题可能已解决或需要进一步调查")

if __name__ == "__main__":
    main()
