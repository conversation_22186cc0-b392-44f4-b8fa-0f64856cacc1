#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI所有功能 - 确保修复后的GUI所有功能正常工作
"""

from prediction_engine_adapter import PredictionEngineAdapter
import traceback

def test_all_functions():
    """测试所有功能"""
    print("🧪 测试修复后的GUI所有功能")
    print("=" * 50)
    
    try:
        # 初始化适配器
        print("\n1️⃣ 初始化适配器...")
        adapter = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        # 测试基本预测
        print("\n2️⃣ 测试基本预测功能...")
        result = adapter.run_prediction("2025210")
        print(f"✅ 预测成功: {len(result.final_numbers)}个号码")
        print(f"   号码: {result.final_numbers}")
        print(f"   置信度: {result.confidence_score:.2%}")
        print(f"   策略数: {result.total_strategies_used}")
        
        # 测试预测摘要
        print("\n3️⃣ 测试预测摘要...")
        summary = adapter.get_prediction_summary()
        print(f"✅ 摘要获取成功")
        print(f"   可用策略: {summary['available_strategies']}")
        print(f"   训练模型: {summary['trained_models']}")
        print(f"   系统状态: {summary['system_status']}")
        
        # 测试历史数据加载
        print("\n4️⃣ 测试历史数据加载...")
        history = adapter.load_historical_data()
        print(f"✅ 历史数据加载成功: {len(history)}条记录")
        
        # 测试高级生肖分析
        print("\n5️⃣ 测试高级生肖分析...")
        analysis = adapter.get_advanced_zodiac_analysis()
        if 'error' in analysis:
            print(f"❌ 高级分析失败: {analysis['error']}")
        else:
            print(f"✅ 高级分析成功")
            print(f"   候选组合: {len(analysis['candidates'])}个")
            print(f"   推荐组合: {len(analysis['top_recommendations'])}个")
            print(f"   系统状态: {analysis['system_status']['status']}")
        
        # 测试回测功能
        print("\n6️⃣ 测试回测功能...")
        backtest = adapter.run_backtest()
        print(f"✅ 回测成功")
        print(f"   命中率: {backtest.hit_rate:.2%}")
        print(f"   总预测: {backtest.total_predictions}")
        print(f"   总命中: {backtest.total_hits}")
        
        # 测试模型训练
        print("\n7️⃣ 测试模型训练...")
        models = adapter.train_models(history[:100])
        print(f"✅ 模型训练完成: {len(models)}个模型")
        
        # 测试导出功能
        print("\n8️⃣ 测试导出功能...")
        json_file = adapter.export_prediction(result, 'json', 'test_export')
        csv_file = adapter.export_prediction(result, 'csv', 'test_export')
        print(f"✅ 导出成功")
        print(f"   JSON文件: {json_file}")
        print(f"   CSV文件: {csv_file}")
        
        # 测试高级报告
        print("\n9️⃣ 测试高级报告生成...")
        report = adapter.generate_advanced_report()
        print(f"✅ 报告生成成功: {len(report)}字符")
        
        print(f"\n🎉 所有功能测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print(f"详细错误:")
        traceback.print_exc()
        return False

def test_gui_compatibility():
    """测试GUI兼容性"""
    print(f"\n🔧 测试GUI兼容性...")
    
    try:
        # 测试GUI期望的属性和方法
        adapter = PredictionEngineAdapter()
        
        # 检查必要的属性
        print("检查必要属性...")
        assert hasattr(adapter, 'dsl_parser'), "缺少dsl_parser属性"
        assert hasattr(adapter, 'advanced_zodiac_engine'), "缺少advanced_zodiac_engine属性"
        print("✅ 属性检查通过")
        
        # 检查必要的方法
        print("检查必要方法...")
        methods = [
            'run_prediction', 'get_prediction_summary', 'load_historical_data',
            'run_backtest', 'train_models', 'export_prediction',
            'get_advanced_zodiac_analysis', 'generate_advanced_report'
        ]
        
        for method in methods:
            assert hasattr(adapter, method), f"缺少{method}方法"
            assert callable(getattr(adapter, method)), f"{method}不可调用"
        
        print("✅ 方法检查通过")
        
        # 检查高级引擎属性
        print("检查高级引擎...")
        assert hasattr(adapter.advanced_zodiac_engine, 'z_score_threshold'), "缺少z_score_threshold"
        assert hasattr(adapter.advanced_zodiac_engine, 'dynamic_mapper'), "缺少dynamic_mapper"
        print("✅ 高级引擎检查通过")
        
        print("✅ GUI兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI兼容性测试失败: {e}")
        return False

def test_prediction_variability():
    """测试预测变化性"""
    print(f"\n🎲 测试预测变化性...")
    
    try:
        adapter = PredictionEngineAdapter()
        results = []
        
        # 进行5次预测
        for i in range(5):
            period = f"202521{i}"
            result = adapter.run_prediction(period)
            results.append({
                'period': period,
                'numbers': set(result.final_numbers),
                'confidence': result.confidence_score
            })
            print(f"   预测{i+1}: {len(result.final_numbers)}个号码, 置信度{result.confidence_score:.2%}")
        
        # 检查变化性
        all_same = True
        first_numbers = results[0]['numbers']
        
        for i, result in enumerate(results[1:], 1):
            overlap = first_numbers & result['numbers']
            overlap_rate = len(overlap) / len(first_numbers) * 100
            print(f"   预测{i+1}与预测1重叠率: {overlap_rate:.1f}%")
            
            if overlap_rate < 80:
                all_same = False
        
        if all_same:
            print("❌ 预测结果变化性不足")
            return False
        else:
            print("✅ 预测结果具有良好的变化性")
            return True
            
    except Exception as e:
        print(f"❌ 变化性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 启动GUI功能完整性测试...")
    
    # 运行所有测试
    tests = [
        ("基本功能测试", test_all_functions),
        ("GUI兼容性测试", test_gui_compatibility),
        ("预测变化性测试", test_prediction_variability)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        success = test_func()
        results.append((test_name, success))
    
    # 总结结果
    print(f"\n{'='*60}")
    print(f"📋 测试结果总结")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print(f"\n🎉 恭喜！修复后的GUI所有功能正常工作")
        print(f"✅ 可以安全使用原GUI界面")
        print(f"✅ 所有功能都使用真实预测引擎")
        print(f"✅ 高级分析功能已修复")
        print(f"✅ 预测结果具有良好的变化性")
    else:
        print(f"\n⚠️ 发现问题，需要进一步修复")
    
    return all_passed

if __name__ == "__main__":
    main()
