#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三层筛选系统 - 完整功能验证
"""

def test_three_layer_filter_engine():
    """测试三层筛选引擎"""
    print("🧪 测试三层筛选引擎")
    print("=" * 60)
    
    try:
        from three_layer_filter_engine import ThreeLayerFilterEngine
        
        engine = ThreeLayerFilterEngine()
        result = engine.predict("2025210")
        
        print(f"✅ 三层筛选引擎测试成功")
        print(f"   推荐号码: {result.numbers}")
        print(f"   置信度: {result.confidence:.2%}")
        print(f"   号码数量: {len(result.numbers)}")
        
        # 验证结果
        if 12 <= len(result.numbers) <= 16:
            print("   ✅ 号码数量符合要求")
        else:
            print("   ❌ 号码数量不符合要求")
            return False
        
        if all(1 <= num <= 49 for num in result.numbers):
            print("   ✅ 号码范围正确")
        else:
            print("   ❌ 号码范围错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 三层筛选引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extreme_tracker():
    """测试极限追踪系统"""
    print("\n🧪 测试极限追踪系统")
    print("-" * 40)
    
    try:
        from extreme_tracker_backtest import ExtremeStatTracker
        
        tracker = ExtremeStatTracker("test_extreme.db")
        
        # 注册测试组合
        tracker.register_combination("test_zodiac", "zodiac", "龙")
        tracker.register_combination("test_color", "color", "红")
        
        # 更新测试数据
        tracker.update_period_result("2025210", 12)
        tracker.update_period_result("2025211", 25)
        
        # 获取极限状态
        status = tracker.get_extreme_status("test_zodiac")
        
        print(f"✅ 极限追踪系统测试成功")
        print(f"   测试组合状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 极限追踪系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_system():
    """测试集成系统"""
    print("\n🧪 测试集成系统")
    print("-" * 40)
    
    try:
        from three_layer_system_integration import ThreeLayerIntegratedSystem
        
        system = ThreeLayerIntegratedSystem()
        result = system.predict("2025210")
        
        print(f"✅ 集成系统测试成功")
        print(f"   推荐号码: {result.recommended_numbers}")
        print(f"   置信度: {result.confidence:.2%}")
        print(f"   极限分析: {len(result.extreme_analysis.get('critical_combinations', []))} 个危险组合")
        
        # 生成报告
        report = system.generate_comprehensive_report(result)
        print(f"   报告长度: {len(report)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adapter_integration():
    """测试适配器集成"""
    print("\n🧪 测试适配器集成")
    print("-" * 40)
    
    try:
        from prediction_engine_adapter import PredictionEngineAdapter
        
        adapter = PredictionEngineAdapter()
        
        # 测试标准预测
        print("   测试标准预测...")
        standard_result = adapter.run_prediction("2025210")
        print(f"   标准预测: {len(standard_result.final_numbers)} 个号码")
        
        # 测试三层筛选预测
        print("   测试三层筛选预测...")
        three_layer_result = adapter.run_three_layer_prediction("2025210")
        print(f"   三层筛选: {len(three_layer_result.final_numbers)} 个号码")
        
        print(f"✅ 适配器集成测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 适配器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consistency():
    """测试一致性"""
    print("\n🧪 测试预测一致性")
    print("-" * 40)
    
    try:
        from prediction_engine_adapter import PredictionEngineAdapter
        
        adapter = PredictionEngineAdapter()
        
        # 测试同一期号多次预测的一致性
        period = "2025210"
        results = []
        
        for i in range(3):
            if hasattr(adapter, 'run_three_layer_prediction'):
                result = adapter.run_three_layer_prediction(period)
            else:
                result = adapter.run_prediction(period)
            results.append(result.final_numbers)
        
        # 检查一致性
        first_result = results[0]
        all_consistent = all(result == first_result for result in results[1:])
        
        if all_consistent:
            print(f"✅ 预测一致性测试通过")
            print(f"   期号 {period} 的预测结果完全一致")
            print(f"   推荐号码: {first_result}")
        else:
            print(f"❌ 预测一致性测试失败")
            for i, result in enumerate(results):
                print(f"   第{i+1}次: {result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🧪 测试配置加载")
    print("-" * 40)
    
    try:
        import yaml
        
        # 测试配置文件是否存在和可读
        config_file = "三层筛选策略配置.yaml"
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ 配置文件加载成功")
        print(f"   策略名称: {config.get('strategy_name', 'Unknown')}")
        print(f"   版本: {config.get('version', 'Unknown')}")
        
        # 验证必要的配置项
        required_keys = ['base_pool', 'output_control', 'layer1_big_filter', 'layer2_medium_filter', 'layer3_fine_filter']
        
        for key in required_keys:
            if key in config:
                print(f"   ✅ {key} 配置存在")
            else:
                print(f"   ❌ {key} 配置缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动三层筛选系统完整测试...")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("配置文件加载", test_config_loading),
        ("三层筛选引擎", test_three_layer_filter_engine),
        ("极限追踪系统", test_extreme_tracker),
        ("集成系统", test_integrated_system),
        ("适配器集成", test_adapter_integration),
        ("预测一致性", test_consistency)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        success = test_func()
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 总结结果
    print("\n" + "="*80)
    print("📋 测试结果总结")
    print("="*80)
    
    passed_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed_count}/{total_count} 测试通过")
    
    if passed_count == total_count:
        print("\n🎉 恭喜！三层筛选系统完全正常")
        print("✅ 所有组件都工作正常")
        print("✅ 系统集成成功")
        print("✅ 预测一致性良好")
        
        print("\n💡 现在可以使用:")
        print("   - 标准预测: adapter.run_prediction(period)")
        print("   - 三层筛选: adapter.run_three_layer_prediction(period)")
        print("   - 集成系统: system.predict(period)")
        print("   - 极限追踪: tracker.get_extreme_status(combination)")
        
        print("\n🎯 三层筛选特点:")
        print("   - 大筛子: 多维策略组(生肖+号码+波色+五行)")
        print("   - 中筛子: 48码四组分布筛选")
        print("   - 小筛子: 交叉特征+极限追踪+平衡反向")
        print("   - 输出: 精选12-16个特码")
        
    else:
        print(f"\n⚠️ 有 {total_count - passed_count} 个测试失败")
        print("请检查上述错误信息并进行修复")
    
    return passed_count == total_count

if __name__ == "__main__":
    main()
