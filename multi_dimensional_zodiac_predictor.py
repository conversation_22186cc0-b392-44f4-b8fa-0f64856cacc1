#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多维生肖预测策略
"""

from multi_dimensional_zodiac_analyzer import MultiDimensionalZodiacAnalyzer
from dynamic_mapping_system import DynamicMappingSystem
from typing import Dict, List, Any, Tuple
from collections import defaultdict

class MultiDimensionalZodiacPredictor:
    """多维生肖预测器"""
    
    def __init__(self):
        self.analyzer = MultiDimensionalZodiacAnalyzer()
        self.mapping_system = DynamicMappingSystem()
        
        # 调整预测参数
        self.analyzer.extreme_threshold = 0.6  # 降低极限阈值
        self.analyzer.reback_threshold = 0.5   # 降低回补阈值
        
        # 预测策略权重
        self.strategy_weights = {
            'extreme_reback': 0.4,      # 极限回补策略
            'hot_dimension': 0.3,       # 热门维度策略  
            'combination_fusion': 0.2,   # 组合融合策略
            'balance_correction': 0.1    # 平衡修正策略
        }
    
    def predict_next_period(self, target_period: str) -> Dict[str, Any]:
        """预测下一期的多维生肖"""
        print(f"🎯 多维生肖预测 - 期号: {target_period}")
        
        # 1. 获取分析结果
        analysis_result = self.analyzer.analyze_historical_data()
        extreme_stats = analysis_result['extreme_stats']
        
        # 2. 执行多种预测策略
        strategies_result = {}
        
        # 策略1: 极限回补策略
        strategies_result['extreme_reback'] = self._extreme_reback_strategy(extreme_stats)
        
        # 策略2: 热门维度策略
        strategies_result['hot_dimension'] = self._hot_dimension_strategy(extreme_stats)
        
        # 策略3: 组合融合策略
        strategies_result['combination_fusion'] = self._combination_fusion_strategy(extreme_stats)
        
        # 策略4: 平衡修正策略
        strategies_result['balance_correction'] = self._balance_correction_strategy(extreme_stats)
        
        # 3. 融合所有策略
        final_prediction = self._fuse_strategies(strategies_result)
        
        # 4. 生成预测号码
        predicted_numbers = self._generate_numbers_from_zodiacs(final_prediction['top_zodiacs'], target_period)
        
        return {
            'target_period': target_period,
            'strategies_result': strategies_result,
            'final_prediction': final_prediction,
            'predicted_numbers': predicted_numbers,
            'confidence_score': final_prediction['confidence'],
            'prediction_summary': self._generate_prediction_summary(final_prediction, strategies_result)
        }
    
    def _extreme_reback_strategy(self, extreme_stats: Dict) -> Dict[str, Any]:
        """极限回补策略"""
        zodiac_scores = defaultdict(float)
        all_dimensions = self.analyzer.db.get_zodiac_dimensions()
        
        # 寻找极限/临界维度
        extreme_dimensions = []
        for dim_key, stat in extreme_stats.items():
            if stat['extreme_ratio'] >= 0.6:  # 降低阈值
                extreme_dimensions.append((dim_key, stat))
        
        # 按极限程度排序
        extreme_dimensions.sort(key=lambda x: x[1]['extreme_ratio'], reverse=True)
        
        # 为极限维度的生肖加分
        for dim_key, stat in extreme_dimensions[:10]:  # 取前10个
            if dim_key in all_dimensions:
                zodiacs = all_dimensions[dim_key]
                weight = stat['reback_probability'] * (1 + stat['extreme_ratio'])
                
                for zodiac in zodiacs:
                    zodiac_scores[zodiac] += weight
        
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'strategy_name': '极限回补策略',
            'extreme_dimensions': extreme_dimensions[:5],
            'top_zodiacs': sorted_zodiacs[:8],
            'confidence': min(0.8, len(extreme_dimensions) * 0.1)
        }
    
    def _hot_dimension_strategy(self, extreme_stats: Dict) -> Dict[str, Any]:
        """热门维度策略"""
        zodiac_scores = defaultdict(float)
        all_dimensions = self.analyzer.db.get_zodiac_dimensions()
        
        # 寻找高命中率维度
        hot_dimensions = []
        for dim_key, stat in extreme_stats.items():
            if stat['hit_rate'] >= 0.3 and stat['current_miss'] <= 3:  # 高命中率且近期未遗漏
                hot_dimensions.append((dim_key, stat))
        
        # 按命中率排序
        hot_dimensions.sort(key=lambda x: x[1]['hit_rate'], reverse=True)
        
        # 为热门维度的生肖加分
        for dim_key, stat in hot_dimensions[:8]:
            if dim_key in all_dimensions:
                zodiacs = all_dimensions[dim_key]
                weight = stat['hit_rate'] * (1 - stat['current_miss'] * 0.1)
                
                for zodiac in zodiacs:
                    zodiac_scores[zodiac] += weight
        
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'strategy_name': '热门维度策略',
            'hot_dimensions': hot_dimensions[:5],
            'top_zodiacs': sorted_zodiacs[:8],
            'confidence': min(0.7, len(hot_dimensions) * 0.08)
        }
    
    def _combination_fusion_strategy(self, extreme_stats: Dict) -> Dict[str, Any]:
        """组合融合策略"""
        zodiac_scores = defaultdict(float)
        
        # 重点关注六合和三合组合
        combination_categories = ["六合组合", "三合组合"]
        combination_dims = {}
        
        for category in combination_categories:
            dims = self.analyzer.db.get_dimensions_by_category(category)
            combination_dims.update(dims)
        
        # 分析组合维度
        fusion_combinations = []
        for dim_group, zodiacs in combination_dims.items():
            dim_key = f"{category}-{dim_group}"
            if dim_key in extreme_stats:
                stat = extreme_stats[dim_key]
                if stat['extreme_ratio'] >= 0.4 or stat['hit_rate'] >= 0.25:
                    fusion_combinations.append((dim_group, zodiacs, stat))
        
        # 为组合生肖加分
        for dim_group, zodiacs, stat in fusion_combinations:
            weight = (stat['extreme_ratio'] + stat['hit_rate']) * 0.5
            for zodiac in zodiacs:
                zodiac_scores[zodiac] += weight
        
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'strategy_name': '组合融合策略',
            'fusion_combinations': fusion_combinations,
            'top_zodiacs': sorted_zodiacs[:8],
            'confidence': min(0.6, len(fusion_combinations) * 0.15)
        }
    
    def _balance_correction_strategy(self, extreme_stats: Dict) -> Dict[str, Any]:
        """平衡修正策略"""
        zodiac_scores = defaultdict(float)
        
        # 分析阴阳、奇偶、颜色等平衡维度
        balance_categories = ["阴阳维度", "颜色维度", "性别维度", "前后维度"]
        balance_analysis = {}
        
        for category in balance_categories:
            dims = self.analyzer.db.get_dimensions_by_category(category)
            category_stats = {}
            
            for dim_group, zodiacs in dims.items():
                dim_key = f"{category}-{dim_group}"
                if dim_key in extreme_stats:
                    category_stats[dim_group] = extreme_stats[dim_key]
            
            balance_analysis[category] = category_stats
        
        # 寻找失衡的维度
        imbalanced_dims = []
        for category, stats in balance_analysis.items():
            if len(stats) == 2:  # 二元对立维度
                dims = list(stats.items())
                if len(dims) == 2:
                    dim1, stat1 = dims[0]
                    dim2, stat2 = dims[1]
                    
                    # 计算失衡程度
                    imbalance = abs(stat1['current_miss'] - stat2['current_miss'])
                    if imbalance >= 3:  # 遗漏差距>=3期
                        # 选择遗漏更多的维度
                        if stat1['current_miss'] > stat2['current_miss']:
                            imbalanced_dims.append((f"{category}-{dim1}", stat1))
                        else:
                            imbalanced_dims.append((f"{category}-{dim2}", stat2))
        
        # 为失衡维度的生肖加分
        all_dimensions = self.analyzer.db.get_zodiac_dimensions()
        for dim_key, stat in imbalanced_dims:
            if dim_key in all_dimensions:
                zodiacs = all_dimensions[dim_key]
                weight = stat['current_miss'] * 0.1
                
                for zodiac in zodiacs:
                    zodiac_scores[zodiac] += weight
        
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'strategy_name': '平衡修正策略',
            'imbalanced_dims': imbalanced_dims,
            'top_zodiacs': sorted_zodiacs[:8],
            'confidence': min(0.5, len(imbalanced_dims) * 0.2)
        }
    
    def _fuse_strategies(self, strategies_result: Dict) -> Dict[str, Any]:
        """融合所有策略"""
        final_zodiac_scores = defaultdict(float)
        total_confidence = 0
        
        # 加权融合
        for strategy_name, result in strategies_result.items():
            weight = self.strategy_weights.get(strategy_name, 0.25)
            confidence = result['confidence']
            
            total_confidence += confidence * weight
            
            for zodiac, score in result['top_zodiacs']:
                final_zodiac_scores[zodiac] += score * weight * confidence
        
        # 排序
        sorted_zodiacs = sorted(final_zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'top_zodiacs': sorted_zodiacs[:6],
            'all_scores': dict(final_zodiac_scores),
            'confidence': min(0.9, total_confidence),
            'fusion_method': '加权融合'
        }
    
    def _generate_numbers_from_zodiacs(self, top_zodiacs: List[Tuple[str, float]], target_period: str) -> List[int]:
        """从生肖生成预测号码"""
        predicted_numbers = []

        print(f"🔢 从生肖生成预测号码...")

        # 为每个top生肖生成对应号码
        for i, (zodiac, score) in enumerate(top_zodiacs[:4], 1):  # 取前4个生肖
            try:
                # 获取该生肖对应的号码
                zodiac_numbers = self.mapping_system.get_numbers_for_zodiac(zodiac, target_period)
                print(f"   {i}. 生肖{zodiac} (评分{score:.3f}): {zodiac_numbers}")

                if zodiac_numbers:
                    # 根据评分选择号码数量
                    if score > 0.5:
                        selected_count = 3  # 高分生肖选3个
                    elif score > 0.3:
                        selected_count = 2  # 中分生肖选2个
                    else:
                        selected_count = 1  # 低分生肖选1个

                    selected_numbers = zodiac_numbers[:selected_count]
                    predicted_numbers.extend(selected_numbers)
                    print(f"      选择号码: {selected_numbers}")
                else:
                    print(f"      ❌ 该生肖无对应号码")

            except Exception as e:
                print(f"      ❌ 映射失败: {e}")
                continue

        # 去重并排序
        predicted_numbers = sorted(list(set(predicted_numbers)))
        print(f"   初步号码: {predicted_numbers} (共{len(predicted_numbers)}个)")

        # 确保有8个号码
        if len(predicted_numbers) < 8:
            print(f"   需要补充 {8-len(predicted_numbers)} 个号码...")

            # 智能补充：从剩余生肖中选择
            remaining_zodiacs = top_zodiacs[4:] if len(top_zodiacs) > 4 else []

            for zodiac, score in remaining_zodiacs:
                if len(predicted_numbers) >= 8:
                    break

                try:
                    zodiac_numbers = self.mapping_system.get_numbers_for_zodiac(zodiac, target_period)
                    if zodiac_numbers:
                        for num in zodiac_numbers:
                            if num not in predicted_numbers:
                                predicted_numbers.append(num)
                                print(f"      补充号码: {num} (来自生肖{zodiac})")
                                if len(predicted_numbers) >= 8:
                                    break
                except:
                    continue

            # 如果还不够，使用历史热门号码
            if len(predicted_numbers) < 8:
                hot_numbers = [7, 12, 18, 23, 29, 34, 40, 45]  # 历史热门号码
                for num in hot_numbers:
                    if num not in predicted_numbers:
                        predicted_numbers.append(num)
                        print(f"      补充热门号码: {num}")
                        if len(predicted_numbers) >= 8:
                            break

        final_numbers = sorted(predicted_numbers[:8])
        print(f"   最终号码: {final_numbers}")

        return final_numbers
    
    def _generate_prediction_summary(self, final_prediction: Dict, strategies_result: Dict) -> str:
        """生成预测摘要"""
        summary = f"多维生肖预测摘要:\n"
        summary += f"- 融合置信度: {final_prediction['confidence']:.1%}\n"
        summary += f"- 预测生肖: {', '.join([z for z, s in final_prediction['top_zodiacs'][:3]])}\n"
        
        summary += f"- 策略贡献:\n"
        for strategy_name, result in strategies_result.items():
            weight = self.strategy_weights.get(strategy_name, 0.25)
            summary += f"  {result['strategy_name']}: {result['confidence']:.1%} (权重{weight:.1%})\n"
        
        return summary

def test_multi_dimensional_predictor():
    """测试多维生肖预测器"""
    print("🧪 测试多维生肖预测器")
    print("="*50)
    
    predictor = MultiDimensionalZodiacPredictor()
    
    # 运行预测
    result = predictor.predict_next_period("2025199")
    
    print(f"🎯 预测结果:")
    print(f"目标期号: {result['target_period']}")
    print(f"预测号码: {result['predicted_numbers']}")
    print(f"置信度: {result['confidence_score']:.1%}")
    
    print(f"\n📊 预测生肖排行:")
    for i, (zodiac, score) in enumerate(result['final_prediction']['top_zodiacs'], 1):
        print(f"   {i}. {zodiac}: {score:.3f}")
    
    print(f"\n📋 预测摘要:")
    print(result['prediction_summary'])
    
    print(f"\n✅ 多维生肖预测器测试完成")

if __name__ == "__main__":
    test_multi_dimensional_predictor()
