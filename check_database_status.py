#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库和数据映射接口状态
"""

import os
import sqlite3
import json
import pickle
import csv
import time
from pathlib import Path

def check_file_status():
    """检查关键文件状态"""
    print("📁 关键文件状态检查")
    print("="*60)
    
    files_to_check = {
        'lottery_data.db': '数据库文件',
        'lottery_data_20250717.csv': '历史数据CSV',
        'zodiac_cache.pkl': '生肖缓存文件',
        'history.json': '历史数据JSON',
        'strategy_config.yaml': '策略配置文件'
    }
    
    for filename, description in files_to_check.items():
        if os.path.exists(filename):
            try:
                file_size = os.path.getsize(filename)
                file_time = os.path.getmtime(filename)
                file_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_time))
                
                print(f"✅ {description}: {filename}")
                print(f"   大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
                print(f"   修改时间: {file_time_str}")
                
                # 检查文件是否可读
                try:
                    with open(filename, 'rb') as f:
                        f.read(1)
                    print(f"   状态: 可读取")
                except Exception as e:
                    print(f"   ❌ 状态: 读取失败 - {e}")
                
            except Exception as e:
                print(f"❌ {description}: 检查失败 - {e}")
        else:
            print(f"❌ {description}: 文件不存在")
        print()

def check_database():
    """检查SQLite数据库"""
    print("🗄️ SQLite数据库检查")
    print("="*60)
    
    db_file = 'lottery_data.db'
    
    if not os.path.exists(db_file):
        print(f"❌ 数据库文件不存在: {db_file}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_file, timeout=10)
        cursor = conn.cursor()
        
        print(f"✅ 数据库连接成功")
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 数据库表数量: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n📋 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            print(f"   列数: {len(columns)}")
            
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                print(f"     - {col_name}: {col_type}")
            
            # 获取记录数
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"   记录数: {count:,}")
                
                # 检查最新记录
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 1;")
                    sample = cursor.fetchone()
                    print(f"   样本数据: {sample}")
                    
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_csv_data():
    """检查CSV数据文件"""
    print("📊 CSV数据文件检查")
    print("="*60)
    
    csv_file = 'lottery_data_20250717.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            
            # 读取表头
            headers = next(reader)
            print(f"✅ CSV文件打开成功")
            print(f"📋 列数: {len(headers)}")
            print(f"📋 列名: {headers}")
            
            # 统计行数
            row_count = 0
            sample_rows = []
            
            for i, row in enumerate(reader):
                row_count += 1
                if i < 3:  # 保存前3行作为样本
                    sample_rows.append(row)
                if i > 10000:  # 避免读取过多数据
                    break
            
            print(f"📊 数据行数: {row_count:,}+ 行")
            
            # 显示样本数据
            print(f"📋 样本数据:")
            for i, row in enumerate(sample_rows, 1):
                print(f"   {i}. {row}")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV文件检查失败: {e}")
        return False

def check_cache_file():
    """检查缓存文件"""
    print("💾 缓存文件检查")
    print("="*60)
    
    cache_file = 'zodiac_cache.pkl'
    
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return False
    
    try:
        with open(cache_file, 'rb') as f:
            cache_data = pickle.load(f)
        
        print(f"✅ 缓存文件加载成功")
        print(f"📊 缓存数据类型: {type(cache_data)}")
        
        if isinstance(cache_data, dict):
            print(f"📋 缓存键数量: {len(cache_data)}")
            print(f"📋 缓存键列表: {list(cache_data.keys())[:10]}...")  # 显示前10个键
            
            # 检查数据大小
            total_size = 0
            for key, value in cache_data.items():
                if hasattr(value, '__len__'):
                    total_size += len(value)
            
            print(f"📊 缓存数据总量: {total_size:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存文件检查失败: {e}")
        return False

def check_json_data():
    """检查JSON数据文件"""
    print("📄 JSON数据文件检查")
    print("="*60)
    
    json_file = 'history.json'
    
    if not os.path.exists(json_file):
        print(f"❌ JSON文件不存在: {json_file}")
        return False
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"✅ JSON文件加载成功")
        print(f"📊 数据类型: {type(json_data)}")
        
        if isinstance(json_data, list):
            print(f"📊 记录数量: {len(json_data)}")
            
            if len(json_data) > 0:
                sample = json_data[0]
                print(f"📋 样本记录: {sample}")
                
                # 检查数据结构
                if isinstance(sample, dict):
                    print(f"📋 字段列表: {list(sample.keys())}")
        
        elif isinstance(json_data, dict):
            print(f"📋 键数量: {len(json_data)}")
            print(f"📋 键列表: {list(json_data.keys())[:10]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON文件检查失败: {e}")
        return False

def check_data_mapping_interfaces():
    """检查数据映射接口"""
    print("🔗 数据映射接口检查")
    print("="*60)
    
    # 检查动态映射系统
    print("🔄 检查动态映射系统...")
    try:
        from dynamic_mapping_system import DynamicMappingSystem
        
        start_time = time.time()
        mapping_system = DynamicMappingSystem()
        init_time = time.time() - start_time
        
        print(f"✅ 动态映射系统初始化成功: {init_time:.3f} 秒")
        
        # 测试映射功能
        test_number = 1
        zodiac = mapping_system.get_zodiac_for_number(test_number, "2025001")
        print(f"   测试映射 - 号码{test_number}: {zodiac}")
        
    except Exception as e:
        print(f"❌ 动态映射系统失败: {e}")
    
    # 检查数据属性管理器
    print(f"\n📊 检查数据属性管理器...")
    try:
        from data_attributes import HistoryDataManager, DataAttributeMapper
        
        start_time = time.time()
        data_manager = HistoryDataManager()
        manager_time = time.time() - start_time
        
        print(f"✅ 数据管理器初始化成功: {manager_time:.3f} 秒")
        
        # 测试数据加载
        start_time = time.time()
        data = data_manager.load_data()
        load_time = time.time() - start_time
        
        if data:
            print(f"✅ 数据加载成功: {load_time:.3f} 秒, {len(data)} 条记录")
        else:
            print(f"⚠️ 数据加载为空")
        
    except Exception as e:
        print(f"❌ 数据属性管理器失败: {e}")
    
    # 检查生肖引擎
    print(f"\n🐲 检查生肖引擎...")
    try:
        from optimized_zodiac_engine import OptimizedZodiacEngine
        
        start_time = time.time()
        zodiac_engine = OptimizedZodiacEngine()
        engine_time = time.time() - start_time
        
        if engine_time > 10:
            print(f"⚠️ 生肖引擎初始化缓慢: {engine_time:.2f} 秒")
        else:
            print(f"✅ 生肖引擎初始化正常: {engine_time:.2f} 秒")
        
        print(f"   小组数量: {len(zodiac_engine.all_groups):,}")
        print(f"   最后处理期号: {zodiac_engine.last_processed_period}")
        
    except Exception as e:
        print(f"❌ 生肖引擎失败: {e}")

def provide_database_solutions():
    """提供数据库问题解决方案"""
    print(f"\n💡 数据库问题解决方案")
    print("="*60)
    
    solutions = """
🔧 常见数据库问题及解决方案:

1. 📁 文件损坏或锁定:
   - 检查文件权限
   - 关闭其他可能占用文件的程序
   - 重启系统释放文件锁

2. 💾 缓存文件问题:
   - 删除 zodiac_cache.pkl
   - 让系统重新生成缓存
   - 检查磁盘空间是否充足

3. 🗄️ 数据库连接问题:
   - 检查 lottery_data.db 是否完整
   - 尝试用SQLite工具修复数据库
   - 重新导入CSV数据

4. 📊 数据映射问题:
   - 检查动态映射系统配置
   - 验证期号格式是否正确
   - 确保历史数据完整

5. 🔄 性能问题:
   - 清理过期缓存文件
   - 优化数据库索引
   - 减少同时处理的数据量

⚠️ 紧急修复步骤:
1. 备份重要配置文件
2. 删除所有缓存文件 (*.pkl)
3. 重新启动程序
4. 让系统重新初始化
"""
    
    print(solutions)

def main():
    """主检查流程"""
    print("🔍 数据库和数据映射接口全面检查")
    print("="*70)
    
    # 文件状态检查
    check_file_status()
    
    # 数据库检查
    check_database()
    
    # CSV数据检查
    check_csv_data()
    
    # 缓存文件检查
    check_cache_file()
    
    # JSON数据检查
    check_json_data()
    
    # 数据映射接口检查
    check_data_mapping_interfaces()
    
    # 提供解决方案
    provide_database_solutions()
    
    print(f"\n🎯 数据库检查完成!")

if __name__ == "__main__":
    main()
