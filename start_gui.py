#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩智能预测系统 - GUI启动器
简化版启动脚本，处理依赖和错误
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, scrolledtext

def check_dependencies():
    """检查依赖项"""
    missing_modules = []
    
    try:
        import tkinter.ttk
    except ImportError:
        missing_modules.append("tkinter.ttk")
    
    try:
        import threading
    except ImportError:
        missing_modules.append("threading")
    
    try:
        import json
    except ImportError:
        missing_modules.append("json")
    
    try:
        import datetime
    except ImportError:
        missing_modules.append("datetime")
    
    # 检查后端模块
    backend_modules = [
        "prediction_engine",
        "dsl_strategy_parser", 
        "report_generator",
        "backtest_engine"
    ]
    
    for module in backend_modules:
        try:
            __import__(module)
        except ImportError as e:
            missing_modules.append(f"{module} ({e})")
    
    return missing_modules

def create_simple_gui():
    """创建简化的GUI界面"""
    root = tk.Tk()
    root.title("六合彩智能预测系统 v1.0 (简化版)")
    root.geometry("800x600")
    root.configure(bg='#f0f0f0')
    
    # 主框架
    main_frame = tk.Frame(root, bg='#f0f0f0')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = tk.Label(
        main_frame,
        text="🎲 六合彩智能预测系统",
        font=("Arial", 20, "bold"),
        bg='#f0f0f0',
        fg='#2c3e50'
    )
    title_label.pack(pady=(0, 20))
    
    # 副标题
    subtitle_label = tk.Label(
        main_frame,
        text="基于多维策略融合与机器学习的智能预测系统",
        font=("Arial", 12),
        bg='#f0f0f0',
        fg='#7f8c8d'
    )
    subtitle_label.pack(pady=(0, 30))
    
    # 功能按钮框架
    button_frame = tk.Frame(main_frame, bg='#f0f0f0')
    button_frame.pack(fill=tk.X, pady=20)
    
    # 预测功能
    predict_frame = tk.LabelFrame(button_frame, text="🎯 预测功能", font=("Arial", 12, "bold"), bg='#f0f0f0')
    predict_frame.pack(fill=tk.X, pady=10)
    
    # 期号输入
    period_frame = tk.Frame(predict_frame, bg='#f0f0f0')
    period_frame.pack(fill=tk.X, padx=10, pady=10)
    
    tk.Label(period_frame, text="目标期号:", font=("Arial", 10), bg='#f0f0f0').pack(side=tk.LEFT)
    period_var = tk.StringVar(value="2025201")
    period_entry = tk.Entry(period_frame, textvariable=period_var, font=("Arial", 10), width=15)
    period_entry.pack(side=tk.LEFT, padx=(10, 0))
    
    # 预测按钮
    def run_prediction():
        period = period_var.get().strip()
        if not period:
            messagebox.showerror("错误", "请输入期号")
            return
        
        try:
            # 尝试导入并运行预测引擎
            from prediction_engine import PredictionEngine
            
            result_window = tk.Toplevel(root)
            result_window.title(f"预测结果 - {period}")
            result_window.geometry("600x400")
            
            # 显示加载信息
            loading_label = tk.Label(result_window, text="正在运行预测，请稍候...", font=("Arial", 12))
            loading_label.pack(expand=True)
            
            def do_prediction():
                try:
                    engine = PredictionEngine()
                    result = engine.run_prediction(period)
                    
                    # 更新结果窗口
                    loading_label.destroy()
                    
                    # 显示结果
                    result_text = tk.Text(result_window, font=("Consolas", 10))
                    result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                    
                    # 格式化结果
                    output = f"""
预测结果 - 期号 {period}
{'='*50}

推荐号码: {', '.join(f'{n:02d}' for n in result.final_numbers)}

预测信息:
  置信度: {result.confidence_score:.2%}
  使用策略: {result.total_strategies_used} 个
  使用模型: {result.total_models_used} 个
  执行时间: {result.execution_time:.2f} 秒
  预测时间: {result.prediction_date}

策略详情:
"""
                    for strategy in result.strategy_details:
                        output += f"  • {strategy.get('strategy_name', 'Unknown')}: "
                        output += f"置信度 {strategy.get('confidence', 0):.3f}\n"
                    
                    if result.model_details:
                        output += "\n模型详情:\n"
                        for model in result.model_details:
                            output += f"  • {model.get('model_name', 'Unknown')}: "
                            output += f"置信度 {model.get('confidence', 0):.3f}\n"
                    
                    result_text.insert(tk.END, output)
                    result_text.config(state=tk.DISABLED)
                    
                except Exception as e:
                    loading_label.config(text=f"预测失败: {e}")
            
            # 在后台运行预测
            import threading
            threading.Thread(target=do_prediction, daemon=True).start()
            
        except ImportError as e:
            messagebox.showerror("错误", f"无法加载预测引擎: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"预测失败: {e}")
    
    predict_btn = tk.Button(
        predict_frame,
        text="🎯 开始预测",
        command=run_prediction,
        font=("Arial", 12, "bold"),
        bg='#3498db',
        fg='white',
        padx=20,
        pady=10
    )
    predict_btn.pack(pady=10)
    
    # 工具功能
    tools_frame = tk.LabelFrame(button_frame, text="🔧 工具功能", font=("Arial", 12, "bold"), bg='#f0f0f0')
    tools_frame.pack(fill=tk.X, pady=10)
    
    tools_button_frame = tk.Frame(tools_frame, bg='#f0f0f0')
    tools_button_frame.pack(pady=10)
    
    def run_backtest():
        try:
            from prediction_engine import PredictionEngine
            
            backtest_window = tk.Toplevel(root)
            backtest_window.title("回测分析")
            backtest_window.geometry("600x400")
            
            loading_label = tk.Label(backtest_window, text="正在运行回测分析，请稍候...", font=("Arial", 12))
            loading_label.pack(expand=True)
            
            def do_backtest():
                try:
                    engine = PredictionEngine()
                    result = engine.run_backtest()
                    
                    loading_label.destroy()
                    
                    result_text = tk.Text(backtest_window, font=("Consolas", 10))
                    result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                    
                    if 'error' in result:
                        output = f"回测失败: {result['error']}"
                    else:
                        output = f"""
回测分析结果
{'='*50}

核心指标:
  命中率: {result.get('hit_rate', 0):.2%}
  命中次数: {result.get('hit_count', 0)}
  总期数: {result.get('total_periods', 0)}
  平均遗漏: {result.get('avg_miss_interval', 0):.2f} 期
  最大遗漏: {result.get('max_miss_streak', 0)} 期
  夏普比率: {result.get('sharpe_ratio', 0):.4f}

分析完成时间: {tk.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                    
                    result_text.insert(tk.END, output)
                    result_text.config(state=tk.DISABLED)
                    
                except Exception as e:
                    loading_label.config(text=f"回测失败: {e}")
            
            import threading
            threading.Thread(target=do_backtest, daemon=True).start()
            
        except ImportError as e:
            messagebox.showerror("错误", f"无法加载回测引擎: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {e}")
    
    def show_strategies():
        try:
            from dsl_strategy_parser import DSLStrategyParser
            
            parser = DSLStrategyParser()
            summary = parser.get_strategy_summary()
            
            strategy_window = tk.Toplevel(root)
            strategy_window.title("策略状态")
            strategy_window.geometry("500x400")
            
            strategy_text = tk.Text(strategy_window, font=("Consolas", 10))
            strategy_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            output = f"""
策略配置状态
{'='*50}

总策略数: {summary['total_strategies']}
激活策略数: {summary['active_strategies']}
融合方法: {summary['fusion_method']}
最大输出号码数: {summary['max_output_numbers']}

策略详情:
"""
            for strategy in summary['strategies']:
                status = "✓ 启用" if strategy['active'] else "✗ 禁用"
                output += f"  • {strategy['name']}: {status} (权重: {strategy['weight']})\n"
            
            strategy_text.insert(tk.END, output)
            strategy_text.config(state=tk.DISABLED)
            
        except ImportError as e:
            messagebox.showerror("错误", f"无法加载策略解析器: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"获取策略状态失败: {e}")

    def show_history_data():
        try:
            from prediction_engine import PredictionEngine

            engine = PredictionEngine()
            history_data = engine.load_historical_data()

            if not history_data:
                messagebox.showwarning("警告", "没有找到历史数据")
                return

            data_window = tk.Toplevel(root)
            data_window.title("历史开奖数据")
            data_window.geometry("800x600")

            # 创建表格框架
            table_frame = tk.Frame(data_window)
            table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 创建滚动文本显示数据
            data_text = scrolledtext.ScrolledText(table_frame, font=("Consolas", 9))
            data_text.pack(fill=tk.BOTH, expand=True)

            # 格式化显示数据
            header = f"{'期号':<12} {'日期':<12} {'特码':<6} {'生肖':<6} {'五行':<6} {'波色':<6} {'大小':<4} {'单双':<4}\n"
            data_text.insert(tk.END, header)
            data_text.insert(tk.END, "="*70 + "\n")

            # 显示前100条记录
            for i, record in enumerate(history_data[:100]):
                period = record.get('period', '')
                date = record.get('draw_date', '')[:10]  # 只显示日期部分
                special_code = record.get('special_code', '')
                zodiac = record.get('zodiac', '')
                five_element = record.get('five_element', '')
                wave_color = record.get('wave_color', '')

                if special_code:
                    big_small = "大" if special_code >= 25 else "小"
                    odd_even = "单" if special_code % 2 == 1 else "双"
                else:
                    big_small = odd_even = ""

                line = f"{period:<12} {date:<12} {special_code:<6} {zodiac:<6} {five_element:<6} {wave_color:<6} {big_small:<4} {odd_even:<4}\n"
                data_text.insert(tk.END, line)

            if len(history_data) > 100:
                data_text.insert(tk.END, f"\n... 还有 {len(history_data)-100} 条记录未显示")

            # 添加统计信息
            total_records = len(history_data)
            valid_codes = [r.get('special_code') for r in history_data if r.get('special_code')]

            if valid_codes:
                avg_code = sum(valid_codes) / len(valid_codes)
                big_count = sum(1 for code in valid_codes if code >= 25)
                small_count = len(valid_codes) - big_count

                stats = f"""
\n统计信息:
总记录数: {total_records}
有效记录: {len(valid_codes)}
平均特码: {avg_code:.2f}
大号比例: {big_count/len(valid_codes)*100:.1f}%
小号比例: {small_count/len(valid_codes)*100:.1f}%
"""
                data_text.insert(tk.END, stats)

            data_text.config(state=tk.DISABLED)

        except ImportError as e:
            messagebox.showerror("错误", f"无法加载预测引擎: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"加载历史数据失败: {e}")

    # 工具按钮
    tk.Button(
        tools_button_frame,
        text="📊 回测分析",
        command=run_backtest,
        font=("Arial", 10),
        bg='#e74c3c',
        fg='white',
        padx=15,
        pady=5
    ).pack(side=tk.LEFT, padx=5)
    
    tk.Button(
        tools_button_frame,
        text="⚙️ 策略状态",
        command=show_strategies,
        font=("Arial", 10),
        bg='#f39c12',
        fg='white',
        padx=15,
        pady=5
    ).pack(side=tk.LEFT, padx=5)

    tk.Button(
        tools_button_frame,
        text="📊 历史数据",
        command=show_history_data,
        font=("Arial", 10),
        bg='#9b59b6',
        fg='white',
        padx=15,
        pady=5
    ).pack(side=tk.LEFT, padx=5)

    def manual_input_data():
        try:
            from data_attributes import HistoryDataManager, DataAttributeMapper

            input_window = tk.Toplevel(root)
            input_window.title("手动输入开奖数据")
            input_window.geometry("400x350")
            input_window.resizable(False, False)

            # 主框架
            main_frame = tk.Frame(input_window, bg='#f0f0f0')
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = tk.Label(
                main_frame,
                text="📝 手动输入开奖数据",
                font=("Arial", 14, "bold"),
                bg='#f0f0f0'
            )
            title_label.pack(pady=(0, 20))

            # 输入框架
            input_frame = tk.LabelFrame(main_frame, text="开奖信息", bg='#f0f0f0', font=("Arial", 10, "bold"))
            input_frame.pack(fill=tk.X, pady=(0, 20))

            # 期号
            period_frame = tk.Frame(input_frame, bg='#f0f0f0')
            period_frame.pack(fill=tk.X, padx=10, pady=5)
            tk.Label(period_frame, text="期号:", bg='#f0f0f0', width=8).pack(side=tk.LEFT)
            period_var = tk.StringVar()
            period_entry = tk.Entry(period_frame, textvariable=period_var, font=("Arial", 10))
            period_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

            # 日期
            date_frame = tk.Frame(input_frame, bg='#f0f0f0')
            date_frame.pack(fill=tk.X, padx=10, pady=5)
            tk.Label(date_frame, text="日期:", bg='#f0f0f0', width=8).pack(side=tk.LEFT)
            date_var = tk.StringVar(value=tk.datetime.datetime.now().strftime('%Y-%m-%d'))
            date_entry = tk.Entry(date_frame, textvariable=date_var, font=("Arial", 10))
            date_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

            # 特码
            code_frame = tk.Frame(input_frame, bg='#f0f0f0')
            code_frame.pack(fill=tk.X, padx=10, pady=5)
            tk.Label(code_frame, text="特码:", bg='#f0f0f0', width=8).pack(side=tk.LEFT)
            code_var = tk.StringVar()
            code_entry = tk.Entry(code_frame, textvariable=code_var, font=("Arial", 10))
            code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

            # 属性预览
            preview_frame = tk.LabelFrame(main_frame, text="属性预览", bg='#f0f0f0', font=("Arial", 10, "bold"))
            preview_frame.pack(fill=tk.X, pady=(0, 20))

            preview_text = tk.Text(preview_frame, height=4, font=("Consolas", 9))
            preview_text.pack(fill=tk.X, padx=10, pady=10)

            def update_preview(*args):
                try:
                    code_str = code_var.get().strip()
                    if code_str and code_str.isdigit():
                        code = int(code_str)
                        if 1 <= code <= 49:
                            mapper = DataAttributeMapper()
                            attrs = mapper.map_all_attributes(code)

                            preview_text.delete(1.0, tk.END)
                            preview_info = f"""生肖: {attrs['zodiac']}    五行: {attrs['five_element']}
波色: {attrs['wave_color']}    大小: {attrs['big_small']}
单双: {attrs['odd_even']}    尾数: {attrs['tail_number']}"""
                            preview_text.insert(tk.END, preview_info)
                        else:
                            preview_text.delete(1.0, tk.END)
                            preview_text.insert(tk.END, "特码范围: 1-49")
                    else:
                        preview_text.delete(1.0, tk.END)
                        preview_text.insert(tk.END, "请输入特码查看属性预览")
                except:
                    preview_text.delete(1.0, tk.END)
                    preview_text.insert(tk.END, "输入错误")

            code_var.trace('w', update_preview)
            update_preview()

            # 按钮
            button_frame = tk.Frame(main_frame, bg='#f0f0f0')
            button_frame.pack(fill=tk.X)

            def save_data():
                try:
                    period = period_var.get().strip()
                    date = date_var.get().strip()
                    code_str = code_var.get().strip()

                    if not period or not date or not code_str:
                        messagebox.showerror("错误", "请填写完整信息")
                        return

                    if not code_str.isdigit():
                        messagebox.showerror("错误", "特码必须是数字")
                        return

                    code = int(code_str)

                    manager = HistoryDataManager()
                    success, message = manager.add_or_update_record(period, date, code)

                    if success:
                        if manager.save_data():
                            messagebox.showinfo("成功", message + "\n数据已保存")
                            input_window.destroy()
                        else:
                            messagebox.showerror("错误", "保存失败")
                    else:
                        messagebox.showerror("错误", message)

                except Exception as e:
                    messagebox.showerror("错误", f"保存失败: {e}")

            tk.Button(
                button_frame,
                text="💾 保存",
                command=save_data,
                font=("Arial", 10),
                bg='#27ae60',
                fg='white',
                padx=20,
                pady=5
            ).pack(side=tk.LEFT, padx=(0, 10))

            tk.Button(
                button_frame,
                text="❌ 取消",
                command=input_window.destroy,
                font=("Arial", 10),
                bg='#e74c3c',
                fg='white',
                padx=20,
                pady=5
            ).pack(side=tk.RIGHT)

        except ImportError as e:
            messagebox.showerror("错误", f"无法加载数据管理器: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"打开输入窗口失败: {e}")

    tk.Button(
        tools_button_frame,
        text="➕ 手动输入",
        command=manual_input_data,
        font=("Arial", 10),
        bg='#27ae60',
        fg='white',
        padx=15,
        pady=5
    ).pack(side=tk.LEFT, padx=5)
    
    # 状态信息
    status_frame = tk.LabelFrame(button_frame, text="📊 系统状态", font=("Arial", 12, "bold"), bg='#f0f0f0')
    status_frame.pack(fill=tk.X, pady=10)
    
    status_text = tk.Text(status_frame, height=8, font=("Consolas", 9))
    status_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 显示系统信息
    system_info = f"""
系统信息:
  Python版本: {sys.version.split()[0]}
  工作目录: {os.getcwd()}
  系统时间: {tk.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

功能状态:
  ✓ 基础GUI界面
  ✓ 预测引擎
  ✓ 策略解析器
  ✓ 回测分析
  ✓ 报告生成器

使用说明:
  1. 输入期号，点击"开始预测"
  2. 使用"回测分析"验证策略效果
  3. 查看"策略状态"了解当前配置
"""
    
    status_text.insert(tk.END, system_info)
    status_text.config(state=tk.DISABLED)
    
    return root

def main():
    """主函数"""
    print("🎲 六合彩智能预测系统启动中...")
    
    # 检查依赖
    missing = check_dependencies()
    
    if missing:
        print("❌ 缺少以下依赖项:")
        for module in missing:
            print(f"   - {module}")
        print("\n尝试启动简化版GUI...")
    else:
        print("✅ 所有依赖项检查通过")
    
    try:
        # 尝试启动完整GUI
        if not missing:
            print("启动完整GUI界面...")
            from gui_main import LotteryPredictionGUI
            root = tk.Tk()
            app = LotteryPredictionGUI(root)
            root.mainloop()
        else:
            # 启动简化GUI
            print("启动简化GUI界面...")
            root = create_simple_gui()
            root.mainloop()
            
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        
        # 最后的备选方案：命令行界面
        print("\n尝试命令行模式...")
        try:
            from prediction_engine import PredictionEngine
            engine = PredictionEngine()
            
            period = input("请输入预测期号 (默认2025201): ").strip() or "2025201"
            print(f"\n正在预测期号: {period}")
            
            result = engine.run_prediction(period)
            
            print(f"\n🎯 预测结果:")
            print(f"推荐号码: {', '.join(f'{n:02d}' for n in result.final_numbers)}")
            print(f"置信度: {result.confidence_score:.2%}")
            print(f"执行时间: {result.execution_time:.2f}秒")
            
        except Exception as cmd_error:
            print(f"❌ 命令行模式也失败: {cmd_error}")
            print("请检查系统环境和依赖项")

if __name__ == "__main__":
    main()
