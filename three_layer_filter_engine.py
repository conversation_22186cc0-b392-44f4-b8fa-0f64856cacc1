from typing import List, Dict, Set, Tuple
from tail_group_filter import TailGroupFilter
import json
from pathlib import Path
from dataclasses import dataclass

@dataclass
class FilterResult:
    """筛选结果数据类"""
    numbers: List[int]
    confidence: float = 0.0
    description: str = ""

class ThreeLayerFilterEngine:
    def __init__(self, config_path: str = None):
        """初始化三层筛选引擎"""
        self.config_path = config_path or "filter_config.json"
        self.tail_filter = TailGroupFilter(
            db_path="lottery_data.db",
            config_file=self.config_path
        )  # 尾数筛选器
        self.cache = {}  # 缓存结果

        # 初始化筛选配置
        self.init_filter_config()

        # 加载外部配置（如果提供）
        if config_path:
            self.load_config(config_path)

    def load_config(self, config_path: str):
        """从配置文件加载筛选配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                if 'filter_config' in config:
                    fc = config['filter_config']
                    if 'large_filter' in fc:
                        self.large_filter_config.update(fc['large_filter'])
                    if 'medium_filter' in fc:
                        self.medium_filter_config.update(fc['medium_filter'])
        except Exception as e:
            print(f"⚠️ 加载筛选配置失败: {str(e)}")

    def init_filter_config(self):
        """初始化筛选配置"""
        # 大筛子配置
        self.large_filter_config = {
            'zodiac_weights': {
                'rat': 1.0, 'ox': 1.0, 'tiger': 1.0, 'rabbit': 1.0,
                'dragon': 1.0, 'snake': 1.0, 'horse': 1.0, 'sheep': 1.0,
                'monkey': 1.0, 'rooster': 1.0, 'dog': 1.0, 'pig': 1.0
            },
            'element_weights': {
                'metal': 1.0, 'wood': 1.0, 'water': 1.0,
                'fire': 1.0, 'earth': 1.0
            }
        }

        # 中筛子配置
        self.medium_filter_config = {
            'odd_even_ratio': 0.5,  # 奇偶比例
            'size_threshold': 24,    # 大小分界值
            'prime_weight': 0.3      # 质数权重
        }

    def large_filter(self, numbers: List[int]) -> List[int]:
        """大筛子：基于生肖和五行特征筛选"""
        if not numbers:
            return []

        # 简单实现：保留80%的号码
        filtered = sorted(numbers)
        cutoff = int(len(filtered) * 0.8)
        return filtered[:cutoff]

    def medium_filter(self, numbers: List[int]) -> List[int]:
        """中筛子：基于号码特征筛选"""
        if not numbers:
            return []

        filtered = []
        for num in numbers:
            # 检查奇偶性
            is_odd = num % 2 == 1
            # 检查大小
            is_big = num > self.medium_filter_config['size_threshold']
            # 根据特征筛选
            if (is_odd and is_big) or (not is_odd and not is_big):
                filtered.append(num)

        return sorted(filtered)

    def small_filter(self, numbers: List[int]) -> Dict[str, List[int]]:
        """小筛子：使用尾数组合筛选"""
        return self.tail_filter.filter_candidates(numbers)

    def apply_all_filters(self, numbers: List[int]) -> FilterResult:
        """应用完整的三层筛选"""
        if not numbers:
            return FilterResult([], 0.0, "输入为空")

        # 1. 大筛子
        large_filtered = self.large_filter(numbers)

        # 2. 中筛子
        medium_filtered = self.medium_filter(large_filtered)

        # 3. 小筛子
        small_filtered = self.small_filter(medium_filtered)

        # 统计筛选效果
        stats = self.get_filter_statistics(numbers)

        # 计算综合置信度
        confidence = (len(medium_filtered) / len(numbers)) if numbers else 0

        return FilterResult(
            numbers=medium_filtered,
            confidence=confidence,
            description=f"筛选后保留 {len(medium_filtered)}/{len(numbers)} 个号码"
        )

    def large_filter(self, candidates: List[int]) -> List[int]:
        """第一层筛选：大筛子（基于生肖和五行）"""
        # TODO: 实现生肖和五行筛选逻辑
        return sorted(list(set(candidates)))

    def medium_filter(self, candidates: List[int]) -> List[int]:
        """第二层筛选：中筛子（基于四码组合）"""
        if not candidates:
            return []

        # 根据号码特征进行筛选
        filtered = []
        for num in candidates:
            if self._check_medium_criteria(num):
                filtered.append(num)

        return sorted(filtered)

    def small_filter(self, candidates: List[int]) -> Dict[str, List[int]]:
        """第三层筛选：小筛子（尾数组合筛选）"""
        return self.tail_filter.filter_candidates(candidates)

    def _check_medium_criteria(self, number: int) -> bool:
        """检查号码是否满足中筛子条件"""
        # 1. 奇偶性检查
        is_odd = number % 2 == 1

        # 2. 大小数检查（24为分界）
        is_big = number > 24

        # 3. 合数质数检查
        is_prime = self._is_prime(number)

        # 根据历史统计规律筛选
        if is_odd and is_big and not is_prime:
            return True
        if not is_odd and not is_big and is_prime:
            return True

        return False

    def _is_prime(self, n: int) -> bool:
        """判断一个数是否为质数"""
        if n < 2:
            return False
        for i in range(2, int(n ** 0.5) + 1):
            if n % i == 0:
                return False
        return True

    def apply_all_filters(self, candidates: List[int]) -> Dict[str, List[int]]:
        """应用全部三层筛选"""
        # 1. 大筛子
        large_filtered = self.large_filter(candidates)

        # 2. 中筛子
        medium_filtered = self.medium_filter(large_filtered)

        # 3. 小筛子（尾数组合）
        final_results = self.small_filter(medium_filtered)

        return final_results

    def analyze_filter_effectiveness(self,
                                  history_data: List[int],
                                  prediction_data: List[int]) -> Dict[str, float]:
        """分析三层筛选的有效性"""
        # 原始命中率
        original_hit_rate = len(set(history_data) & set(prediction_data)) / len(prediction_data)

        # 大筛子后的命中率
        large_filtered = self.large_filter(prediction_data)
        large_hit_rate = len(set(history_data) & set(large_filtered)) / len(large_filtered) if large_filtered else 0

        # 中筛子后的命中率
        medium_filtered = self.medium_filter(large_filtered)
        medium_hit_rate = len(set(history_data) & set(medium_filtered)) / len(medium_filtered) if medium_filtered else 0

        # 小筛子后的命中率（使用最佳尾数组合）
        small_filtered_groups = self.small_filter(medium_filtered)
        best_group_hit_rate = 0
        if small_filtered_groups:
            best_group = max(small_filtered_groups.values(), key=len)
            best_group_hit_rate = len(set(history_data) & set(best_group)) / len(best_group) if best_group else 0

        return {
            "original_hit_rate": original_hit_rate,
            "large_filter_hit_rate": large_hit_rate,
            "medium_filter_hit_rate": medium_hit_rate,
            "small_filter_hit_rate": best_group_hit_rate
        }

    def get_filter_statistics(self, candidates: List[int]) -> Dict[str, int]:
        """获取筛选统计信息"""
        stats = {
            "initial_count": len(candidates)
        }

        # 大筛子统计
        large_filtered = self.large_filter(candidates)
        stats["after_large_filter"] = len(large_filtered)

        # 中筛子统计
        medium_filtered = self.medium_filter(large_filtered)
        stats["after_medium_filter"] = len(medium_filtered)

        # 小筛子统计
        small_filtered = self.small_filter(medium_filtered)
        stats["tail_group_count"] = len(small_filtered)
        stats["average_group_size"] = sum(len(group) for group in small_filtered.values()) / len(small_filtered) if small_filtered else 0

        return stats
