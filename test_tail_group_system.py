import unittest
from tail_group_filter import Tai<PERSON><PERSON><PERSON><PERSON>ilter
from pathlib import Path
import sqlite3
import random
from typing import List
import json

class TestTailGroupSystem(unittest.TestCase):

    def setUp(self):
        """测试前的准备工作"""
        self.test_db = "test_lottery.db"
        self.test_config = "test_filter_config.json"

        # 创建测试配置文件
        config = {
            "tail_group_settings": {
                "min_group_size": 3,
                "max_group_size": 6,
                "min_tail_value": 0,
                "max_tail_value": 9,
                "hot_group_threshold": 5,
                "analysis_window": 30
            }
        }
        with open(self.test_config, 'w') as f:
            json.dump(config, f)

        self.filter = TailGroupFilter(db_path=self.test_db, config_file=self.test_config)

        # 生成模拟历史数据
        self.history_data = self.generate_test_data()

    def tearDown(self):
        """测试后的清理工作"""
        self.filter.conn.close()
        if Path(self.test_db).exists():
            Path(self.test_db).unlink()
        if Path(self.test_config).exists():
            Path(self.test_config).unlink()

    def generate_test_data(self) -> List[dict]:
        """生成测试用的历史开奖数据"""
        data = []
        for i in range(100):  # 生成100期数据
            period = f"2025{str(i+1).zfill(3)}"
            numbers = []
            # 确保生成的数字包含配置中的尾数
            for group in self.filter.config['groups']:
                tails = group['tails']
                for tail in tails:
                    if len(numbers) < 7:
                        base = random.randint(0, 4) * 10 + tail
                        if 1 <= base <= 49 and base not in numbers:
                            numbers.append(base)

            # 补充剩余的随机数字
            while len(numbers) < 7:
                num = random.randint(1, 49)
                if num not in numbers:
                    numbers.append(num)

            data.append({
                "period": period,
                "numbers": numbers
            })
        return data

    def test_basic_functions(self):
        """测试基础功能"""
        # 测试默认组合生成
        self.assertTrue(len(self.filter.config['groups']) > 0)

        # 测试号码匹配
        for group in self.filter.config['groups']:
            numbers = self.filter.get_numbers_by_tail(set(group['tails']))
            self.assertTrue(len(numbers) > 0)

        # 测试缓存功能
        test_tails = {1, 2, 3}
        result1 = self.filter.get_numbers_by_tail(test_tails)
        result2 = self.filter.get_numbers_by_tail(test_tails)
        self.assertEqual(result1, result2)

    def test_database_operations(self):
        """测试数据库操作"""
        # 测试保存配置
        old_config = self.filter.config.copy()
        self.filter.save_to_database()

        # 创建新的过滤器实例并验证配置加载
        new_filter = TailGroupFilter(
            db_path=self.test_db,
            config_file=self.test_config
        )

        # 验证配置的正确性
        self.assertEqual(len(new_filter.config['groups']), len(old_config['groups']))
        new_filter.conn.close()

    def test_analysis_functions(self):
        """测试分析功能"""
        # 先保存一些历史数据
        for data in self.history_data[:10]:  # 使用前10期数据
            self.filter.conn.execute(
                'INSERT INTO lottery_results (draw_date, numbers) VALUES (?, ?)',
                (data['period'], ','.join(map(str, data['numbers'])))
            )
        self.filter.save_to_database()

        # 测试统计分析
        stats = self.filter.analyze_history(days=365)
        self.assertTrue(len(stats) > 0)

        # 测试热门组合获取
        hot_groups = self.filter.get_hot_groups(min_hit_rate=0.1)
        self.assertTrue(isinstance(hot_groups, list))
        for group in hot_groups:
            self.assertIn('label', group)
            self.assertIn('hit_rate', group)
            self.assertIn('prediction_confidence', group)

    def test_prediction_and_backtest(self):
        """测试预测和回测功能"""
        # 准备测试数据：使用80期作为训练数据，20期作为测试数据
        self.filter.conn.execute('''DELETE FROM lottery_results''')

        # 插入训练数据
        train_data = self.history_data[:80]
        for data in train_data:
            self.filter.conn.execute(
                'INSERT INTO lottery_results (draw_date, numbers) VALUES (?, ?)',
                (data['period'], ','.join(map(str, data['numbers'])))
            )
        self.filter.conn.commit()

        # 对每一期进行预测和验证
        hits = 0
        total_predictions = 0

        # 使用热门组合生成预测
        hot_groups = self.filter.get_hot_groups(min_hit_rate=0.1)

        test_data = self.history_data[80:]
        for test_period in test_data:
            # 获取所有可能的预测号码
            predictions = set()
            for group in hot_groups:
                group_config = next(g for g in self.filter.config['groups'] if g['label'] == group['label'])
                matched = self.filter.get_numbers_by_tail(set(group_config['tails']))
                predictions.update(matched)

            # 确保预测号码在合理范围内
            predictions = {n for n in predictions if 1 <= n <= 49}

            # 验证预测结果
            actual_numbers = set(test_period['numbers'])
            current_hits = len(predictions & actual_numbers)
            hits += current_hits
            total_predictions += len(predictions) if current_hits > 0 else 0

        # 计算预测准确率
        accuracy = hits / total_predictions if total_predictions > 0 else 0
        print(f"预测准确率: {accuracy:.2%}")
        self.assertTrue(accuracy > 0)  # 确保至少有一些命中

def run_tests():
    """运行所有测试"""
    unittest.main(argv=[''], verbosity=2)

if __name__ == '__main__':
    run_tests()
