#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
100期回测性能测试
检查用时、模组调用和结果分析
"""

import time
from datetime import datetime
from prediction_engine import PredictionEngine

def run_100_period_backtest():
    """运行100期回测测试"""
    print('🚀 开始100期回测测试...')
    print(f'开始时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('='*60)
    
    start_time = time.time()
    
    try:
        # 初始化预测引擎
        print('📊 初始化预测引擎...')
        engine = PredictionEngine()
        
        print('📈 加载历史数据...')
        history_data = engine.load_historical_data()
        print(f'   - 加载了 {len(history_data)} 条历史记录')
        
        print('🔄 开始回测分析...')
        
        # 运行回测
        result = engine.run_backtest()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print('='*60)
        print('✅ 回测完成！')
        print(f'结束时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print(f'总用时: {execution_time:.2f} 秒 ({execution_time/60:.2f} 分钟)')
        print('='*60)
        
        # 分析结果
        if 'error' in result:
            print(f'❌ 回测失败: {result["error"]}')
        else:
            print('📊 回测结果分析:')
            print(f'   命中率: {result.get("hit_rate", 0):.2%}')
            print(f'   命中次数: {result.get("hit_count", 0)}')
            print(f'   总期数: {result.get("total_periods", 0)}')
            print(f'   平均遗漏: {result.get("avg_miss_interval", 0):.2f} 期')
            print(f'   最大遗漏: {result.get("max_miss_streak", 0)} 期')
            print(f'   夏普比率: {result.get("sharpe_ratio", 0):.4f}')
            
            # 显示最近10期详细结果
            if 'details' in result and result['details']:
                print('\n📋 最近10期详细结果:')
                for i, detail in enumerate(result['details'][:10], 1):
                    period = detail.get('period', '')
                    predicted = detail.get('predicted', [])
                    actual = detail.get('actual', '')
                    is_hit = detail.get('is_hit', False)
                    
                    pred_str = ', '.join(f'{n:02d}' for n in predicted[:6])
                    status = '✓ 命中' if is_hit else '✗ 未中'
                    print(f'   {i:2d}. {period}: [{pred_str}...] → {actual:02d} {status}')
            
            print('\n🔧 模组调用检查:')
            summary = engine.get_prediction_summary()
            print(f'   - 可用策略: {summary.get("available_strategies", 0)} 个')
            print(f'   - 训练模型: {summary.get("trained_models", 0)} 个')
            print(f'   - 引擎状态: {summary.get("engine_status", "unknown")}')
            
            # 性能分析
            print('\n⚡ 性能分析:')
            periods_per_second = result.get("total_periods", 0) / execution_time if execution_time > 0 else 0
            print(f'   - 处理速度: {periods_per_second:.2f} 期/秒')
            print(f'   - 平均每期用时: {execution_time/result.get("total_periods", 1):.3f} 秒')
            
            # 策略效果分析
            if result.get("hit_rate", 0) > 0:
                print('\n📈 策略效果评估:')
                hit_rate = result.get("hit_rate", 0)
                if hit_rate >= 0.25:
                    performance = "优秀"
                elif hit_rate >= 0.20:
                    performance = "良好"
                elif hit_rate >= 0.15:
                    performance = "一般"
                else:
                    performance = "需要改进"
                
                print(f'   - 整体表现: {performance}')
                print(f'   - 理论期望: 约{1/hit_rate:.1f}期命中一次' if hit_rate > 0 else '   - 理论期望: 无法计算')
                
                sharpe = result.get("sharpe_ratio", 0)
                risk_level = "低风险" if sharpe > 0.5 else "中风险" if sharpe > 0 else "高风险"
                print(f'   - 风险评估: {risk_level}')
            
    except Exception as e:
        print(f'❌ 回测过程出错: {e}')
        import traceback
        traceback.print_exc()
    
    print('\n🎯 测试完成！')

if __name__ == "__main__":
    run_100_period_backtest()
