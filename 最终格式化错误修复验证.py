#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终格式化错误修复验证 - 彻底验证格式化问题是否解决
"""

import traceback

def final_verification():
    """最终验证"""
    print("🎯 最终格式化错误修复验证")
    print("=" * 60)
    
    try:
        print("1️⃣ 导入适配器...")
        from prediction_engine_adapter import PredictionEngineAdapter
        print("✅ 适配器导入成功")
        
        print("\n2️⃣ 初始化适配器...")
        adapter = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        print("\n3️⃣ 设置阈值（模拟GUI操作）...")
        adapter.advanced_zodiac_engine.z_score_threshold = 2.0
        print("✅ 阈值设置成功")
        
        print("\n4️⃣ 调用高级分析...")
        analysis_result = adapter.get_advanced_zodiac_analysis()
        
        if 'error' in analysis_result:
            print("❌ 高级分析失败:", analysis_result['error'])
            return False
        
        print("✅ 高级分析调用成功")
        
        print("\n5️⃣ 验证返回数据结构...")
        candidates = analysis_result.get('candidates', [])
        system_status = analysis_result.get('system_status', {})
        top_recommendations = analysis_result.get('top_recommendations', [])
        energy_analysis = analysis_result.get('energy_analysis', {})
        
        print("   候选组合数量:", len(candidates))
        print("   系统状态:", system_status.get('status', 'unknown'))
        print("   推荐组合数量:", len(top_recommendations))
        print("   能量分析:", energy_analysis)
        
        print("\n6️⃣ 测试格式化操作...")
        
        # 测试所有可能的格式化操作
        for i, candidate in enumerate(candidates[:3]):
            try:
                # 测试所有字段的格式化
                rank = candidate.get('rank', 0)
                members = candidate.get('members', [])
                z_score = candidate.get('z_score', 0)
                current_miss = candidate.get('current_miss', 0)
                urgency_level = candidate.get('urgency_level', '')
                recommendation_strength = candidate.get('recommendation_strength', 0)
                
                # 格式化测试
                members_str = ", ".join(str(m) for m in members)
                z_score_str = "{:.2f}".format(float(z_score))
                strength_str = "{:.1%}".format(float(recommendation_strength))
                
                print("   候选组合", i+1, ":")
                print("     排名:", rank)
                print("     成员:", members_str)
                print("     Z-Score:", z_score_str)
                print("     当前遗漏:", current_miss)
                print("     紧急度:", urgency_level)
                print("     推荐强度:", strength_str)
                
            except Exception as e:
                print("   ❌ 候选组合", i+1, "格式化失败:", str(e))
                traceback.print_exc()
                return False
        
        print("\n7️⃣ 测试状态格式化...")
        try:
            total_groups = system_status.get('total_groups', 0)
            active_candidates = system_status.get('active_candidates', 0)
            avg_z_score = system_status.get('avg_z_score', 0)
            max_z_score = system_status.get('max_z_score', 0)
            
            # 格式化测试
            total_groups_str = "{:,}".format(int(total_groups))
            avg_z_score_str = "{:.3f}".format(float(avg_z_score))
            max_z_score_str = "{:.3f}".format(float(max_z_score))
            
            print("   总组数:", total_groups_str)
            print("   活跃候选:", active_candidates)
            print("   平均Z-Score:", avg_z_score_str)
            print("   最大Z-Score:", max_z_score_str)
            
        except Exception as e:
            print("   ❌ 状态格式化失败:", str(e))
            traceback.print_exc()
            return False
        
        print("\n8️⃣ 测试报告生成...")
        try:
            report = adapter.generate_advanced_report()
            if "报告生成失败" in report:
                print("   ❌ 报告生成失败")
                return False
            else:
                print("   ✅ 报告生成成功，长度:", len(report), "字符")
        except Exception as e:
            print("   ❌ 报告生成异常:", str(e))
            return False
        
        print("\n9️⃣ 多次调用稳定性测试...")
        for i in range(5):
            try:
                test_result = adapter.get_advanced_zodiac_analysis()
                if 'error' in test_result:
                    print("   ❌ 第", i+1, "次调用失败:", test_result['error'])
                    return False
                print("   ✅ 第", i+1, "次调用成功")
            except Exception as e:
                print("   ❌ 第", i+1, "次调用异常:", str(e))
                return False
        
        print("\n🎉 所有验证测试通过！")
        return True
        
    except Exception as e:
        print("❌ 验证过程中出现异常:")
        print("   错误类型:", type(e).__name__)
        print("   错误信息:", str(e))
        print("\n详细错误堆栈:")
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🔧 测试边界情况")
    print("-" * 40)
    
    try:
        from prediction_engine_adapter import PredictionEngineAdapter
        adapter = PredictionEngineAdapter()
        
        # 测试各种可能的边界情况
        edge_cases = [
            ("正常调用", lambda: adapter.get_advanced_zodiac_analysis()),
            ("重复调用", lambda: adapter.get_advanced_zodiac_analysis()),
            ("阈值修改后调用", lambda: (
                setattr(adapter.advanced_zodiac_engine, 'z_score_threshold', 1.5),
                adapter.get_advanced_zodiac_analysis()
            )[1]),
        ]
        
        for case_name, case_func in edge_cases:
            try:
                print("   测试", case_name, "...")
                result = case_func()
                
                if isinstance(result, dict) and 'error' in result:
                    print("   ❌", case_name, "返回错误:", result['error'])
                    return False
                else:
                    print("   ✅", case_name, "成功")
                    
            except Exception as e:
                print("   ❌", case_name, "异常:", str(e))
                return False
        
        print("✅ 边界情况测试通过")
        return True
        
    except Exception as e:
        print("❌ 边界情况测试失败:", str(e))
        return False

def main():
    """主函数"""
    print("🔧 启动最终格式化错误修复验证...")
    
    # 运行验证
    verification_result = final_verification()
    edge_case_result = test_edge_cases()
    
    print("\n" + "="*60)
    print("📋 最终验证结果")
    print("="*60)
    
    print("格式化错误修复验证:", "✅ 通过" if verification_result else "❌ 失败")
    print("边界情况测试:", "✅ 通过" if edge_case_result else "❌ 失败")
    
    overall_success = verification_result and edge_case_result
    
    print("\n🎯 总体结果:", "✅ 完全成功" if overall_success else "❌ 存在问题")
    
    if overall_success:
        print("\n🎉 恭喜！格式化错误已彻底解决")
        print("✅ 错误 'unsupported format string passed to dict.format' 已完全修复")
        print("✅ 高级分析功能完全正常")
        print("✅ 所有格式化操作都成功")
        print("✅ 多次调用稳定可靠")
        print("✅ 边界情况处理正确")
        
        print("\n💡 现在可以安全使用:")
        print("   - start_lottery_gui.bat 启动GUI")
        print("   - 启动主GUI-调试版.bat 调试启动")
        print("   - GUI中的所有高级分析功能")
        print("   - 所有候选组合显示和格式化")
        
        print("\n🎯 修复要点:")
        print("   - 增强了错误处理机制")
        print("   - 确保所有数值都是正确的类型")
        print("   - 避免了所有可能的格式化陷阱")
        print("   - 添加了完整的异常捕获")
        
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("请检查上述错误信息并进行相应修复")
    
    return overall_success

if __name__ == "__main__":
    main()
