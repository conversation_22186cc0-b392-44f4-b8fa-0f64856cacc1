@echo off
title Main GUI Debug Launcher
color 0E

echo.
echo ================================================
echo    Main GUI Debug Launcher v1.0
echo    Debugging GUI startup issues
echo ================================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check Python environment
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] 'python' command not available, trying 'py'...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Python not found!
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo [OK] Python environment found
%PYTHON_CMD% --version

:: Check core files
echo.
echo [INFO] Checking core files...

if not exist "gui_main.py" (
    echo [ERROR] Missing file: gui_main.py
    pause
    exit /b 1
)

if not exist "prediction_engine_adapter.py" (
    echo [ERROR] Missing file: prediction_engine_adapter.py
    pause
    exit /b 1
)

echo [OK] Core files found

:: Test basic imports
echo.
echo [INFO] Testing basic imports...

%PYTHON_CMD% -c "import tkinter; print('tkinter OK')"
if %errorlevel% neq 0 (
    echo [ERROR] tkinter import failed
    pause
    exit /b 1
)

%PYTHON_CMD% -c "import prediction_engine_adapter; print('adapter OK')"
if %errorlevel% neq 0 (
    echo [ERROR] adapter import failed
    pause
    exit /b 1
)

echo [OK] Basic imports successful

:: Start GUI with detailed output
echo.
echo [INFO] Starting main GUI with detailed output...
echo [INFO] If GUI window doesn't appear, check the output below for errors
echo.
echo ================================================
echo  Starting GUI - Please wait...
echo ================================================
echo.

%PYTHON_CMD% gui_main.py

:: Check result
echo.
echo ================================================
if %errorlevel% equ 0 (
    echo [OK] GUI exited normally
) else (
    echo [ERROR] GUI failed with error code: %errorlevel%
    echo.
    echo Possible solutions:
    echo 1. Check if all required files are present
    echo 2. Ensure Python dependencies are installed
    echo 3. Try running as administrator
    echo 4. Check the error messages above
)
echo ================================================

pause
exit /b %errorlevel%
