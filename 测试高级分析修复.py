#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级分析修复 - 验证高级分析功能是否正常工作
"""

from prediction_engine_adapter import PredictionEngineAdapter
import traceback

def test_advanced_analysis():
    """测试高级分析功能"""
    print("🧪 测试高级分析功能修复")
    print("=" * 50)
    
    try:
        # 初始化适配器
        print("1️⃣ 初始化适配器...")
        adapter = PredictionEngineAdapter()
        print("✅ 适配器初始化成功")
        
        # 测试高级生肖分析
        print("\n2️⃣ 测试高级生肖分析...")
        analysis = adapter.get_advanced_zodiac_analysis()
        
        if 'error' in analysis:
            print(f"❌ 高级分析失败: {analysis['error']}")
            return False
        else:
            print("✅ 高级分析成功")
            print(f"   候选组合: {len(analysis.get('candidates', []))}个")
            print(f"   推荐组合: {len(analysis.get('top_recommendations', []))}个")
            
            # 显示前3个候选组合
            candidates = analysis.get('candidates', [])
            if candidates:
                print("\n   前3个候选组合:")
                for i, candidate in enumerate(candidates[:3], 1):
                    print(f"     {i}. 组合: {candidate.get('members', [])}")
                    print(f"        Z-Score: {candidate.get('z_score', 0)}")
                    print(f"        紧急度: {candidate.get('urgency_level', 'N/A')}")
        
        # 测试高级报告生成
        print("\n3️⃣ 测试高级报告生成...")
        report = adapter.generate_advanced_report()
        
        if "报告生成失败" in report:
            print(f"❌ 报告生成失败")
            print(f"错误详情: {report}")
            return False
        else:
            print("✅ 报告生成成功")
            print(f"   报告长度: {len(report)}字符")
            print(f"   报告预览:")
            print("   " + "\n   ".join(report.split('\n')[:10]))
        
        # 测试基本预测功能
        print("\n4️⃣ 测试基本预测功能...")
        result = adapter.run_prediction("2025210")
        print("✅ 预测功能正常")
        print(f"   推荐号码: {result.final_numbers}")
        print(f"   置信度: {result.confidence_score:.2%}")
        
        print(f"\n🎉 所有测试通过！高级分析功能已修复")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False

def test_gui_compatibility():
    """测试GUI兼容性"""
    print(f"\n🔧 测试GUI兼容性...")
    
    try:
        adapter = PredictionEngineAdapter()
        
        # 测试GUI可能调用的方法
        methods_to_test = [
            'get_advanced_zodiac_analysis',
            'generate_advanced_report',
            'run_prediction',
            'get_prediction_summary'
        ]
        
        for method_name in methods_to_test:
            print(f"   测试 {method_name}...")
            method = getattr(adapter, method_name)
            
            if method_name == 'run_prediction':
                result = method("2025210")
                assert hasattr(result, 'final_numbers'), f"{method_name}返回结果缺少final_numbers"
                assert hasattr(result, 'confidence_score'), f"{method_name}返回结果缺少confidence_score"
            elif method_name == 'get_advanced_zodiac_analysis':
                result = method()
                assert isinstance(result, dict), f"{method_name}应返回字典"
                if 'error' not in result:
                    assert 'candidates' in result, f"{method_name}返回结果缺少candidates"
            elif method_name == 'generate_advanced_report':
                result = method()
                assert isinstance(result, str), f"{method_name}应返回字符串"
                assert len(result) > 100, f"{method_name}返回的报告太短"
            elif method_name == 'get_prediction_summary':
                result = method()
                assert isinstance(result, dict), f"{method_name}应返回字典"
                assert 'system_status' in result, f"{method_name}返回结果缺少system_status"
            
            print(f"   ✅ {method_name} 测试通过")
        
        print("✅ GUI兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 启动高级分析修复验证...")
    
    # 运行测试
    test1_result = test_advanced_analysis()
    test2_result = test_gui_compatibility()
    
    print(f"\n{'='*60}")
    print(f"📋 测试结果总结")
    print(f"{'='*60}")
    print(f"高级分析功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"GUI兼容性测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 所有测试通过！")
        print(f"✅ 高级分析功能已修复")
        print(f"✅ GUI可以正常使用高级分析")
        print(f"✅ 错误 'unsupported format string passed to dict.format' 已解决")
        print(f"\n💡 现在可以安全使用 start_lottery_gui.bat 启动GUI")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步修复")
    
    return test1_result and test2_result

if __name__ == "__main__":
    main()
