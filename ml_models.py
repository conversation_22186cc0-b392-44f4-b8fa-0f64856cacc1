#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型层
支持多种ML算法的训练和预测
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
import joblib
import warnings
warnings.filterwarnings('ignore')

@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    model_type: str
    params: Dict[str, Any]
    feature_columns: List[str]
    target_column: str
    enabled: bool = True

@dataclass
class ModelResult:
    """模型预测结果"""
    model_name: str
    predicted_numbers: List[int]
    probabilities: Dict[int, float]
    confidence: float
    feature_importance: Optional[Dict[str, float]] = None

class FeatureEngineer:
    """特征工程器"""
    
    def __init__(self):
        self.scalers = {}
        self.encoders = {}
        self.pca = None
    
    def create_features(self, history_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """创建特征"""
        df = pd.DataFrame(history_data)
        
        # 基础特征
        df['special_code'] = pd.to_numeric(df['special_code'], errors='coerce')
        df['draw_date'] = pd.to_datetime(df['draw_date'], errors='coerce')
        
        # 时间特征
        df['year'] = df['draw_date'].dt.year
        df['month'] = df['draw_date'].dt.month
        df['day'] = df['draw_date'].dt.day
        df['weekday'] = df['draw_date'].dt.weekday
        df['quarter'] = df['draw_date'].dt.quarter
        
        # 号码特征
        df['is_big'] = (df['special_code'] >= 25).astype(int)
        df['is_odd'] = (df['special_code'] % 2).astype(int)
        df['tail_number'] = df['special_code'] % 10
        df['sum_digits'] = df['special_code'].apply(lambda x: sum(int(d) for d in str(x)) if pd.notna(x) else 0)
        
        # 历史统计特征
        df = self._add_historical_features(df)
        
        # 生肖和五行编码
        if 'zodiac' in df.columns:
            df['zodiac_encoded'] = self._encode_categorical(df['zodiac'], 'zodiac')
        
        if 'five_element' in df.columns:
            df['element_encoded'] = self._encode_categorical(df['five_element'], 'element')
        
        # 波色编码
        if 'wave_color' in df.columns:
            df['wave_encoded'] = self._encode_categorical(df['wave_color'], 'wave')
        
        return df.fillna(0)
    
    def _add_historical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加历史统计特征"""
        df = df.sort_values('draw_date').reset_index(drop=True)
        
        # 滑动窗口特征
        windows = [5, 10, 20]
        for window in windows:
            # 最近N期的平均值
            df[f'avg_{window}'] = df['special_code'].rolling(window=window, min_periods=1).mean()
            
            # 最近N期的标准差
            df[f'std_{window}'] = df['special_code'].rolling(window=window, min_periods=1).std()
            
            # 最近N期大号比例
            df[f'big_ratio_{window}'] = df['is_big'].rolling(window=window, min_periods=1).mean()
            
            # 最近N期单号比例
            df[f'odd_ratio_{window}'] = df['is_odd'].rolling(window=window, min_periods=1).mean()
        
        # 间隔特征
        df['days_since_last'] = df['draw_date'].diff().dt.days.fillna(0)
        
        # 趋势特征
        df['trend_5'] = df['special_code'].rolling(5).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)
        df['trend_10'] = df['special_code'].rolling(10).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)
        
        return df
    
    def _encode_categorical(self, series: pd.Series, name: str) -> pd.Series:
        """编码分类变量"""
        if name not in self.encoders:
            self.encoders[name] = LabelEncoder()
            return pd.Series(self.encoders[name].fit_transform(series.fillna('unknown')))
        else:
            return pd.Series(self.encoders[name].transform(series.fillna('unknown')))
    
    def scale_features(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """标准化特征"""
        scaled_df = df.copy()
        
        for col in feature_columns:
            if col in df.columns:
                if col not in self.scalers:
                    self.scalers[col] = StandardScaler()
                    scaled_df[col] = self.scalers[col].fit_transform(df[[col]])
                else:
                    scaled_df[col] = self.scalers[col].transform(df[[col]])
        
        return scaled_df
    
    def apply_pca(self, df: pd.DataFrame, feature_columns: List[str], n_components: int = 10) -> pd.DataFrame:
        """应用PCA降维"""
        if self.pca is None:
            self.pca = PCA(n_components=n_components)
            pca_features = self.pca.fit_transform(df[feature_columns])
        else:
            pca_features = self.pca.transform(df[feature_columns])
        
        # 创建PCA特征列
        pca_df = df.copy()
        for i in range(n_components):
            pca_df[f'pca_{i}'] = pca_features[:, i]
        
        return pca_df

class MLModelManager:
    """机器学习模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.feature_engineer = FeatureEngineer()
        self.model_configs = self._get_default_configs()
        self.trained_models = {}
    
    def _get_default_configs(self) -> List[ModelConfig]:
        """获取默认模型配置"""
        return [
            ModelConfig(
                name='random_forest',
                model_type='RandomForest',
                params={'n_estimators': 100, 'max_depth': 10, 'random_state': 42},
                feature_columns=['is_big', 'is_odd', 'tail_number', 'sum_digits', 'month', 'weekday'],
                target_column='special_code'
            ),
            ModelConfig(
                name='gradient_boosting',
                model_type='GradientBoosting',
                params={'n_estimators': 100, 'learning_rate': 0.1, 'max_depth': 6, 'random_state': 42},
                feature_columns=['is_big', 'is_odd', 'tail_number', 'sum_digits', 'avg_5', 'std_5'],
                target_column='special_code'
            ),
            ModelConfig(
                name='knn_pca',
                model_type='KNN_PCA',
                params={'n_neighbors': 5},
                feature_columns=['is_big', 'is_odd', 'tail_number', 'sum_digits', 'month', 'weekday', 'avg_10'],
                target_column='special_code'
            ),
            ModelConfig(
                name='svm',
                model_type='SVM',
                params={'kernel': 'rbf', 'C': 1.0, 'probability': True, 'random_state': 42},
                feature_columns=['is_big', 'is_odd', 'tail_number', 'sum_digits'],
                target_column='special_code'
            )
        ]
    
    def train_models(self, history_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """训练所有模型"""
        # 特征工程
        df = self.feature_engineer.create_features(history_data)
        
        results = {}
        
        for config in self.model_configs:
            if not config.enabled:
                continue
            
            try:
                print(f"训练模型: {config.name}")
                result = self._train_single_model(df, config)
                results[config.name] = result
                print(f"模型 {config.name} 训练完成，准确率: {result['accuracy']:.4f}")
            except Exception as e:
                print(f"模型 {config.name} 训练失败: {e}")
                results[config.name] = {'error': str(e)}
        
        return results
    
    def _train_single_model(self, df: pd.DataFrame, config: ModelConfig) -> Dict[str, Any]:
        """训练单个模型"""
        # 准备数据
        available_features = [col for col in config.feature_columns if col in df.columns]
        if not available_features:
            raise ValueError(f"没有可用的特征列: {config.feature_columns}")
        
        X = df[available_features].fillna(0)
        y = df[config.target_column].fillna(0)
        
        # 特征缩放
        if config.model_type in ['SVM', 'KNN_PCA']:
            X = self.feature_engineer.scale_features(df, available_features)[available_features]
        
        # PCA降维（仅对KNN_PCA）
        if config.model_type == 'KNN_PCA':
            pca_df = self.feature_engineer.apply_pca(df, available_features, n_components=min(5, len(available_features)))
            X = pca_df[[f'pca_{i}' for i in range(min(5, len(available_features)))]]
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 创建模型
        if config.model_type == 'RandomForest':
            model = RandomForestClassifier(**config.params)
        elif config.model_type == 'GradientBoosting':
            model = GradientBoostingClassifier(**config.params)
        elif config.model_type == 'KNN_PCA':
            model = KNeighborsClassifier(**config.params)
        elif config.model_type == 'SVM':
            model = SVC(**config.params)
        else:
            raise ValueError(f"不支持的模型类型: {config.model_type}")
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # 交叉验证
        cv_scores = cross_val_score(model, X, y, cv=5)
        
        # 保存模型
        self.trained_models[config.name] = {
            'model': model,
            'config': config,
            'feature_columns': available_features
        }
        
        # 特征重要性（如果支持）
        feature_importance = None
        if hasattr(model, 'feature_importances_'):
            feature_importance = dict(zip(available_features, model.feature_importances_))
        
        return {
            'accuracy': accuracy,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'feature_importance': feature_importance,
            'n_features': len(available_features),
            'n_samples': len(X)
        }
    
    def predict_with_models(self, latest_data: Dict[str, Any], top_n: int = 12) -> List[ModelResult]:
        """使用所有训练好的模型进行预测"""
        results = []
        
        # 创建预测用的DataFrame
        pred_df = self.feature_engineer.create_features([latest_data])
        
        for model_name, model_info in self.trained_models.items():
            try:
                model = model_info['model']
                config = model_info['config']
                feature_columns = model_info['feature_columns']
                
                # 准备特征
                X_pred = pred_df[feature_columns].fillna(0)
                
                # 特征缩放
                if config.model_type in ['SVM', 'KNN_PCA']:
                    X_pred = self.feature_engineer.scale_features(pred_df, feature_columns)[feature_columns]
                
                # PCA降维
                if config.model_type == 'KNN_PCA':
                    pca_df = self.feature_engineer.apply_pca(pred_df, feature_columns, n_components=min(5, len(feature_columns)))
                    X_pred = pca_df[[f'pca_{i}' for i in range(min(5, len(feature_columns)))]]
                
                # 预测
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(X_pred)[0]
                    classes = model.classes_
                    
                    # 获取概率最高的号码
                    prob_dict = dict(zip(classes, probabilities))
                    sorted_probs = sorted(prob_dict.items(), key=lambda x: x[1], reverse=True)
                    
                    predicted_numbers = [int(num) for num, _ in sorted_probs[:top_n]]
                    confidence = sorted_probs[0][1] if sorted_probs else 0
                else:
                    # 对于不支持概率预测的模型，使用简单预测
                    prediction = model.predict(X_pred)[0]
                    predicted_numbers = [int(prediction)]
                    prob_dict = {int(prediction): 1.0}
                    confidence = 1.0
                
                # 特征重要性
                feature_importance = None
                if hasattr(model, 'feature_importances_'):
                    feature_importance = dict(zip(feature_columns, model.feature_importances_))
                
                result = ModelResult(
                    model_name=model_name,
                    predicted_numbers=predicted_numbers,
                    probabilities=prob_dict,
                    confidence=confidence,
                    feature_importance=feature_importance
                )
                
                results.append(result)
                
            except Exception as e:
                print(f"模型 {model_name} 预测失败: {e}")
        
        return results
    
    def save_models(self, filepath: str):
        """保存训练好的模型"""
        save_data = {
            'trained_models': {},
            'feature_engineer': {
                'scalers': self.feature_engineer.scalers,
                'encoders': self.feature_engineer.encoders,
                'pca': self.feature_engineer.pca
            }
        }
        
        for name, model_info in self.trained_models.items():
            save_data['trained_models'][name] = {
                'model': model_info['model'],
                'config': model_info['config'],
                'feature_columns': model_info['feature_columns']
            }
        
        joblib.dump(save_data, filepath)
        print(f"模型已保存到: {filepath}")
    
    def load_models(self, filepath: str):
        """加载训练好的模型"""
        try:
            save_data = joblib.load(filepath)
            self.trained_models = save_data['trained_models']
            
            # 恢复特征工程器状态
            fe_data = save_data['feature_engineer']
            self.feature_engineer.scalers = fe_data['scalers']
            self.feature_engineer.encoders = fe_data['encoders']
            self.feature_engineer.pca = fe_data['pca']
            
            print(f"模型已从 {filepath} 加载")
        except Exception as e:
            print(f"加载模型失败: {e}")

if __name__ == "__main__":
    # 测试机器学习模型
    # 这里需要真实的历史数据进行测试
    print("机器学习模型管理器已创建")
    print("请使用真实的历史数据进行训练和测试")
