#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩智能预测系统 - GUI主界面 v2.1
基于tkinter的可视化管理界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import json
import os

class PredictionGUI:
    def __init__(self):
        """初始化主窗口"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("六合彩智能预测系统 v2.1")
        self.root.geometry("1024x768")

        # 初始化变量
        self.init_variables()

        # 创建界面
        self.create_ui()

        # 加载配置
        self.load_config()

    def init_variables(self):
        """初始化变量"""
        # 状态变量
        self.is_predicting = False
        self.is_analyzing = False

        # 输入变量
        self.period_var = tk.StringVar()
        self.start_period_var = tk.StringVar()
        self.end_period_var = tk.StringVar()

        # 筛选选项
        self.large_filter_var = tk.BooleanVar(value=True)
        self.medium_filter_var = tk.BooleanVar(value=True)
        self.small_filter_var = tk.BooleanVar(value=True)

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建左侧面板
        left_frame = self.create_left_panel(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建右侧面板
        right_frame = self.create_right_panel(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建底部日志面板
        self.create_log_panel()

    def create_left_panel(self, parent):
        """创建左侧面板"""
        left_frame = ttk.LabelFrame(parent, text="预测控制", padding="5")

        # 期号输入区
        period_frame = ttk.Frame(left_frame)
        period_frame.pack(fill=tk.X, pady=5)

        ttk.Label(period_frame, text="目标期号:").pack(side=tk.LEFT)
        ttk.Entry(period_frame, textvariable=self.period_var).pack(side=tk.LEFT, padx=5)
        ttk.Button(period_frame, text="开始预测",
                  command=self.start_prediction).pack(side=tk.LEFT)

        # 筛选选项区
        filter_frame = ttk.LabelFrame(left_frame, text="筛选设置")
        filter_frame.pack(fill=tk.X, pady=5)

        ttk.Checkbutton(filter_frame, text="大筛子(生肖五行)",
                       variable=self.large_filter_var).pack(anchor=tk.W)
        ttk.Checkbutton(filter_frame, text="中筛子(号码特征)",
                       variable=self.medium_filter_var).pack(anchor=tk.W)
        ttk.Checkbutton(filter_frame, text="小筛子(尾数组合)",
                       variable=self.small_filter_var).pack(anchor=tk.W)

        # 预测结果区
        result_frame = ttk.LabelFrame(left_frame, text="预测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.prediction_text = scrolledtext.ScrolledText(result_frame, height=10)
        self.prediction_text.pack(fill=tk.BOTH, expand=True)

        return left_frame

    def create_right_panel(self, parent):
        """创建右侧面板"""
        right_frame = ttk.LabelFrame(parent, text="分析工具", padding="5")

        # 回测控制区
        backtest_frame = ttk.LabelFrame(right_frame, text="回测控制")
        backtest_frame.pack(fill=tk.X, pady=5)

        # 期号范围
        range_frame = ttk.Frame(backtest_frame)
        range_frame.pack(fill=tk.X, pady=5)

        ttk.Label(range_frame, text="开始期号:").pack(side=tk.LEFT)
        ttk.Entry(range_frame, textvariable=self.start_period_var).pack(side=tk.LEFT, padx=5)
        ttk.Label(range_frame, text="结束期号:").pack(side=tk.LEFT)
        ttk.Entry(range_frame, textvariable=self.end_period_var).pack(side=tk.LEFT, padx=5)

        ttk.Button(backtest_frame, text="开始回测",
                  command=self.start_backtest).pack(pady=5)

        # 分析结果区
        analysis_frame = ttk.LabelFrame(right_frame, text="分析结果")
        analysis_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, height=10)
        self.analysis_text.pack(fill=tk.BOTH, expand=True)

        return right_frame

    def create_log_panel(self):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(self.root, text="系统日志", padding="5")
        log_frame.pack(fill=tk.X, side=tk.BOTTOM)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=6)
        self.log_text.pack(fill=tk.BOTH)

    def start_prediction(self):
        """开始预测"""
        if self.is_predicting:
            messagebox.showwarning("警告", "预测正在进行中，请稍候...")
            return

        period = self.period_var.get().strip()
        if not period:
            messagebox.showerror("错误", "请输入目标期号")
            return

        self.log_message(f"开始预测期号: {period}")
        self.prediction_text.delete(1.0, tk.END)
        self.prediction_text.insert(tk.END, "预测功能开发中...\n")

    def start_backtest(self):
        """开始回测"""
        if self.is_analyzing:
            messagebox.showwarning("警告", "回测正在进行中，请稍候...")
            return

        start = self.start_period_var.get().strip()
        end = self.end_period_var.get().strip()

        if not start or not end:
            messagebox.showerror("错误", "请输入回测期号范围")
            return

        self.log_message(f"开始回测: {start} - {end}")
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, "回测功能开发中...\n")

    def load_config(self):
        """加载配置"""
        config_file = "config.json"

        if not os.path.exists(config_file):
            self.log_message("未找到配置文件，使用默认设置")
            return

        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载筛选设置
            filters = config.get("filters", {})
            self.large_filter_var.set(filters.get("large", True))
            self.medium_filter_var.set(filters.get("medium", True))
            self.small_filter_var.set(filters.get("small", True))

            self.log_message("配置加载完成")

        except Exception as e:
            self.log_message(f"加载配置出错: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            "filters": {
                "large": self.large_filter_var.get(),
                "medium": self.medium_filter_var.get(),
                "small": self.small_filter_var.get()
            }
        }

        try:
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")

        except Exception as e:
            self.log_message(f"保存配置出错: {str(e)}")
            messagebox.showerror("错误", str(e))

    def log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    app = PredictionGUI()
    app.run()

if __name__ == "__main__":
    main()
